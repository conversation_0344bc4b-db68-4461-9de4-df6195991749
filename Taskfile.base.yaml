# https://taskfile.dev

version: '3'

vars:
  MODULE: 'risk_control_hub'
  COVERPKG: ''
  GOARCH: ''
  TEST_FLAGS: '-gcflags=all=-l'

tasks:
  test:
    deps:
      - setup:gotestsum
    cmds:
      - ./bin/gotestsum --format testname -- {{.TEST_FLAGS}} {{.CLI_ARGS}}
    env:
      GOARCH: '{{.GOARCH}}'
  test:cov:
    deps:
      - setup:gotestsum
    cmds:
      - ./bin/gotestsum --format testname -- {{.TEST_FLAGS}} -covermode=count -coverprofile=coverage.txt {{.CLI_ARGS}}
    env:
      GOARCH: '{{.GOARCH}}'
  coverage:
    cmds:
      - task: test:cov
        vars:
          CLI_ARGS: >-
            -coverpkg={{.COVERPKG}} ./... &&
            go tool cover -func coverage.txt | grep total

  lint:
    deps:
      - setup:golangci-lint
    cmds:
      - ./bin/golangci-lint run {{.CLI_ARGS}}

  lint:fast:
    cmds:
      - task: lint
        vars:
          CLI_ARGS: --fast {{.CLI_ARGS}}

  format:
    cmds:
      - task: goimports
        vars:
          CLI_ARGS: .

  goimports:
    deps:
      - setup:goimports
      - setup:goimportshack
    cmds:
      - >-
        ./bin/goimports.sh {{.CLI_ARGS}} &&
        ./bin/goimports -local '{{.MODULE}}' -w {{.CLI_ARGS}}

  smrcptr:
    deps:
      - setup:smrcptr
    cmds:
      - ./bin/smrcptr ./...

  gocover-cobertura:
    deps:
      - setup:gocover-cobertura
    cmds:
      - ./bin/gocover-cobertura {{.CLI_ARGS}}

  setup:
    deps:
      - setup:local
    cmds:
      - HOMEBREW_NO_AUTO_UPDATE=1 brew install lefthook
      - lefthook install

  setup:force:
    cmds:
      - rm -rf ./bin
      - task: setup:local

  setup:golangci-lint:
    cmds:
      - >-
        curl -SL
        https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh
        | sh -s v1.53.3
    status:
      - test -f ./bin/golangci-lint
      - ./bin/golangci-lint --version | grep -q ' 1.53.3 '
  setup:goimports:
    cmds:
      - GOBIN=`pwd`/bin go install golang.org/x/tools/cmd/goimports@v0.4.0
    status:
      - test -f ./bin/goimports
    sources:
      - ./Taskfile.base.yaml
  setup:smrcptr:
    cmds:
      - GOBIN=`pwd`/bin go install github.com/nikolaydubina/smrcptr@v1.2.1
    status:
      - test -f ./bin/smrcptr
    sources:
      - ./Taskfile.base.yaml
  setup:gotestsum:
    cmds:
      - GOBIN=`pwd`/bin go install gotest.tools/gotestsum@v1.9.0
    status:
      - test -f ./bin/gotestsum
    sources:
      - ./Taskfile.base.yaml
  setup:gocover-cobertura:
    cmds:
      - GOBIN=`pwd`/bin go install github.com/boumenot/gocover-cobertura@v1.2.0
    status:
      - test -f ./bin/gocover-cobertura
    sources:
      - ./Taskfile.base.yaml
  setup:goimportshack:
    cmds:
      - |
        cat >./bin/goimports.sh <<'EOL'
        #!/usr/bin/env sh

        if [ $# -eq 0 ]; then
          echo "usage: $0 <filename> <filename2>"
          echo "usage: $0 . # handle all files"
          exit 1
        fi

        FILES=("$@")

        if [ "$1" = "." ]; then
          echo "all go files"
          FILES=$(find . -name "*.go" | grep -v -e ".mock.go" -e ".gen.go" -e ".pb.go")
        fi

        for i in "${FILES[@]}"; do
          sed -i '' -e '
            /^import/,/)/ {
            /^$/ d
            }
          ' $i
        done

        EOL
      - chmod +x ./bin/goimports.sh
    status:
      - test -f ./bin/goimports.sh
    sources:
      - ./Taskfile.base.yaml

  setup:local:
    cmds:
      - mkdir -p ./bin
      - task: setup:golangci-lint
      - task: setup:goimports
      - task: setup:smrcptr
      - task: setup:gotestsum
      - task: setup:goimportshack
