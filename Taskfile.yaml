# https://taskfile.dev

version: '3'

vars:
  MODULE: 'aml-insight'
  COVERPKG: ''
  GOARCH: 'amd64'
  TEST_FLAGS: '-gcflags=all=-l'

includes:
  b:
    taskfile: ./Taskfile.base.yaml
    vars: # not support dynamic vars not, wait for https://github.com/go-task/task/issues/951 resolved
      MODULE: 'aml-insight'
      COVERPKG: ''
      GOARCH: 'amd64'
      TEST_FLAGS: ''

tasks:
  setup:
    cmds:
      - task: b:setup

