// Code generated by MockGen. DO NOT EDIT.
// Source: ./internal/pkg/oklink/client.go
//
// Generated by this command:
//
//	mockgen -source=./internal/pkg/oklink/client.go -destination=./internal/mock/oklink/client.mock.go -package=mockoklink
//

// Package mockoklink is a generated GoMock package.
package mockoklink

import (
	oklink "aml-insight/internal/pkg/oklink"
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockTxGetter is a mock of TxGetter interface.
type MockTxGetter struct {
	ctrl     *gomock.Controller
	recorder *MockTxGetterMockRecorder
	isgomock struct{}
}

// MockTxGetterMockRecorder is the mock recorder for MockTxGetter.
type MockTxGetterMockRecorder struct {
	mock *MockTxGetter
}

// NewMockTxGetter creates a new mock instance.
func NewMockTxGetter(ctrl *gomock.Controller) *MockTxGetter {
	mock := &MockTxGetter{ctrl: ctrl}
	mock.recorder = &MockTxGetterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTxGetter) EXPECT() *MockTxGetterMockRecorder {
	return m.recorder
}

// GetNormalTx mocks base method.
func (m *MockTxGetter) GetNormalTx(ctx context.Context, req oklink.NormalTxReq) (*oklink.NormalTxResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNormalTx", ctx, req)
	ret0, _ := ret[0].(*oklink.NormalTxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNormalTx indicates an expected call of GetNormalTx.
func (mr *MockTxGetterMockRecorder) GetNormalTx(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNormalTx", reflect.TypeOf((*MockTxGetter)(nil).GetNormalTx), ctx, req)
}

// GetTokenTx mocks base method.
func (m *MockTxGetter) GetTokenTx(ctx context.Context, req oklink.TokenTxReq) (*oklink.TokenTxResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenTx", ctx, req)
	ret0, _ := ret[0].(*oklink.TokenTxResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenTx indicates an expected call of GetTokenTx.
func (mr *MockTxGetterMockRecorder) GetTokenTx(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenTx", reflect.TypeOf((*MockTxGetter)(nil).GetTokenTx), ctx, req)
}
