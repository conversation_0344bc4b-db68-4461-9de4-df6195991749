// Code generated by MockGen. DO NOT EDIT.
// Source: code.bydev.io/cht/customer/kyc-stub.git/pkg/bybit/kyc/aml/v1 (interfaces: AmlAPIClient)
//
// Generated by this command:
//
//	mockgen -destination=./rpc/AmlAPIClient.mock.go -package=mockrpc code.bydev.io/cht/customer/kyc-stub.git/pkg/bybit/kyc/aml/v1 AmlAPIClient
//

// Package mockrpc is a generated GoMock package.
package mockrpc

import (
	context "context"
	reflect "reflect"

	v1 "code.bydev.io/cht/customer/kyc-stub.git/pkg/bybit/kyc/aml/v1"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAmlAPIClient is a mock of AmlAPIClient interface.
type MockAmlAPIClient struct {
	ctrl     *gomock.Controller
	recorder *MockAmlAPIClientMockRecorder
	isgomock struct{}
}

// MockAmlAPIClientMockRecorder is the mock recorder for MockAmlAPIClient.
type MockAmlAPIClientMockRecorder struct {
	mock *MockAmlAPIClient
}

// NewMockAmlAPIClient creates a new mock instance.
func NewMockAmlAPIClient(ctrl *gomock.Controller) *MockAmlAPIClient {
	mock := &MockAmlAPIClient{ctrl: ctrl}
	mock.recorder = &MockAmlAPIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmlAPIClient) EXPECT() *MockAmlAPIClientMockRecorder {
	return m.recorder
}

// CreateScreening mocks base method.
func (m *MockAmlAPIClient) CreateScreening(ctx context.Context, in *v1.CreateScreeningRequest, opts ...grpc.CallOption) (*v1.CreateScreeningResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateScreening", varargs...)
	ret0, _ := ret[0].(*v1.CreateScreeningResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateScreening indicates an expected call of CreateScreening.
func (mr *MockAmlAPIClientMockRecorder) CreateScreening(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateScreening", reflect.TypeOf((*MockAmlAPIClient)(nil).CreateScreening), varargs...)
}

// MoveCase mocks base method.
func (m *MockAmlAPIClient) MoveCase(ctx context.Context, in *v1.MoveCaseRequest, opts ...grpc.CallOption) (*v1.MoveCaseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MoveCase", varargs...)
	ret0, _ := ret[0].(*v1.MoveCaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MoveCase indicates an expected call of MoveCase.
func (mr *MockAmlAPIClientMockRecorder) MoveCase(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MoveCase", reflect.TypeOf((*MockAmlAPIClient)(nil).MoveCase), varargs...)
}

// QueryAMLResult mocks base method.
func (m *MockAmlAPIClient) QueryAMLResult(ctx context.Context, in *v1.QueryAMLResultRequest, opts ...grpc.CallOption) (*v1.QueryAMLResultResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryAMLResult", varargs...)
	ret0, _ := ret[0].(*v1.QueryAMLResultResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryAMLResult indicates an expected call of QueryAMLResult.
func (mr *MockAmlAPIClientMockRecorder) QueryAMLResult(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryAMLResult", reflect.TypeOf((*MockAmlAPIClient)(nil).QueryAMLResult), varargs...)
}

// QueryScreeningDecision mocks base method.
func (m *MockAmlAPIClient) QueryScreeningDecision(ctx context.Context, in *v1.QueryScreeningDecisionRequest, opts ...grpc.CallOption) (*v1.QueryScreeningDecisionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryScreeningDecision", varargs...)
	ret0, _ := ret[0].(*v1.QueryScreeningDecisionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryScreeningDecision indicates an expected call of QueryScreeningDecision.
func (mr *MockAmlAPIClientMockRecorder) QueryScreeningDecision(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryScreeningDecision", reflect.TypeOf((*MockAmlAPIClient)(nil).QueryScreeningDecision), varargs...)
}
