// Code generated by MockGen. DO NOT EDIT.
// Source: git.bybit.com/svc/stub/pkg/pb/api/kyc (interfaces: KycInternalClient)
//
// Generated by this command:
//
//	mockgen -destination=./rpc/KycInternalClient.mock.go -package=mockrpc git.bybit.com/svc/stub/pkg/pb/api/kyc KycInternalClient
//

// Package mockrpc is a generated GoMock package.
package mockrpc

import (
	context "context"
	reflect "reflect"

	kyc "git.bybit.com/svc/stub/pkg/pb/api/kyc"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockKycInternalClient is a mock of KycInternalClient interface.
type MockKycInternalClient struct {
	ctrl     *gomock.Controller
	recorder *MockKycInternalClientMockRecorder
	isgomock struct{}
}

// MockKycInternalClientMockRecorder is the mock recorder for MockKycInternalClient.
type MockKycInternalClientMockRecorder struct {
	mock *MockKycInternalClient
}

// NewMockKycInternalClient creates a new mock instance.
func NewMockKycInternalClient(ctrl *gomock.Controller) *MockKycInternalClient {
	mock := &MockKycInternalClient{ctrl: ctrl}
	mock.recorder = &MockKycInternalClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKycInternalClient) EXPECT() *MockKycInternalClientMockRecorder {
	return m.recorder
}

// CleanUpOnboardingInfo mocks base method.
func (m *MockKycInternalClient) CleanUpOnboardingInfo(ctx context.Context, in *kyc.CleanUpOnboardingInfoRequest, opts ...grpc.CallOption) (*kyc.CleanUpOnboardingInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CleanUpOnboardingInfo", varargs...)
	ret0, _ := ret[0].(*kyc.CleanUpOnboardingInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CleanUpOnboardingInfo indicates an expected call of CleanUpOnboardingInfo.
func (mr *MockKycInternalClientMockRecorder) CleanUpOnboardingInfo(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanUpOnboardingInfo", reflect.TypeOf((*MockKycInternalClient)(nil).CleanUpOnboardingInfo), varargs...)
}

// GetCompanyKYCInfo mocks base method.
func (m *MockKycInternalClient) GetCompanyKYCInfo(ctx context.Context, in *kyc.GetCompanyKYCInfoRequest, opts ...grpc.CallOption) (*kyc.GetCompanyKYCInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCompanyKYCInfo", varargs...)
	ret0, _ := ret[0].(*kyc.GetCompanyKYCInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCompanyKYCInfo indicates an expected call of GetCompanyKYCInfo.
func (mr *MockKycInternalClientMockRecorder) GetCompanyKYCInfo(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCompanyKYCInfo", reflect.TypeOf((*MockKycInternalClient)(nil).GetCompanyKYCInfo), varargs...)
}

// GetKYCSDKToken mocks base method.
func (m *MockKycInternalClient) GetKYCSDKToken(ctx context.Context, in *kyc.GetKYCSDKTokenRequest, opts ...grpc.CallOption) (*kyc.GetKYCSDKTokenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetKYCSDKToken", varargs...)
	ret0, _ := ret[0].(*kyc.GetKYCSDKTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKYCSDKToken indicates an expected call of GetKYCSDKToken.
func (mr *MockKycInternalClientMockRecorder) GetKYCSDKToken(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKYCSDKToken", reflect.TypeOf((*MockKycInternalClient)(nil).GetKYCSDKToken), varargs...)
}

// GetKycProfile mocks base method.
func (m *MockKycInternalClient) GetKycProfile(ctx context.Context, in *kyc.GetKycProfileRequest, opts ...grpc.CallOption) (*kyc.GetKycProfileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetKycProfile", varargs...)
	ret0, _ := ret[0].(*kyc.GetKycProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKycProfile indicates an expected call of GetKycProfile.
func (mr *MockKycInternalClientMockRecorder) GetKycProfile(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKycProfile", reflect.TypeOf((*MockKycInternalClient)(nil).GetKycProfile), varargs...)
}

// GetKycVendorConfig mocks base method.
func (m *MockKycInternalClient) GetKycVendorConfig(ctx context.Context, in *kyc.GetKycVendorConfigRequest, opts ...grpc.CallOption) (*kyc.GetKycVendorConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetKycVendorConfig", varargs...)
	ret0, _ := ret[0].(*kyc.GetKycVendorConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKycVendorConfig indicates an expected call of GetKycVendorConfig.
func (mr *MockKycInternalClientMockRecorder) GetKycVendorConfig(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKycVendorConfig", reflect.TypeOf((*MockKycInternalClient)(nil).GetKycVendorConfig), varargs...)
}

// GetMemberKYC mocks base method.
func (m *MockKycInternalClient) GetMemberKYC(ctx context.Context, in *kyc.GetMemberKYCRequest, opts ...grpc.CallOption) (*kyc.GetMemberKYCResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMemberKYC", varargs...)
	ret0, _ := ret[0].(*kyc.GetMemberKYCResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMemberKYC indicates an expected call of GetMemberKYC.
func (mr *MockKycInternalClientMockRecorder) GetMemberKYC(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMemberKYC", reflect.TypeOf((*MockKycInternalClient)(nil).GetMemberKYC), varargs...)
}

// GetMemberKYCList mocks base method.
func (m *MockKycInternalClient) GetMemberKYCList(ctx context.Context, in *kyc.GetMemberKYCListRequest, opts ...grpc.CallOption) (*kyc.GetMemberKYCListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMemberKYCList", varargs...)
	ret0, _ := ret[0].(*kyc.GetMemberKYCListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMemberKYCList indicates an expected call of GetMemberKYCList.
func (mr *MockKycInternalClientMockRecorder) GetMemberKYCList(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMemberKYCList", reflect.TypeOf((*MockKycInternalClient)(nil).GetMemberKYCList), varargs...)
}

// GetMemberKYCVendorToken mocks base method.
func (m *MockKycInternalClient) GetMemberKYCVendorToken(ctx context.Context, in *kyc.GetMemberKYCVendorTokenIn, opts ...grpc.CallOption) (*kyc.GetMemberKYCVendorTokenOut, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMemberKYCVendorToken", varargs...)
	ret0, _ := ret[0].(*kyc.GetMemberKYCVendorTokenOut)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMemberKYCVendorToken indicates an expected call of GetMemberKYCVendorToken.
func (mr *MockKycInternalClientMockRecorder) GetMemberKYCVendorToken(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMemberKYCVendorToken", reflect.TypeOf((*MockKycInternalClient)(nil).GetMemberKYCVendorToken), varargs...)
}

// GetNonDocVerificationApplicantData mocks base method.
func (m *MockKycInternalClient) GetNonDocVerificationApplicantData(ctx context.Context, in *kyc.GetNonDocVerificationApplicantDataRequest, opts ...grpc.CallOption) (*kyc.GetNonDocVerificationApplicantDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNonDocVerificationApplicantData", varargs...)
	ret0, _ := ret[0].(*kyc.GetNonDocVerificationApplicantDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNonDocVerificationApplicantData indicates an expected call of GetNonDocVerificationApplicantData.
func (mr *MockKycInternalClientMockRecorder) GetNonDocVerificationApplicantData(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNonDocVerificationApplicantData", reflect.TypeOf((*MockKycInternalClient)(nil).GetNonDocVerificationApplicantData), varargs...)
}

// GetNonDocVerificationResult mocks base method.
func (m *MockKycInternalClient) GetNonDocVerificationResult(ctx context.Context, in *kyc.GetNonDocVerificationResultRequest, opts ...grpc.CallOption) (*kyc.GetNonDocVerificationResultResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNonDocVerificationResult", varargs...)
	ret0, _ := ret[0].(*kyc.GetNonDocVerificationResultResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNonDocVerificationResult indicates an expected call of GetNonDocVerificationResult.
func (mr *MockKycInternalClientMockRecorder) GetNonDocVerificationResult(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNonDocVerificationResult", reflect.TypeOf((*MockKycInternalClient)(nil).GetNonDocVerificationResult), varargs...)
}

// GetPendingKycInfo mocks base method.
func (m *MockKycInternalClient) GetPendingKycInfo(ctx context.Context, in *kyc.GetPendingKycInfoRequest, opts ...grpc.CallOption) (*kyc.GetPendingKycInfoRequestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPendingKycInfo", varargs...)
	ret0, _ := ret[0].(*kyc.GetPendingKycInfoRequestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPendingKycInfo indicates an expected call of GetPendingKycInfo.
func (mr *MockKycInternalClientMockRecorder) GetPendingKycInfo(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPendingKycInfo", reflect.TypeOf((*MockKycInternalClient)(nil).GetPendingKycInfo), varargs...)
}

// GetProcessingState mocks base method.
func (m *MockKycInternalClient) GetProcessingState(ctx context.Context, in *kyc.GetProcessingStateRequest, opts ...grpc.CallOption) (*kyc.GetProcessingStateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetProcessingState", varargs...)
	ret0, _ := ret[0].(*kyc.GetProcessingStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProcessingState indicates an expected call of GetProcessingState.
func (mr *MockKycInternalClientMockRecorder) GetProcessingState(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProcessingState", reflect.TypeOf((*MockKycInternalClient)(nil).GetProcessingState), varargs...)
}

// GetRiskKycResult mocks base method.
func (m *MockKycInternalClient) GetRiskKycResult(ctx context.Context, in *kyc.GetRiskKycResultRequest, opts ...grpc.CallOption) (*kyc.GetRiskKycResultResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRiskKycResult", varargs...)
	ret0, _ := ret[0].(*kyc.GetRiskKycResultResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRiskKycResult indicates an expected call of GetRiskKycResult.
func (mr *MockKycInternalClientMockRecorder) GetRiskKycResult(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRiskKycResult", reflect.TypeOf((*MockKycInternalClient)(nil).GetRiskKycResult), varargs...)
}

// GetSharingFileURL mocks base method.
func (m *MockKycInternalClient) GetSharingFileURL(ctx context.Context, in *kyc.GetSharingFileURLRequest, opts ...grpc.CallOption) (*kyc.GetSharingFileURLResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSharingFileURL", varargs...)
	ret0, _ := ret[0].(*kyc.GetSharingFileURLResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSharingFileURL indicates an expected call of GetSharingFileURL.
func (mr *MockKycInternalClientMockRecorder) GetSharingFileURL(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSharingFileURL", reflect.TypeOf((*MockKycInternalClient)(nil).GetSharingFileURL), varargs...)
}

// SearchKYCRecord mocks base method.
func (m *MockKycInternalClient) SearchKYCRecord(ctx context.Context, in *kyc.SearchKYCRecordRequest, opts ...grpc.CallOption) (*kyc.SearchKYCRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchKYCRecord", varargs...)
	ret0, _ := ret[0].(*kyc.SearchKYCRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchKYCRecord indicates an expected call of SearchKYCRecord.
func (mr *MockKycInternalClientMockRecorder) SearchKYCRecord(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchKYCRecord", reflect.TypeOf((*MockKycInternalClient)(nil).SearchKYCRecord), varargs...)
}

// SetKYCStatus mocks base method.
func (m *MockKycInternalClient) SetKYCStatus(ctx context.Context, in *kyc.SetKYCStatusRequest, opts ...grpc.CallOption) (*kyc.SetKYCStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetKYCStatus", varargs...)
	ret0, _ := ret[0].(*kyc.SetKYCStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetKYCStatus indicates an expected call of SetKYCStatus.
func (mr *MockKycInternalClientMockRecorder) SetKYCStatus(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetKYCStatus", reflect.TypeOf((*MockKycInternalClient)(nil).SetKYCStatus), varargs...)
}

// SetKycVendorConfig mocks base method.
func (m *MockKycInternalClient) SetKycVendorConfig(ctx context.Context, in *kyc.SetKycVendorConfigRequest, opts ...grpc.CallOption) (*kyc.SetKycVendorConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetKycVendorConfig", varargs...)
	ret0, _ := ret[0].(*kyc.SetKycVendorConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetKycVendorConfig indicates an expected call of SetKycVendorConfig.
func (mr *MockKycInternalClientMockRecorder) SetKycVendorConfig(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetKycVendorConfig", reflect.TypeOf((*MockKycInternalClient)(nil).SetKycVendorConfig), varargs...)
}

// UpdateApplicantInfo mocks base method.
func (m *MockKycInternalClient) UpdateApplicantInfo(ctx context.Context, in *kyc.UpdateApplicantInfoRequest, opts ...grpc.CallOption) (*kyc.UpdateApplicantInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateApplicantInfo", varargs...)
	ret0, _ := ret[0].(*kyc.UpdateApplicantInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateApplicantInfo indicates an expected call of UpdateApplicantInfo.
func (mr *MockKycInternalClientMockRecorder) UpdateApplicantInfo(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApplicantInfo", reflect.TypeOf((*MockKycInternalClient)(nil).UpdateApplicantInfo), varargs...)
}

// VerifyDocumentIsUsed mocks base method.
func (m *MockKycInternalClient) VerifyDocumentIsUsed(ctx context.Context, in *kyc.VerifyDocumentIsUsedRequest, opts ...grpc.CallOption) (*kyc.VerifyDocumentIsUsedResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyDocumentIsUsed", varargs...)
	ret0, _ := ret[0].(*kyc.VerifyDocumentIsUsedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyDocumentIsUsed indicates an expected call of VerifyDocumentIsUsed.
func (mr *MockKycInternalClientMockRecorder) VerifyDocumentIsUsed(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyDocumentIsUsed", reflect.TypeOf((*MockKycInternalClient)(nil).VerifyDocumentIsUsed), varargs...)
}

// VerifyNonDoc mocks base method.
func (m *MockKycInternalClient) VerifyNonDoc(ctx context.Context, in *kyc.VerifyNonDocRequest, opts ...grpc.CallOption) (*kyc.VerifyNonDocResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyNonDoc", varargs...)
	ret0, _ := ret[0].(*kyc.VerifyNonDocResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyNonDoc indicates an expected call of VerifyNonDoc.
func (mr *MockKycInternalClientMockRecorder) VerifyNonDoc(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyNonDoc", reflect.TypeOf((*MockKycInternalClient)(nil).VerifyNonDoc), varargs...)
}
