package mock

//go:generate mockgen -destination=./model/model.mock.go -package=mockmodel aml-insight/internal/model RnLogModel,RnUserModel,FiatAmlTransactionModel,AddressLabelModel,AllAddressLabelModel,AddressLabelHistoryModel,AddressLabelChangeLogModel,AmlEmailTriggerModel,AmlHitCategoryModel,AmlLabelTriggerModel,EntityNameMappingModel,VendorCategoryMappingModel

//go:generate mockgen -destination=./rn/rn.mock.go -package=mockrn aml-insight/internal/pkg/rn Client

// -- 非 bufgen 的 rpc
//go:generate mockgen -destination=./rpc/KycInternalClient.mock.go -package=mockrpc git.bybit.com/svc/stub/pkg/pb/api/kyc KycInternalClient

//go:generate mockgen -destination=./rpc/AmlAPIClient.mock.go -package=mockrpc code.bydev.io/cht/customer/kyc-stub.git/pkg/bybit/kyc/aml/v1 AmlAPIClient
