// Code generated by MockGen. DO NOT EDIT.
// Source: aml-insight/internal/pkg/rn (interfaces: Client)
//
// Generated by this command:
//
//	mockgen -destination=./rn/rn.mock.go -package=mockrn aml-insight/internal/pkg/rn Client
//

// Package mockrn is a generated GoMock package.
package mockrn

import (
	rn "aml-insight/internal/pkg/rn"
	context "context"
	reflect "reflect"

	resty "github.com/go-resty/resty/v2"
	gomock "go.uber.org/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
	isgomock struct{}
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// SendReq mocks base method.
func (m *MockClient) SendReq(ctx context.Context, req *rn.Request, org *rn.OrgConfig) (*rn.Response, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendReq", ctx, req, org)
	ret0, _ := ret[0].(*rn.Response)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SendReq indicates an expected call of SendReq.
func (mr *MockClientMockRecorder) SendReq(ctx, req, org any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendReq", reflect.TypeOf((*MockClient)(nil).SendReq), ctx, req, org)
}
