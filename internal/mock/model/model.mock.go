// Code generated by MockGen. DO NOT EDIT.
// Source: aml-insight/internal/model (interfaces: RnLogModel,RnUserModel,FiatAmlTransactionModel,AddressLabelModel,AllAddressLabelModel,AddressLabelHistoryModel,AddressLabelChangeLogModel,AmlEmailTriggerModel,AmlHitCategoryModel,AmlLabelTriggerModel,EntityNameMappingModel,VendorCategoryMappingModel,NormalTransactionModel,SyncAddressLabelModel,TokenTransactionModel,AdditionalKyaScanModel,PreKyaHistoryModel,AmlCaseModel)
//
// Generated by this command:
//
//	mockgen -destination=./model/model.mock.go -package=mockmodel aml-insight/internal/model RnLogModel,RnUserModel,FiatAmlTransactionModel,AddressLabelModel,AllAddressLabelModel,AddressLabelHistoryModel,AddressLabelChangeLogModel,AmlEmailTriggerModel,AmlHitCategoryModel,AmlLabelTriggerModel,EntityNameMappingModel,VendorCategoryMappingModel,NormalTransactionModel,SyncAddressLabelModel,TokenTransactionModel,AdditionalKyaScanModel,PreKyaHistoryModel,AmlCaseModel
//

// Package mockmodel is a generated GoMock package.
package mockmodel

import (
	model "aml-insight/internal/model"
	context "context"
	sql "database/sql"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockRnLogModel is a mock of RnLogModel interface.
type MockRnLogModel struct {
	ctrl     *gomock.Controller
	recorder *MockRnLogModelMockRecorder
	isgomock struct{}
}

// MockRnLogModelMockRecorder is the mock recorder for MockRnLogModel.
type MockRnLogModelMockRecorder struct {
	mock *MockRnLogModel
}

// NewMockRnLogModel creates a new mock instance.
func NewMockRnLogModel(ctrl *gomock.Controller) *MockRnLogModel {
	mock := &MockRnLogModel{ctrl: ctrl}
	mock.recorder = &MockRnLogModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRnLogModel) EXPECT() *MockRnLogModelMockRecorder {
	return m.recorder
}

// FindOne mocks base method.
func (m *MockRnLogModel) FindOne(ctx context.Context, id uint64) (*model.RnLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.RnLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockRnLogModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockRnLogModel)(nil).FindOne), ctx, id)
}

// Insert mocks base method.
func (m *MockRnLogModel) Insert(ctx context.Context, data *model.RnLog) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockRnLogModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockRnLogModel)(nil).Insert), ctx, data)
}

// Update mocks base method.
func (m *MockRnLogModel) Update(ctx context.Context, newData *model.RnLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRnLogModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRnLogModel)(nil).Update), ctx, newData)
}

// MockRnUserModel is a mock of RnUserModel interface.
type MockRnUserModel struct {
	ctrl     *gomock.Controller
	recorder *MockRnUserModelMockRecorder
	isgomock struct{}
}

// MockRnUserModelMockRecorder is the mock recorder for MockRnUserModel.
type MockRnUserModelMockRecorder struct {
	mock *MockRnUserModel
}

// NewMockRnUserModel creates a new mock instance.
func NewMockRnUserModel(ctrl *gomock.Controller) *MockRnUserModel {
	mock := &MockRnUserModel{ctrl: ctrl}
	mock.recorder = &MockRnUserModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRnUserModel) EXPECT() *MockRnUserModelMockRecorder {
	return m.recorder
}

// FindOne mocks base method.
func (m *MockRnUserModel) FindOne(ctx context.Context, id uint64) (*model.RnUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.RnUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockRnUserModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockRnUserModel)(nil).FindOne), ctx, id)
}

// FindOneByUserId mocks base method.
func (m *MockRnUserModel) FindOneByUserId(ctx context.Context, userId uint64) (*model.RnUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOneByUserId", ctx, userId)
	ret0, _ := ret[0].(*model.RnUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneByUserId indicates an expected call of FindOneByUserId.
func (mr *MockRnUserModelMockRecorder) FindOneByUserId(ctx, userId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneByUserId", reflect.TypeOf((*MockRnUserModel)(nil).FindOneByUserId), ctx, userId)
}

// Insert mocks base method.
func (m *MockRnUserModel) Insert(ctx context.Context, data *model.RnUser) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockRnUserModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockRnUserModel)(nil).Insert), ctx, data)
}

// Update mocks base method.
func (m *MockRnUserModel) Update(ctx context.Context, newData *model.RnUser) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRnUserModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRnUserModel)(nil).Update), ctx, newData)
}

// MockFiatAmlTransactionModel is a mock of FiatAmlTransactionModel interface.
type MockFiatAmlTransactionModel struct {
	ctrl     *gomock.Controller
	recorder *MockFiatAmlTransactionModelMockRecorder
	isgomock struct{}
}

// MockFiatAmlTransactionModelMockRecorder is the mock recorder for MockFiatAmlTransactionModel.
type MockFiatAmlTransactionModelMockRecorder struct {
	mock *MockFiatAmlTransactionModel
}

// NewMockFiatAmlTransactionModel creates a new mock instance.
func NewMockFiatAmlTransactionModel(ctrl *gomock.Controller) *MockFiatAmlTransactionModel {
	mock := &MockFiatAmlTransactionModel{ctrl: ctrl}
	mock.recorder = &MockFiatAmlTransactionModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFiatAmlTransactionModel) EXPECT() *MockFiatAmlTransactionModelMockRecorder {
	return m.recorder
}

// FindListByUidAndRequestIdAndOrderId mocks base method.
func (m *MockFiatAmlTransactionModel) FindListByUidAndRequestIdAndOrderId(ctx context.Context, uid int64, transactionType, requestId, orderId string, limit, page int) ([]*model.FiatAmlTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindListByUidAndRequestIdAndOrderId", ctx, uid, transactionType, requestId, orderId, limit, page)
	ret0, _ := ret[0].([]*model.FiatAmlTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindListByUidAndRequestIdAndOrderId indicates an expected call of FindListByUidAndRequestIdAndOrderId.
func (mr *MockFiatAmlTransactionModelMockRecorder) FindListByUidAndRequestIdAndOrderId(ctx, uid, transactionType, requestId, orderId, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindListByUidAndRequestIdAndOrderId", reflect.TypeOf((*MockFiatAmlTransactionModel)(nil).FindListByUidAndRequestIdAndOrderId), ctx, uid, transactionType, requestId, orderId, limit, page)
}

// FindOne mocks base method.
func (m *MockFiatAmlTransactionModel) FindOne(ctx context.Context, id uint64) (*model.FiatAmlTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.FiatAmlTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockFiatAmlTransactionModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockFiatAmlTransactionModel)(nil).FindOne), ctx, id)
}

// FindOneByRequestId mocks base method.
func (m *MockFiatAmlTransactionModel) FindOneByRequestId(ctx context.Context, requestId string) (*model.FiatAmlTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOneByRequestId", ctx, requestId)
	ret0, _ := ret[0].(*model.FiatAmlTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneByRequestId indicates an expected call of FindOneByRequestId.
func (mr *MockFiatAmlTransactionModelMockRecorder) FindOneByRequestId(ctx, requestId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneByRequestId", reflect.TypeOf((*MockFiatAmlTransactionModel)(nil).FindOneByRequestId), ctx, requestId)
}

// Insert mocks base method.
func (m *MockFiatAmlTransactionModel) Insert(ctx context.Context, data *model.FiatAmlTransaction) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockFiatAmlTransactionModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockFiatAmlTransactionModel)(nil).Insert), ctx, data)
}

// Update mocks base method.
func (m *MockFiatAmlTransactionModel) Update(ctx context.Context, newData *model.FiatAmlTransaction) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockFiatAmlTransactionModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockFiatAmlTransactionModel)(nil).Update), ctx, newData)
}

// MockAddressLabelModel is a mock of AddressLabelModel interface.
type MockAddressLabelModel struct {
	ctrl     *gomock.Controller
	recorder *MockAddressLabelModelMockRecorder
	isgomock struct{}
}

// MockAddressLabelModelMockRecorder is the mock recorder for MockAddressLabelModel.
type MockAddressLabelModelMockRecorder struct {
	mock *MockAddressLabelModel
}

// NewMockAddressLabelModel creates a new mock instance.
func NewMockAddressLabelModel(ctrl *gomock.Controller) *MockAddressLabelModel {
	mock := &MockAddressLabelModel{ctrl: ctrl}
	mock.recorder = &MockAddressLabelModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAddressLabelModel) EXPECT() *MockAddressLabelModelMockRecorder {
	return m.recorder
}

// BatchUpdate mocks base method.
func (m *MockAddressLabelModel) BatchUpdate(ctx context.Context, labels []*model.AddressLabel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdate", ctx, labels)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdate indicates an expected call of BatchUpdate.
func (mr *MockAddressLabelModelMockRecorder) BatchUpdate(ctx, labels any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdate", reflect.TypeOf((*MockAddressLabelModel)(nil).BatchUpdate), ctx, labels)
}

// FindOne mocks base method.
func (m *MockAddressLabelModel) FindOne(ctx context.Context, id uint64) (*model.AddressLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.AddressLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockAddressLabelModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockAddressLabelModel)(nil).FindOne), ctx, id)
}

// FindOneByAddressChain mocks base method.
func (m *MockAddressLabelModel) FindOneByAddressChain(ctx context.Context, address, chain string) (*model.AddressLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOneByAddressChain", ctx, address, chain)
	ret0, _ := ret[0].(*model.AddressLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneByAddressChain indicates an expected call of FindOneByAddressChain.
func (mr *MockAddressLabelModelMockRecorder) FindOneByAddressChain(ctx, address, chain any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneByAddressChain", reflect.TypeOf((*MockAddressLabelModel)(nil).FindOneByAddressChain), ctx, address, chain)
}

// Insert mocks base method.
func (m *MockAddressLabelModel) Insert(ctx context.Context, data *model.AddressLabel) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockAddressLabelModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockAddressLabelModel)(nil).Insert), ctx, data)
}

// InsertOrUpdate mocks base method.
func (m *MockAddressLabelModel) InsertOrUpdate(ctx context.Context, data *model.AddressLabel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertOrUpdate", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertOrUpdate indicates an expected call of InsertOrUpdate.
func (mr *MockAddressLabelModelMockRecorder) InsertOrUpdate(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertOrUpdate", reflect.TypeOf((*MockAddressLabelModel)(nil).InsertOrUpdate), ctx, data)
}

// QueryAll mocks base method.
func (m *MockAddressLabelModel) QueryAll(ctx context.Context, limit, page int32) ([]*model.AddressLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryAll", ctx, limit, page)
	ret0, _ := ret[0].([]*model.AddressLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryAll indicates an expected call of QueryAll.
func (mr *MockAddressLabelModelMockRecorder) QueryAll(ctx, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryAll", reflect.TypeOf((*MockAddressLabelModel)(nil).QueryAll), ctx, limit, page)
}

// QueryIdIn mocks base method.
func (m *MockAddressLabelModel) QueryIdIn(ctx context.Context, ids []uint64, chain string, addressList []string, limit, page int32) ([]*model.AddressLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryIdIn", ctx, ids, chain, addressList, limit, page)
	ret0, _ := ret[0].([]*model.AddressLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryIdIn indicates an expected call of QueryIdIn.
func (mr *MockAddressLabelModelMockRecorder) QueryIdIn(ctx, ids, chain, addressList, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryIdIn", reflect.TypeOf((*MockAddressLabelModel)(nil).QueryIdIn), ctx, ids, chain, addressList, limit, page)
}

// Update mocks base method.
func (m *MockAddressLabelModel) Update(ctx context.Context, newData *model.AddressLabel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAddressLabelModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAddressLabelModel)(nil).Update), ctx, newData)
}

// MockAllAddressLabelModel is a mock of AllAddressLabelModel interface.
type MockAllAddressLabelModel struct {
	ctrl     *gomock.Controller
	recorder *MockAllAddressLabelModelMockRecorder
	isgomock struct{}
}

// MockAllAddressLabelModelMockRecorder is the mock recorder for MockAllAddressLabelModel.
type MockAllAddressLabelModelMockRecorder struct {
	mock *MockAllAddressLabelModel
}

// NewMockAllAddressLabelModel creates a new mock instance.
func NewMockAllAddressLabelModel(ctrl *gomock.Controller) *MockAllAddressLabelModel {
	mock := &MockAllAddressLabelModel{ctrl: ctrl}
	mock.recorder = &MockAllAddressLabelModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAllAddressLabelModel) EXPECT() *MockAllAddressLabelModelMockRecorder {
	return m.recorder
}

// BatchQuery mocks base method.
func (m *MockAllAddressLabelModel) BatchQuery(ctx context.Context, addresses []string) ([]*model.AllAddressLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchQuery", ctx, addresses)
	ret0, _ := ret[0].([]*model.AllAddressLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchQuery indicates an expected call of BatchQuery.
func (mr *MockAllAddressLabelModelMockRecorder) BatchQuery(ctx, addresses any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchQuery", reflect.TypeOf((*MockAllAddressLabelModel)(nil).BatchQuery), ctx, addresses)
}

// BatchUpdate mocks base method.
func (m *MockAllAddressLabelModel) BatchUpdate(ctx context.Context, labels []*model.AllAddressLabel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdate", ctx, labels)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdate indicates an expected call of BatchUpdate.
func (mr *MockAllAddressLabelModelMockRecorder) BatchUpdate(ctx, labels any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdate", reflect.TypeOf((*MockAllAddressLabelModel)(nil).BatchUpdate), ctx, labels)
}

// Delete mocks base method.
func (m *MockAllAddressLabelModel) Delete(ctx context.Context, address, chain string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, address, chain)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockAllAddressLabelModelMockRecorder) Delete(ctx, address, chain any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockAllAddressLabelModel)(nil).Delete), ctx, address, chain)
}

// FindOneByAddressChain mocks base method.
func (m *MockAllAddressLabelModel) FindOneByAddressChain(ctx context.Context, address, chain string) (*model.AllAddressLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOneByAddressChain", ctx, address, chain)
	ret0, _ := ret[0].(*model.AllAddressLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneByAddressChain indicates an expected call of FindOneByAddressChain.
func (mr *MockAllAddressLabelModelMockRecorder) FindOneByAddressChain(ctx, address, chain any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneByAddressChain", reflect.TypeOf((*MockAllAddressLabelModel)(nil).FindOneByAddressChain), ctx, address, chain)
}

// Insert mocks base method.
func (m *MockAllAddressLabelModel) Insert(ctx context.Context, data *model.AllAddressLabel) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockAllAddressLabelModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockAllAddressLabelModel)(nil).Insert), ctx, data)
}

// QueryAll mocks base method.
func (m *MockAllAddressLabelModel) QueryAll(ctx context.Context, limit, page int32) ([]*model.AllAddressLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryAll", ctx, limit, page)
	ret0, _ := ret[0].([]*model.AllAddressLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryAll indicates an expected call of QueryAll.
func (mr *MockAllAddressLabelModelMockRecorder) QueryAll(ctx, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryAll", reflect.TypeOf((*MockAllAddressLabelModel)(nil).QueryAll), ctx, limit, page)
}

// QueryByAddressAndChain mocks base method.
func (m *MockAllAddressLabelModel) QueryByAddressAndChain(ctx context.Context, chain string, addressList []string, limit, page int32) ([]*model.AllAddressLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryByAddressAndChain", ctx, chain, addressList, limit, page)
	ret0, _ := ret[0].([]*model.AllAddressLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByAddressAndChain indicates an expected call of QueryByAddressAndChain.
func (mr *MockAllAddressLabelModelMockRecorder) QueryByAddressAndChain(ctx, chain, addressList, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByAddressAndChain", reflect.TypeOf((*MockAllAddressLabelModel)(nil).QueryByAddressAndChain), ctx, chain, addressList, limit, page)
}

// Update mocks base method.
func (m *MockAllAddressLabelModel) Update(ctx context.Context, data *model.AllAddressLabel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAllAddressLabelModelMockRecorder) Update(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAllAddressLabelModel)(nil).Update), ctx, data)
}

// Upsert mocks base method.
func (m *MockAllAddressLabelModel) Upsert(ctx context.Context, data *model.AllAddressLabel) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, data)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upsert indicates an expected call of Upsert.
func (mr *MockAllAddressLabelModelMockRecorder) Upsert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockAllAddressLabelModel)(nil).Upsert), ctx, data)
}

// MockAddressLabelHistoryModel is a mock of AddressLabelHistoryModel interface.
type MockAddressLabelHistoryModel struct {
	ctrl     *gomock.Controller
	recorder *MockAddressLabelHistoryModelMockRecorder
	isgomock struct{}
}

// MockAddressLabelHistoryModelMockRecorder is the mock recorder for MockAddressLabelHistoryModel.
type MockAddressLabelHistoryModelMockRecorder struct {
	mock *MockAddressLabelHistoryModel
}

// NewMockAddressLabelHistoryModel creates a new mock instance.
func NewMockAddressLabelHistoryModel(ctrl *gomock.Controller) *MockAddressLabelHistoryModel {
	mock := &MockAddressLabelHistoryModel{ctrl: ctrl}
	mock.recorder = &MockAddressLabelHistoryModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAddressLabelHistoryModel) EXPECT() *MockAddressLabelHistoryModelMockRecorder {
	return m.recorder
}

// BatchInsert mocks base method.
func (m *MockAddressLabelHistoryModel) BatchInsert(ctx context.Context, data []*model.AddressLabelHistory) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchInsert", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchInsert indicates an expected call of BatchInsert.
func (mr *MockAddressLabelHistoryModelMockRecorder) BatchInsert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchInsert", reflect.TypeOf((*MockAddressLabelHistoryModel)(nil).BatchInsert), ctx, data)
}

// FindOne mocks base method.
func (m *MockAddressLabelHistoryModel) FindOne(ctx context.Context, id uint64) (*model.AddressLabelHistory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.AddressLabelHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockAddressLabelHistoryModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockAddressLabelHistoryModel)(nil).FindOne), ctx, id)
}

// Insert mocks base method.
func (m *MockAddressLabelHistoryModel) Insert(ctx context.Context, data *model.AddressLabelHistory) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockAddressLabelHistoryModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockAddressLabelHistoryModel)(nil).Insert), ctx, data)
}

// Update mocks base method.
func (m *MockAddressLabelHistoryModel) Update(ctx context.Context, newData *model.AddressLabelHistory) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAddressLabelHistoryModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAddressLabelHistoryModel)(nil).Update), ctx, newData)
}

// MockAddressLabelChangeLogModel is a mock of AddressLabelChangeLogModel interface.
type MockAddressLabelChangeLogModel struct {
	ctrl     *gomock.Controller
	recorder *MockAddressLabelChangeLogModelMockRecorder
	isgomock struct{}
}

// MockAddressLabelChangeLogModelMockRecorder is the mock recorder for MockAddressLabelChangeLogModel.
type MockAddressLabelChangeLogModelMockRecorder struct {
	mock *MockAddressLabelChangeLogModel
}

// NewMockAddressLabelChangeLogModel creates a new mock instance.
func NewMockAddressLabelChangeLogModel(ctrl *gomock.Controller) *MockAddressLabelChangeLogModel {
	mock := &MockAddressLabelChangeLogModel{ctrl: ctrl}
	mock.recorder = &MockAddressLabelChangeLogModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAddressLabelChangeLogModel) EXPECT() *MockAddressLabelChangeLogModelMockRecorder {
	return m.recorder
}

// FindOne mocks base method.
func (m *MockAddressLabelChangeLogModel) FindOne(ctx context.Context, id uint64) (*model.AddressLabelChangeLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.AddressLabelChangeLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockAddressLabelChangeLogModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockAddressLabelChangeLogModel)(nil).FindOne), ctx, id)
}

// Insert mocks base method.
func (m *MockAddressLabelChangeLogModel) Insert(ctx context.Context, data *model.AddressLabelChangeLog) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockAddressLabelChangeLogModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockAddressLabelChangeLogModel)(nil).Insert), ctx, data)
}

// Update mocks base method.
func (m *MockAddressLabelChangeLogModel) Update(ctx context.Context, newData *model.AddressLabelChangeLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAddressLabelChangeLogModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAddressLabelChangeLogModel)(nil).Update), ctx, newData)
}

// UpdateChangeLogStatus mocks base method.
func (m *MockAddressLabelChangeLogModel) UpdateChangeLogStatus(ctx context.Context, id uint64, status string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChangeLogStatus", ctx, id, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateChangeLogStatus indicates an expected call of UpdateChangeLogStatus.
func (mr *MockAddressLabelChangeLogModelMockRecorder) UpdateChangeLogStatus(ctx, id, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChangeLogStatus", reflect.TypeOf((*MockAddressLabelChangeLogModel)(nil).UpdateChangeLogStatus), ctx, id, status)
}

// MockAmlEmailTriggerModel is a mock of AmlEmailTriggerModel interface.
type MockAmlEmailTriggerModel struct {
	ctrl     *gomock.Controller
	recorder *MockAmlEmailTriggerModelMockRecorder
	isgomock struct{}
}

// MockAmlEmailTriggerModelMockRecorder is the mock recorder for MockAmlEmailTriggerModel.
type MockAmlEmailTriggerModelMockRecorder struct {
	mock *MockAmlEmailTriggerModel
}

// NewMockAmlEmailTriggerModel creates a new mock instance.
func NewMockAmlEmailTriggerModel(ctrl *gomock.Controller) *MockAmlEmailTriggerModel {
	mock := &MockAmlEmailTriggerModel{ctrl: ctrl}
	mock.recorder = &MockAmlEmailTriggerModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmlEmailTriggerModel) EXPECT() *MockAmlEmailTriggerModelMockRecorder {
	return m.recorder
}

// FindOne mocks base method.
func (m *MockAmlEmailTriggerModel) FindOne(ctx context.Context, id uint64) (*model.AmlEmailTrigger, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.AmlEmailTrigger)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockAmlEmailTriggerModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockAmlEmailTriggerModel)(nil).FindOne), ctx, id)
}

// Insert mocks base method.
func (m *MockAmlEmailTriggerModel) Insert(ctx context.Context, data *model.AmlEmailTrigger) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockAmlEmailTriggerModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockAmlEmailTriggerModel)(nil).Insert), ctx, data)
}

// QueryByLabel mocks base method.
func (m *MockAmlEmailTriggerModel) QueryByLabel(ctx context.Context, label string, limit, page int32) ([]*model.AmlEmailTrigger, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryByLabel", ctx, label, limit, page)
	ret0, _ := ret[0].([]*model.AmlEmailTrigger)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByLabel indicates an expected call of QueryByLabel.
func (mr *MockAmlEmailTriggerModelMockRecorder) QueryByLabel(ctx, label, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByLabel", reflect.TypeOf((*MockAmlEmailTriggerModel)(nil).QueryByLabel), ctx, label, limit, page)
}

// QueryByLabels mocks base method.
func (m *MockAmlEmailTriggerModel) QueryByLabels(ctx context.Context, labels []string, limit, page int32) ([]*model.AmlEmailTrigger, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryByLabels", ctx, labels, limit, page)
	ret0, _ := ret[0].([]*model.AmlEmailTrigger)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByLabels indicates an expected call of QueryByLabels.
func (mr *MockAmlEmailTriggerModelMockRecorder) QueryByLabels(ctx, labels, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByLabels", reflect.TypeOf((*MockAmlEmailTriggerModel)(nil).QueryByLabels), ctx, labels, limit, page)
}

// Update mocks base method.
func (m *MockAmlEmailTriggerModel) Update(ctx context.Context, newData *model.AmlEmailTrigger) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAmlEmailTriggerModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAmlEmailTriggerModel)(nil).Update), ctx, newData)
}

// MockAmlHitCategoryModel is a mock of AmlHitCategoryModel interface.
type MockAmlHitCategoryModel struct {
	ctrl     *gomock.Controller
	recorder *MockAmlHitCategoryModelMockRecorder
	isgomock struct{}
}

// MockAmlHitCategoryModelMockRecorder is the mock recorder for MockAmlHitCategoryModel.
type MockAmlHitCategoryModelMockRecorder struct {
	mock *MockAmlHitCategoryModel
}

// NewMockAmlHitCategoryModel creates a new mock instance.
func NewMockAmlHitCategoryModel(ctrl *gomock.Controller) *MockAmlHitCategoryModel {
	mock := &MockAmlHitCategoryModel{ctrl: ctrl}
	mock.recorder = &MockAmlHitCategoryModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmlHitCategoryModel) EXPECT() *MockAmlHitCategoryModelMockRecorder {
	return m.recorder
}

// FindOne mocks base method.
func (m *MockAmlHitCategoryModel) FindOne(ctx context.Context, id uint64) (*model.AmlHitCategory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.AmlHitCategory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockAmlHitCategoryModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockAmlHitCategoryModel)(nil).FindOne), ctx, id)
}

// Insert mocks base method.
func (m *MockAmlHitCategoryModel) Insert(ctx context.Context, data *model.AmlHitCategory) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockAmlHitCategoryModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockAmlHitCategoryModel)(nil).Insert), ctx, data)
}

// QueryByIdOrCategory mocks base method.
func (m *MockAmlHitCategoryModel) QueryByIdOrCategory(ctx context.Context, id int64, category string, limit, page int32) ([]*model.AmlHitCategory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryByIdOrCategory", ctx, id, category, limit, page)
	ret0, _ := ret[0].([]*model.AmlHitCategory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByIdOrCategory indicates an expected call of QueryByIdOrCategory.
func (mr *MockAmlHitCategoryModelMockRecorder) QueryByIdOrCategory(ctx, id, category, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByIdOrCategory", reflect.TypeOf((*MockAmlHitCategoryModel)(nil).QueryByIdOrCategory), ctx, id, category, limit, page)
}

// Update mocks base method.
func (m *MockAmlHitCategoryModel) Update(ctx context.Context, newData *model.AmlHitCategory) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAmlHitCategoryModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAmlHitCategoryModel)(nil).Update), ctx, newData)
}

// MockAmlLabelTriggerModel is a mock of AmlLabelTriggerModel interface.
type MockAmlLabelTriggerModel struct {
	ctrl     *gomock.Controller
	recorder *MockAmlLabelTriggerModelMockRecorder
	isgomock struct{}
}

// MockAmlLabelTriggerModelMockRecorder is the mock recorder for MockAmlLabelTriggerModel.
type MockAmlLabelTriggerModelMockRecorder struct {
	mock *MockAmlLabelTriggerModel
}

// NewMockAmlLabelTriggerModel creates a new mock instance.
func NewMockAmlLabelTriggerModel(ctrl *gomock.Controller) *MockAmlLabelTriggerModel {
	mock := &MockAmlLabelTriggerModel{ctrl: ctrl}
	mock.recorder = &MockAmlLabelTriggerModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmlLabelTriggerModel) EXPECT() *MockAmlLabelTriggerModelMockRecorder {
	return m.recorder
}

// FindOne mocks base method.
func (m *MockAmlLabelTriggerModel) FindOne(ctx context.Context, id uint64) (*model.AmlLabelTrigger, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.AmlLabelTrigger)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockAmlLabelTriggerModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockAmlLabelTriggerModel)(nil).FindOne), ctx, id)
}

// Insert mocks base method.
func (m *MockAmlLabelTriggerModel) Insert(ctx context.Context, data *model.AmlLabelTrigger) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockAmlLabelTriggerModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockAmlLabelTriggerModel)(nil).Insert), ctx, data)
}

// QueryByIdOrLabel mocks base method.
func (m *MockAmlLabelTriggerModel) QueryByIdOrLabel(ctx context.Context, id uint64, label string, limit, page int32) ([]*model.AmlLabelTrigger, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryByIdOrLabel", ctx, id, label, limit, page)
	ret0, _ := ret[0].([]*model.AmlLabelTrigger)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByIdOrLabel indicates an expected call of QueryByIdOrLabel.
func (mr *MockAmlLabelTriggerModelMockRecorder) QueryByIdOrLabel(ctx, id, label, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByIdOrLabel", reflect.TypeOf((*MockAmlLabelTriggerModel)(nil).QueryByIdOrLabel), ctx, id, label, limit, page)
}

// Update mocks base method.
func (m *MockAmlLabelTriggerModel) Update(ctx context.Context, newData *model.AmlLabelTrigger) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAmlLabelTriggerModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAmlLabelTriggerModel)(nil).Update), ctx, newData)
}

// MockEntityNameMappingModel is a mock of EntityNameMappingModel interface.
type MockEntityNameMappingModel struct {
	ctrl     *gomock.Controller
	recorder *MockEntityNameMappingModelMockRecorder
	isgomock struct{}
}

// MockEntityNameMappingModelMockRecorder is the mock recorder for MockEntityNameMappingModel.
type MockEntityNameMappingModelMockRecorder struct {
	mock *MockEntityNameMappingModel
}

// NewMockEntityNameMappingModel creates a new mock instance.
func NewMockEntityNameMappingModel(ctrl *gomock.Controller) *MockEntityNameMappingModel {
	mock := &MockEntityNameMappingModel{ctrl: ctrl}
	mock.recorder = &MockEntityNameMappingModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEntityNameMappingModel) EXPECT() *MockEntityNameMappingModelMockRecorder {
	return m.recorder
}

// ExistsByValue mocks base method.
func (m *MockEntityNameMappingModel) ExistsByValue(ctx context.Context, entityNameVal string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExistsByValue", ctx, entityNameVal)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExistsByValue indicates an expected call of ExistsByValue.
func (mr *MockEntityNameMappingModelMockRecorder) ExistsByValue(ctx, entityNameVal any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExistsByValue", reflect.TypeOf((*MockEntityNameMappingModel)(nil).ExistsByValue), ctx, entityNameVal)
}

// FindOne mocks base method.
func (m *MockEntityNameMappingModel) FindOne(ctx context.Context, id uint64) (*model.EntityNameMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.EntityNameMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockEntityNameMappingModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockEntityNameMappingModel)(nil).FindOne), ctx, id)
}

// FindOneByEntityNameKey mocks base method.
func (m *MockEntityNameMappingModel) FindOneByEntityNameKey(ctx context.Context, entityNameKey string) (*model.EntityNameMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOneByEntityNameKey", ctx, entityNameKey)
	ret0, _ := ret[0].(*model.EntityNameMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneByEntityNameKey indicates an expected call of FindOneByEntityNameKey.
func (mr *MockEntityNameMappingModelMockRecorder) FindOneByEntityNameKey(ctx, entityNameKey any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneByEntityNameKey", reflect.TypeOf((*MockEntityNameMappingModel)(nil).FindOneByEntityNameKey), ctx, entityNameKey)
}

// Insert mocks base method.
func (m *MockEntityNameMappingModel) Insert(ctx context.Context, data *model.EntityNameMapping) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockEntityNameMappingModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockEntityNameMappingModel)(nil).Insert), ctx, data)
}

// ListWithFilters mocks base method.
func (m *MockEntityNameMappingModel) ListWithFilters(ctx context.Context, startTime, endTime *time.Time, mappingType, entityNameKey string, hasMapping *int32, limit, page int32) ([]*model.EntityNameMapping, int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWithFilters", ctx, startTime, endTime, mappingType, entityNameKey, hasMapping, limit, page)
	ret0, _ := ret[0].([]*model.EntityNameMapping)
	ret1, _ := ret[1].(int32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListWithFilters indicates an expected call of ListWithFilters.
func (mr *MockEntityNameMappingModelMockRecorder) ListWithFilters(ctx, startTime, endTime, mappingType, entityNameKey, hasMapping, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWithFilters", reflect.TypeOf((*MockEntityNameMappingModel)(nil).ListWithFilters), ctx, startTime, endTime, mappingType, entityNameKey, hasMapping, limit, page)
}

// QueryByEntityName mocks base method.
func (m *MockEntityNameMappingModel) QueryByEntityName(ctx context.Context, id uint64, entityName string, limit, page int32) ([]*model.EntityNameMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryByEntityName", ctx, id, entityName, limit, page)
	ret0, _ := ret[0].([]*model.EntityNameMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByEntityName indicates an expected call of QueryByEntityName.
func (mr *MockEntityNameMappingModelMockRecorder) QueryByEntityName(ctx, id, entityName, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByEntityName", reflect.TypeOf((*MockEntityNameMappingModel)(nil).QueryByEntityName), ctx, id, entityName, limit, page)
}

// Update mocks base method.
func (m *MockEntityNameMappingModel) Update(ctx context.Context, newData *model.EntityNameMapping) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockEntityNameMappingModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockEntityNameMappingModel)(nil).Update), ctx, newData)
}

// MockVendorCategoryMappingModel is a mock of VendorCategoryMappingModel interface.
type MockVendorCategoryMappingModel struct {
	ctrl     *gomock.Controller
	recorder *MockVendorCategoryMappingModelMockRecorder
	isgomock struct{}
}

// MockVendorCategoryMappingModelMockRecorder is the mock recorder for MockVendorCategoryMappingModel.
type MockVendorCategoryMappingModelMockRecorder struct {
	mock *MockVendorCategoryMappingModel
}

// NewMockVendorCategoryMappingModel creates a new mock instance.
func NewMockVendorCategoryMappingModel(ctrl *gomock.Controller) *MockVendorCategoryMappingModel {
	mock := &MockVendorCategoryMappingModel{ctrl: ctrl}
	mock.recorder = &MockVendorCategoryMappingModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVendorCategoryMappingModel) EXPECT() *MockVendorCategoryMappingModelMockRecorder {
	return m.recorder
}

// FindOne mocks base method.
func (m *MockVendorCategoryMappingModel) FindOne(ctx context.Context, id uint64) (*model.VendorCategoryMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.VendorCategoryMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockVendorCategoryMappingModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockVendorCategoryMappingModel)(nil).FindOne), ctx, id)
}

// Insert mocks base method.
func (m *MockVendorCategoryMappingModel) Insert(ctx context.Context, data *model.VendorCategoryMapping) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockVendorCategoryMappingModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockVendorCategoryMappingModel)(nil).Insert), ctx, data)
}

// QueryByVendorCategoryName mocks base method.
func (m *MockVendorCategoryMappingModel) QueryByVendorCategoryName(ctx context.Context, id uint64, categoryName string, limit, page int32) ([]*model.VendorCategoryMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryByVendorCategoryName", ctx, id, categoryName, limit, page)
	ret0, _ := ret[0].([]*model.VendorCategoryMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByVendorCategoryName indicates an expected call of QueryByVendorCategoryName.
func (mr *MockVendorCategoryMappingModelMockRecorder) QueryByVendorCategoryName(ctx, id, categoryName, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByVendorCategoryName", reflect.TypeOf((*MockVendorCategoryMappingModel)(nil).QueryByVendorCategoryName), ctx, id, categoryName, limit, page)
}

// Update mocks base method.
func (m *MockVendorCategoryMappingModel) Update(ctx context.Context, newData *model.VendorCategoryMapping) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockVendorCategoryMappingModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockVendorCategoryMappingModel)(nil).Update), ctx, newData)
}

// MockNormalTransactionModel is a mock of NormalTransactionModel interface.
type MockNormalTransactionModel struct {
	ctrl     *gomock.Controller
	recorder *MockNormalTransactionModelMockRecorder
	isgomock struct{}
}

// MockNormalTransactionModelMockRecorder is the mock recorder for MockNormalTransactionModel.
type MockNormalTransactionModelMockRecorder struct {
	mock *MockNormalTransactionModel
}

// NewMockNormalTransactionModel creates a new mock instance.
func NewMockNormalTransactionModel(ctrl *gomock.Controller) *MockNormalTransactionModel {
	mock := &MockNormalTransactionModel{ctrl: ctrl}
	mock.recorder = &MockNormalTransactionModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNormalTransactionModel) EXPECT() *MockNormalTransactionModelMockRecorder {
	return m.recorder
}

// FindLastHeight mocks base method.
func (m *MockNormalTransactionModel) FindLastHeight(ctx context.Context, chain, address string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindLastHeight", ctx, chain, address)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindLastHeight indicates an expected call of FindLastHeight.
func (mr *MockNormalTransactionModelMockRecorder) FindLastHeight(ctx, chain, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindLastHeight", reflect.TypeOf((*MockNormalTransactionModel)(nil).FindLastHeight), ctx, chain, address)
}

// FindOne mocks base method.
func (m *MockNormalTransactionModel) FindOne(ctx context.Context, id uint64) (*model.NormalTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.NormalTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockNormalTransactionModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockNormalTransactionModel)(nil).FindOne), ctx, id)
}

// FindOneByTransactionHash mocks base method.
func (m *MockNormalTransactionModel) FindOneByTransactionHash(ctx context.Context, transactionHash string) (*model.NormalTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOneByTransactionHash", ctx, transactionHash)
	ret0, _ := ret[0].(*model.NormalTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneByTransactionHash indicates an expected call of FindOneByTransactionHash.
func (mr *MockNormalTransactionModelMockRecorder) FindOneByTransactionHash(ctx, transactionHash any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneByTransactionHash", reflect.TypeOf((*MockNormalTransactionModel)(nil).FindOneByTransactionHash), ctx, transactionHash)
}

// Insert mocks base method.
func (m *MockNormalTransactionModel) Insert(ctx context.Context, data *model.NormalTransaction) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockNormalTransactionModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockNormalTransactionModel)(nil).Insert), ctx, data)
}

// InsertBatch mocks base method.
func (m *MockNormalTransactionModel) InsertBatch(ctx context.Context, data []*model.NormalTransaction, opt *model.InsertBatchOption) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertBatch", ctx, data, opt)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertBatch indicates an expected call of InsertBatch.
func (mr *MockNormalTransactionModelMockRecorder) InsertBatch(ctx, data, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertBatch", reflect.TypeOf((*MockNormalTransactionModel)(nil).InsertBatch), ctx, data, opt)
}

// Update mocks base method.
func (m *MockNormalTransactionModel) Update(ctx context.Context, newData *model.NormalTransaction) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockNormalTransactionModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockNormalTransactionModel)(nil).Update), ctx, newData)
}

// MockSyncAddressLabelModel is a mock of SyncAddressLabelModel interface.
type MockSyncAddressLabelModel struct {
	ctrl     *gomock.Controller
	recorder *MockSyncAddressLabelModelMockRecorder
	isgomock struct{}
}

// MockSyncAddressLabelModelMockRecorder is the mock recorder for MockSyncAddressLabelModel.
type MockSyncAddressLabelModelMockRecorder struct {
	mock *MockSyncAddressLabelModel
}

// NewMockSyncAddressLabelModel creates a new mock instance.
func NewMockSyncAddressLabelModel(ctrl *gomock.Controller) *MockSyncAddressLabelModel {
	mock := &MockSyncAddressLabelModel{ctrl: ctrl}
	mock.recorder = &MockSyncAddressLabelModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSyncAddressLabelModel) EXPECT() *MockSyncAddressLabelModelMockRecorder {
	return m.recorder
}

// CountByChain mocks base method.
func (m *MockSyncAddressLabelModel) CountByChain(ctx context.Context, chain string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountByChain", ctx, chain)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByChain indicates an expected call of CountByChain.
func (mr *MockSyncAddressLabelModelMockRecorder) CountByChain(ctx, chain any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByChain", reflect.TypeOf((*MockSyncAddressLabelModel)(nil).CountByChain), ctx, chain)
}

// FindAllByChain mocks base method.
func (m *MockSyncAddressLabelModel) FindAllByChain(ctx context.Context, chain string) ([]*model.SyncAddressLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAllByChain", ctx, chain)
	ret0, _ := ret[0].([]*model.SyncAddressLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAllByChain indicates an expected call of FindAllByChain.
func (mr *MockSyncAddressLabelModelMockRecorder) FindAllByChain(ctx, chain any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAllByChain", reflect.TypeOf((*MockSyncAddressLabelModel)(nil).FindAllByChain), ctx, chain)
}

// FindAllChain mocks base method.
func (m *MockSyncAddressLabelModel) FindAllChain(ctx context.Context) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAllChain", ctx)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAllChain indicates an expected call of FindAllChain.
func (mr *MockSyncAddressLabelModelMockRecorder) FindAllChain(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAllChain", reflect.TypeOf((*MockSyncAddressLabelModel)(nil).FindAllChain), ctx)
}

// FindByChain mocks base method.
func (m *MockSyncAddressLabelModel) FindByChain(ctx context.Context, chain string, limit, page int32) ([]*model.SyncAddressLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByChain", ctx, chain, limit, page)
	ret0, _ := ret[0].([]*model.SyncAddressLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByChain indicates an expected call of FindByChain.
func (mr *MockSyncAddressLabelModelMockRecorder) FindByChain(ctx, chain, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByChain", reflect.TypeOf((*MockSyncAddressLabelModel)(nil).FindByChain), ctx, chain, limit, page)
}

// FindOne mocks base method.
func (m *MockSyncAddressLabelModel) FindOne(ctx context.Context, id uint64) (*model.SyncAddressLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.SyncAddressLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockSyncAddressLabelModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockSyncAddressLabelModel)(nil).FindOne), ctx, id)
}

// FindOneByAddressChainType mocks base method.
func (m *MockSyncAddressLabelModel) FindOneByAddressChainType(ctx context.Context, address, chainType string) (*model.SyncAddressLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOneByAddressChainType", ctx, address, chainType)
	ret0, _ := ret[0].(*model.SyncAddressLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneByAddressChainType indicates an expected call of FindOneByAddressChainType.
func (mr *MockSyncAddressLabelModelMockRecorder) FindOneByAddressChainType(ctx, address, chainType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneByAddressChainType", reflect.TypeOf((*MockSyncAddressLabelModel)(nil).FindOneByAddressChainType), ctx, address, chainType)
}

// Insert mocks base method.
func (m *MockSyncAddressLabelModel) Insert(ctx context.Context, data *model.SyncAddressLabel) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockSyncAddressLabelModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockSyncAddressLabelModel)(nil).Insert), ctx, data)
}

// Update mocks base method.
func (m *MockSyncAddressLabelModel) Update(ctx context.Context, newData *model.SyncAddressLabel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockSyncAddressLabelModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockSyncAddressLabelModel)(nil).Update), ctx, newData)
}

// MockTokenTransactionModel is a mock of TokenTransactionModel interface.
type MockTokenTransactionModel struct {
	ctrl     *gomock.Controller
	recorder *MockTokenTransactionModelMockRecorder
	isgomock struct{}
}

// MockTokenTransactionModelMockRecorder is the mock recorder for MockTokenTransactionModel.
type MockTokenTransactionModelMockRecorder struct {
	mock *MockTokenTransactionModel
}

// NewMockTokenTransactionModel creates a new mock instance.
func NewMockTokenTransactionModel(ctrl *gomock.Controller) *MockTokenTransactionModel {
	mock := &MockTokenTransactionModel{ctrl: ctrl}
	mock.recorder = &MockTokenTransactionModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTokenTransactionModel) EXPECT() *MockTokenTransactionModelMockRecorder {
	return m.recorder
}

// FindLastHeight mocks base method.
func (m *MockTokenTransactionModel) FindLastHeight(ctx context.Context, chain, address string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindLastHeight", ctx, chain, address)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindLastHeight indicates an expected call of FindLastHeight.
func (mr *MockTokenTransactionModelMockRecorder) FindLastHeight(ctx, chain, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindLastHeight", reflect.TypeOf((*MockTokenTransactionModel)(nil).FindLastHeight), ctx, chain, address)
}

// FindOne mocks base method.
func (m *MockTokenTransactionModel) FindOne(ctx context.Context, id uint64) (*model.TokenTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.TokenTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockTokenTransactionModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockTokenTransactionModel)(nil).FindOne), ctx, id)
}

// FindOneByTransactionHash mocks base method.
func (m *MockTokenTransactionModel) FindOneByTransactionHash(ctx context.Context, transactionHash string) (*model.TokenTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOneByTransactionHash", ctx, transactionHash)
	ret0, _ := ret[0].(*model.TokenTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneByTransactionHash indicates an expected call of FindOneByTransactionHash.
func (mr *MockTokenTransactionModelMockRecorder) FindOneByTransactionHash(ctx, transactionHash any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneByTransactionHash", reflect.TypeOf((*MockTokenTransactionModel)(nil).FindOneByTransactionHash), ctx, transactionHash)
}

// Insert mocks base method.
func (m *MockTokenTransactionModel) Insert(ctx context.Context, data *model.TokenTransaction) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockTokenTransactionModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockTokenTransactionModel)(nil).Insert), ctx, data)
}

// InsertBatch mocks base method.
func (m *MockTokenTransactionModel) InsertBatch(ctx context.Context, data []*model.TokenTransaction, opt *model.InsertBatchOption) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertBatch", ctx, data, opt)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertBatch indicates an expected call of InsertBatch.
func (mr *MockTokenTransactionModelMockRecorder) InsertBatch(ctx, data, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertBatch", reflect.TypeOf((*MockTokenTransactionModel)(nil).InsertBatch), ctx, data, opt)
}

// Update mocks base method.
func (m *MockTokenTransactionModel) Update(ctx context.Context, newData *model.TokenTransaction) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockTokenTransactionModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockTokenTransactionModel)(nil).Update), ctx, newData)
}

// MockAdditionalKyaScanModel is a mock of AdditionalKyaScanModel interface.
type MockAdditionalKyaScanModel struct {
	ctrl     *gomock.Controller
	recorder *MockAdditionalKyaScanModelMockRecorder
	isgomock struct{}
}

// MockAdditionalKyaScanModelMockRecorder is the mock recorder for MockAdditionalKyaScanModel.
type MockAdditionalKyaScanModelMockRecorder struct {
	mock *MockAdditionalKyaScanModel
}

// NewMockAdditionalKyaScanModel creates a new mock instance.
func NewMockAdditionalKyaScanModel(ctrl *gomock.Controller) *MockAdditionalKyaScanModel {
	mock := &MockAdditionalKyaScanModel{ctrl: ctrl}
	mock.recorder = &MockAdditionalKyaScanModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAdditionalKyaScanModel) EXPECT() *MockAdditionalKyaScanModelMockRecorder {
	return m.recorder
}

// FindOne mocks base method.
func (m *MockAdditionalKyaScanModel) FindOne(ctx context.Context, id uint64) (*model.AdditionalKyaScan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.AdditionalKyaScan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockAdditionalKyaScanModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockAdditionalKyaScanModel)(nil).FindOne), ctx, id)
}

// Insert mocks base method.
func (m *MockAdditionalKyaScanModel) Insert(ctx context.Context, data *model.AdditionalKyaScan) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockAdditionalKyaScanModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockAdditionalKyaScanModel)(nil).Insert), ctx, data)
}

// Update mocks base method.
func (m *MockAdditionalKyaScanModel) Update(ctx context.Context, newData *model.AdditionalKyaScan) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAdditionalKyaScanModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAdditionalKyaScanModel)(nil).Update), ctx, newData)
}

// MockPreKyaHistoryModel is a mock of PreKyaHistoryModel interface.
type MockPreKyaHistoryModel struct {
	ctrl     *gomock.Controller
	recorder *MockPreKyaHistoryModelMockRecorder
	isgomock struct{}
}

// MockPreKyaHistoryModelMockRecorder is the mock recorder for MockPreKyaHistoryModel.
type MockPreKyaHistoryModelMockRecorder struct {
	mock *MockPreKyaHistoryModel
}

// NewMockPreKyaHistoryModel creates a new mock instance.
func NewMockPreKyaHistoryModel(ctrl *gomock.Controller) *MockPreKyaHistoryModel {
	mock := &MockPreKyaHistoryModel{ctrl: ctrl}
	mock.recorder = &MockPreKyaHistoryModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPreKyaHistoryModel) EXPECT() *MockPreKyaHistoryModelMockRecorder {
	return m.recorder
}

// CountKyaHistoryResultIn mocks base method.
func (m *MockPreKyaHistoryModel) CountKyaHistoryResultIn(ctx context.Context, memberId uint64, startTime, endTime time.Time, address string, result []string) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountKyaHistoryResultIn", ctx, memberId, startTime, endTime, address, result)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountKyaHistoryResultIn indicates an expected call of CountKyaHistoryResultIn.
func (mr *MockPreKyaHistoryModelMockRecorder) CountKyaHistoryResultIn(ctx, memberId, startTime, endTime, address, result any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountKyaHistoryResultIn", reflect.TypeOf((*MockPreKyaHistoryModel)(nil).CountKyaHistoryResultIn), ctx, memberId, startTime, endTime, address, result)
}

// FindOne mocks base method.
func (m *MockPreKyaHistoryModel) FindOne(ctx context.Context, id uint64) (*model.PreKyaHistory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.PreKyaHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockPreKyaHistoryModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockPreKyaHistoryModel)(nil).FindOne), ctx, id)
}

// Insert mocks base method.
func (m *MockPreKyaHistoryModel) Insert(ctx context.Context, data *model.PreKyaHistory) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockPreKyaHistoryModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockPreKyaHistoryModel)(nil).Insert), ctx, data)
}

// QueryKyaHistoryByTime mocks base method.
func (m *MockPreKyaHistoryModel) QueryKyaHistoryByTime(ctx context.Context, memberId uint64, startTime, endTime time.Time, address, result string, limit, offset int32) ([]*model.PreKyaHistory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryKyaHistoryByTime", ctx, memberId, startTime, endTime, address, result, limit, offset)
	ret0, _ := ret[0].([]*model.PreKyaHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryKyaHistoryByTime indicates an expected call of QueryKyaHistoryByTime.
func (mr *MockPreKyaHistoryModelMockRecorder) QueryKyaHistoryByTime(ctx, memberId, startTime, endTime, address, result, limit, offset any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryKyaHistoryByTime", reflect.TypeOf((*MockPreKyaHistoryModel)(nil).QueryKyaHistoryByTime), ctx, memberId, startTime, endTime, address, result, limit, offset)
}

// Update mocks base method.
func (m *MockPreKyaHistoryModel) Update(ctx context.Context, newData *model.PreKyaHistory) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockPreKyaHistoryModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockPreKyaHistoryModel)(nil).Update), ctx, newData)
}

// MockAmlCaseModel is a mock of AmlCaseModel interface.
type MockAmlCaseModel struct {
	ctrl     *gomock.Controller
	recorder *MockAmlCaseModelMockRecorder
	isgomock struct{}
}

// MockAmlCaseModelMockRecorder is the mock recorder for MockAmlCaseModel.
type MockAmlCaseModelMockRecorder struct {
	mock *MockAmlCaseModel
}

// NewMockAmlCaseModel creates a new mock instance.
func NewMockAmlCaseModel(ctrl *gomock.Controller) *MockAmlCaseModel {
	mock := &MockAmlCaseModel{ctrl: ctrl}
	mock.recorder = &MockAmlCaseModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmlCaseModel) EXPECT() *MockAmlCaseModelMockRecorder {
	return m.recorder
}

// FindAllByMemberID mocks base method.
func (m *MockAmlCaseModel) FindAllByMemberID(ctx context.Context, memberId int64) ([]*model.AmlTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAllByMemberID", ctx, memberId)
	ret0, _ := ret[0].([]*model.AmlTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAllByMemberID indicates an expected call of FindAllByMemberID.
func (mr *MockAmlCaseModelMockRecorder) FindAllByMemberID(ctx, memberId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAllByMemberID", reflect.TypeOf((*MockAmlCaseModel)(nil).FindAllByMemberID), ctx, memberId)
}

// FindDepositAddressByMemberID mocks base method.
func (m *MockAmlCaseModel) FindDepositAddressByMemberID(ctx context.Context, memberId int64) ([]*model.MemberDepositAddress, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindDepositAddressByMemberID", ctx, memberId)
	ret0, _ := ret[0].([]*model.MemberDepositAddress)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindDepositAddressByMemberID indicates an expected call of FindDepositAddressByMemberID.
func (mr *MockAmlCaseModelMockRecorder) FindDepositAddressByMemberID(ctx, memberId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindDepositAddressByMemberID", reflect.TypeOf((*MockAmlCaseModel)(nil).FindDepositAddressByMemberID), ctx, memberId)
}

// FindFirstStrByMemberID mocks base method.
func (m *MockAmlCaseModel) FindFirstStrByMemberID(ctx context.Context, memberId int64) (*model.AmlTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindFirstStrByMemberID", ctx, memberId)
	ret0, _ := ret[0].(*model.AmlTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindFirstStrByMemberID indicates an expected call of FindFirstStrByMemberID.
func (mr *MockAmlCaseModelMockRecorder) FindFirstStrByMemberID(ctx, memberId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindFirstStrByMemberID", reflect.TypeOf((*MockAmlCaseModel)(nil).FindFirstStrByMemberID), ctx, memberId)
}

// FindListByMemberID mocks base method.
func (m *MockAmlCaseModel) FindListByMemberID(ctx context.Context, memberId int64, page, limit int) ([]*model.AmlTransaction, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindListByMemberID", ctx, memberId, page, limit)
	ret0, _ := ret[0].([]*model.AmlTransaction)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// FindListByMemberID indicates an expected call of FindListByMemberID.
func (mr *MockAmlCaseModelMockRecorder) FindListByMemberID(ctx, memberId, page, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindListByMemberID", reflect.TypeOf((*MockAmlCaseModel)(nil).FindListByMemberID), ctx, memberId, page, limit)
}

// FindListByMemberIDAndActionType mocks base method.
func (m *MockAmlCaseModel) FindListByMemberIDAndActionType(ctx context.Context, memberId int64, actionType, limit, page int) ([]*model.AmlCase, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindListByMemberIDAndActionType", ctx, memberId, actionType, limit, page)
	ret0, _ := ret[0].([]*model.AmlCase)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindListByMemberIDAndActionType indicates an expected call of FindListByMemberIDAndActionType.
func (mr *MockAmlCaseModelMockRecorder) FindListByMemberIDAndActionType(ctx, memberId, actionType, limit, page any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindListByMemberIDAndActionType", reflect.TypeOf((*MockAmlCaseModel)(nil).FindListByMemberIDAndActionType), ctx, memberId, actionType, limit, page)
}

// FindOne mocks base method.
func (m *MockAmlCaseModel) FindOne(ctx context.Context, id uint64) (*model.AmlCase, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", ctx, id)
	ret0, _ := ret[0].(*model.AmlCase)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockAmlCaseModelMockRecorder) FindOne(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockAmlCaseModel)(nil).FindOne), ctx, id)
}

// FindOneByMemberIDAndRequestID mocks base method.
func (m *MockAmlCaseModel) FindOneByMemberIDAndRequestID(ctx context.Context, memberID int64, requestID string) (*model.AmlCase, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOneByMemberIDAndRequestID", ctx, memberID, requestID)
	ret0, _ := ret[0].(*model.AmlCase)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneByMemberIDAndRequestID indicates an expected call of FindOneByMemberIDAndRequestID.
func (mr *MockAmlCaseModelMockRecorder) FindOneByMemberIDAndRequestID(ctx, memberID, requestID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneByMemberIDAndRequestID", reflect.TypeOf((*MockAmlCaseModel)(nil).FindOneByMemberIDAndRequestID), ctx, memberID, requestID)
}

// FindOneByRequestId mocks base method.
func (m *MockAmlCaseModel) FindOneByRequestId(ctx context.Context, requestId string) (*model.AmlCase, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOneByRequestId", ctx, requestId)
	ret0, _ := ret[0].(*model.AmlCase)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneByRequestId indicates an expected call of FindOneByRequestId.
func (mr *MockAmlCaseModelMockRecorder) FindOneByRequestId(ctx, requestId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneByRequestId", reflect.TypeOf((*MockAmlCaseModel)(nil).FindOneByRequestId), ctx, requestId)
}

// Insert mocks base method.
func (m *MockAmlCaseModel) Insert(ctx context.Context, data *model.AmlCase) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockAmlCaseModelMockRecorder) Insert(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockAmlCaseModel)(nil).Insert), ctx, data)
}

// InsertWithTime mocks base method.
func (m *MockAmlCaseModel) InsertWithTime(ctx context.Context, data *model.AmlCase) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertWithTime", ctx, data)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertWithTime indicates an expected call of InsertWithTime.
func (mr *MockAmlCaseModelMockRecorder) InsertWithTime(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertWithTime", reflect.TypeOf((*MockAmlCaseModel)(nil).InsertWithTime), ctx, data)
}

// Update mocks base method.
func (m *MockAmlCaseModel) Update(ctx context.Context, newData *model.AmlCase) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, newData)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAmlCaseModelMockRecorder) Update(ctx, newData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAmlCaseModel)(nil).Update), ctx, newData)
}

// UpdateTxHash mocks base method.
func (m *MockAmlCaseModel) UpdateTxHash(ctx context.Context, requestID, txHash string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTxHash", ctx, requestID, txHash)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTxHash indicates an expected call of UpdateTxHash.
func (mr *MockAmlCaseModelMockRecorder) UpdateTxHash(ctx, requestID, txHash any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTxHash", reflect.TypeOf((*MockAmlCaseModel)(nil).UpdateTxHash), ctx, requestID, txHash)
}
