package lib

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"time"

	bizconfigv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/bizconfig/v1"
	"code.bydev.io/frameworks/byone/core/collection"
	"code.bydev.io/frameworks/byone/core/logx"
	"code.bydev.io/frameworks/byone/core/mapping"
)

const (
	SecurityRecordReportSceneLists = "pay-security:risk_record:scene_list"
)

var localCache *collection.Cache

func init() {
	var err error
	localCache, err = collection.NewCache(time.Minute * 5)
	logx.Must(err)
}

func GetBizConfig[T any](ctx context.Context, bizConfigClient bizconfigv1.CustomerAPIClient, key string) (T, error) {
	var result T
	bizConfig, err := bizConfigClient.GetBizConfig(ctx, &bizconfigv1.GetBizConfigRequest{Scenario: []string{key}})
	if err != nil {
		return result, err
	}

	for _, v := range bizConfig.Result {
		if v.GetScenario() == key {
			return unmarshal[T](v.GetStrValue())
		}
	}

	return result, errors.New("not found")
}

func GetBizConfigWithCache[T any](ctx context.Context, bizConfigClient bizconfigv1.CustomerAPIClient, key string) (T, error) {
	var t T
	v, err := localCache.Take(ctx, key, func() (interface{}, error) {
		return GetBizConfig[T](ctx, bizConfigClient, key)
	})

	if err != nil {
		return t, err
	}

	if _, ok := v.(T); !ok {
		return t, fmt.Errorf("invalid cache type: %T", v)
	}
	return v.(T), nil
}

func unmarshal[T any](value string) (T, error) {
	var result T
	if value == "" {
		return result, nil
	}

	tType := reflect.TypeOf(result)
	kind := mapping.Deref(tType).Kind()

	switch kind {
	case reflect.String:
		// 直接将 value 转换为 T 类型（即 string）
		strResult := any(value).(T)
		return strResult, nil
	case reflect.Struct, reflect.Slice, reflect.Map:
		// 解析 JSON 到 result
		err := json.Unmarshal([]byte(value), &result)
		if err != nil {
			return result, fmt.Errorf("json unmarshal error: %v", err)
		}
		return result, nil
	default:
		// 不支持的类型返回错误
		return result, fmt.Errorf("unsupported type: %s", kind)
	}
}
