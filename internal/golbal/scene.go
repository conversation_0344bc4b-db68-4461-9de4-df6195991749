package golbal

type Scene string

const (
	SceneRechargeTransfer               = "RechargeTransfer"
	SceneDepositAndBuyDepositTransfer   = "DepositAndBuyDepositTransfer"
	SceneDepositAndBuyPostTransfer      = "DepositAndBuyPostTransfer"
	SceneTradeTransfer                  = "TradeTransfer"
	SceneWithdrawTransfer               = "WithdrawTransfer"
	SceneByBitCardAuth                  = "ByBitCardAuth"
	SceneByBitCardAuthReversal          = "ByBitCardAuthReversal"
	SceneByBitCardFinancial             = "ByBitCardFinancial"
	SceneByBitCardFinancialReversal     = "ByBitCardFinancialReversal"
	SceneByBitCardRefund                = "ByBitCardRefund"
	SceneByBitCardMoneySendAuth         = "ByBitCardMoneySendAuth"         // bybit card money send auth
	SceneByBitCardMoneySendAuthReversal = "ByBitCardMoneySendAuthReversal" // bybit card money send

	AMLSceneByBitCardAuth                  = "AMLByBitCardAuth"
	AMLSceneByBitCardAuthReversal          = "AMLByBitCardAuthReversal"
	AMLSceneByBitCardFinancial             = "AMLByBitCardFinancial"
	AMLSceneByBitCardFinancialReversal     = "AMLByBitCardFinancialReversal"
	AMLSceneByBitCardRefund                = "AMLByBitCardRefund"
	AMLSceneByBitCardMoneySendAuth         = "AMLByBitCardMoneySendAuth"         // bybit card money send auth
	AMLSceneByBitCardMoneySendAuthReversal = "AMLByBitCardMoneySendAuthReversal" // bybit card money send

)

const (
	RnOutComeTrigger   = "Trigger"
	RnOutComeReTrigger = "Re-Trigger"
	RnCredit           = "Credit"
	RnDebit            = "Debit"
)

var (
	AMLSceneMap = map[string]string{
		SceneByBitCardAuth:                  AMLSceneByBitCardAuth,
		SceneByBitCardAuthReversal:          AMLSceneByBitCardAuthReversal,
		SceneByBitCardFinancial:             AMLSceneByBitCardFinancial,
		SceneByBitCardFinancialReversal:     AMLSceneByBitCardFinancialReversal,
		SceneByBitCardRefund:                AMLSceneByBitCardRefund,
		SceneByBitCardMoneySendAuth:         AMLSceneByBitCardMoneySendAuth,
		SceneByBitCardMoneySendAuthReversal: AMLSceneByBitCardMoneySendAuthReversal,
	}

	SceneToRNCreditOrDebitMap = map[string]string{
		SceneRechargeTransfer:               RnCredit,
		SceneDepositAndBuyDepositTransfer:   RnCredit,
		SceneDepositAndBuyPostTransfer:      RnCredit,
		SceneTradeTransfer:                  RnCredit,
		SceneWithdrawTransfer:               RnDebit,
		SceneByBitCardAuth:                  RnDebit,
		SceneByBitCardAuthReversal:          RnDebit,
		SceneByBitCardFinancial:             RnDebit,
		SceneByBitCardFinancialReversal:     RnDebit,
		SceneByBitCardRefund:                RnDebit,
		SceneByBitCardMoneySendAuth:         RnDebit,
		SceneByBitCardMoneySendAuthReversal: RnDebit,
	}
)
