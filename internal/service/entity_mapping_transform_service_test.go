package service

import (
	"context"
	"testing"

	mockmodel "aml-insight/internal/mock/model"
	"aml-insight/internal/model"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestEntityMappingTransformService_TransformEntityName(t *testing.T) {
	mockModel := mockmodel.NewMockEntityNameMappingModel(gomock.NewController(t))
	service := NewEntityMappingTransformService(mockModel)

	tests := []struct {
		name           string
		entityName     string
		mockSetup      func()
		expectedResult string
		expectError    bool
	}{
		{
			name:       "successful entity transformation",
			entityName: "Binance",
			mockSetup: func() {
				mockModel.EXPECT().ExistsByValue(gomock.Any(), "Binance").Return(false, nil)
				mockModel.EXPECT().FindOneByEntityNameKey(gomock.Any(), "Binance").Return(&model.EntityNameMapping{
					EntityNameKey: "Binance",
					EntityNameVal: "Binance Exchange",
					MappingType:   "entity",
					HasMapping:    1,
					Valid:         1,
				}, nil)
			},
			expectedResult: "Binance Exchange",
			expectError:    false,
		},
		{
			name:       "value already exists - returns original",
			entityName: "ExistingValue",
			mockSetup: func() {
				mockModel.EXPECT().ExistsByValue(gomock.Any(), "ExistingValue").Return(true, nil)
			},
			expectedResult: "ExistingValue",
			expectError:    false,
		},
		{
			name:       "no mapping found - returns original",
			entityName: "Unknown",
			mockSetup: func() {
				mockModel.EXPECT().ExistsByValue(gomock.Any(), "Unknown").Return(false, nil)
				mockModel.EXPECT().FindOneByEntityNameKey(gomock.Any(), "Unknown").Return(nil, sqlx.ErrNotFound)
				mockModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil, nil)
			},
			expectedResult: "Unknown",
			expectError:    false,
		},
		{
			name:       "wrong mapping type - returns original",
			entityName: "TestEntity",
			mockSetup: func() {
				mockModel.EXPECT().ExistsByValue(gomock.Any(), "TestEntity").Return(false, nil)
				mockModel.EXPECT().FindOneByEntityNameKey(gomock.Any(), "TestEntity").Return(&model.EntityNameMapping{
					EntityNameKey: "TestEntity",
					EntityNameVal: "Test Entity Mapped",
					MappingType:   "source", // Different type
					HasMapping:    1,
					Valid:         1,
				}, nil)
			},
			expectedResult: "TestEntity",
			expectError:    false,
		},
		{
			name:       "disabled mapping - returns original",
			entityName: "TestEntity",
			mockSetup: func() {
				mockModel.EXPECT().ExistsByValue(gomock.Any(), "TestEntity").Return(false, nil)
				mockModel.EXPECT().FindOneByEntityNameKey(gomock.Any(), "TestEntity").Return(&model.EntityNameMapping{
					EntityNameKey: "TestEntity",
					EntityNameVal: "Test Entity Mapped",
					MappingType:   "entity",
					HasMapping:    0, // Disabled
					Valid:         1,
				}, nil)
			},
			expectedResult: "TestEntity",
			expectError:    false,
		},
		{
			name:       "invalid mapping - returns original",
			entityName: "TestEntity",
			mockSetup: func() {
				mockModel.EXPECT().ExistsByValue(gomock.Any(), "TestEntity").Return(false, nil)
				mockModel.EXPECT().FindOneByEntityNameKey(gomock.Any(), "TestEntity").Return(&model.EntityNameMapping{
					EntityNameKey: "TestEntity",
					EntityNameVal: "Test Entity Mapped",
					MappingType:   "entity",
					HasMapping:    1,
					Valid:         0, // Invalid
				}, nil)
			},
			expectedResult: "TestEntity",
			expectError:    false,
		},
		{
			name:           "empty entity name",
			entityName:     "",
			mockSetup:      func() {},
			expectedResult: "",
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock expectations
			tt.mockSetup()

			// Execute
			result, err := service.TransformEntityName(context.Background(), tt.entityName)

			// Verify
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}

func TestEntityMappingTransformService_TransformSource(t *testing.T) {
	mockModel := mockmodel.NewMockEntityNameMappingModel(gomock.NewController(t))
	service := NewEntityMappingTransformService(mockModel)

	// Setup mock data for source transformation
	mockModel.EXPECT().ExistsByValue(gomock.Any(), "chainalysis").Return(false, nil)
	mockModel.EXPECT().FindOneByEntityNameKey(gomock.Any(), "chainalysis").Return(&model.EntityNameMapping{
		EntityNameKey: "chainalysis",
		EntityNameVal: "Chainalysis",
		MappingType:   "source",
		HasMapping:    1,
		Valid:         1,
	}, nil)

	// Test source transformation
	result, err := service.TransformSource(context.Background(), "chainalysis")

	assert.NoError(t, err)
	assert.Equal(t, "Chainalysis", result)
}
