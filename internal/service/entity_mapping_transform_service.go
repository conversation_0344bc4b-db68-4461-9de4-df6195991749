package service

import (
	"context"
	"fmt"
	"strings"

	"aml-insight/internal/model"

	"code.bydev.io/frameworks/byone/core/logc"
)

type EntityMappingTransformService struct {
	entityNameMappingModel model.EntityNameMappingModel
}

func NewEntityMappingTransformService(m model.EntityNameMappingModel) *EntityMappingTransformService {
	return &EntityMappingTransformService{
		entityNameMappingModel: m,
	}
}

func (s *EntityMappingTransformService) transform(ctx context.Context, entityName, mappingType string) (string, error) {
	if len(entityName) == 0 {
		return entityName, nil
	}

	// Validate mapping type
	if mappingType != model.MappingTypeEntity && mappingType != model.MappingTypeSource {
		return entityName, fmt.Errorf("invalid mapping type: %s, must be 'entity' or 'source'", mappingType)
	}

	// Check if the entity name already exists in entity mapping value
	exist, _ := s.entityNameMappingModel.ExistsByValue(ctx, entityName)
	if exist {
		logc.Infow(ctx, "Entity name already exists, using original value",
			logc.Field("entityName", entityName),
			logc.Field("mappingType", mappingType))
		return entityName, nil
	}

	// Query for mapping using the existing cached model
	mapping, err := s.entityNameMappingModel.FindOneByEntityNameKey(ctx, entityName)
	if err != nil {
		if err == model.ErrNotFound {
			// Insert new entity name but not mapping
			if _, err_ := s.entityNameMappingModel.Insert(ctx, &model.EntityNameMapping{
				EntityNameKey: entityName,
				EntityNameVal: entityName,
				MappingType:   mappingType,
				HasMapping:    model.HasMappingNo,
				Editor:        "system",
				Remark:        "system insert",
			}); err_ != nil {
				logc.Errorw(ctx, "Failed to insert entity name mapping", logc.Field("err", err),
					logc.Field("entityName", entityName),
					logc.Field("mappingType", mappingType))
			}

			return entityName, nil
		}

		// If no mapping found, return original value (graceful fallback)
		logc.Infow(ctx, "No mapping found for entity name, using original value",
			logc.Field("entityName", entityName),
			logc.Field("mappingType", mappingType))
		return entityName, err
	}

	// Check if the mapping type matches and has a valid mapping
	if mapping.MappingType != mappingType || mapping.HasMapping == model.HasMappingNo {
		return entityName, nil
	}

	if len(mapping.EntityNameVal) > 0 {
		logc.Infow(ctx, "Transformed entity name",
			logc.Field("original", entityName),
			logc.Field("transformed", mapping.EntityNameVal),
			logc.Field("mappingType", mappingType))
		return mapping.EntityNameVal, nil
	}

	// Fallback to original
	return entityName, nil
}

func (s *EntityMappingTransformService) TransformEntityName(ctx context.Context, entityName string) (string, error) {
	return s.transform(ctx, entityName, model.MappingTypeEntity)
}

func (s *EntityMappingTransformService) TransformSource(ctx context.Context, entityName string) (string, error) {
	return s.transform(ctx, entityName, model.MappingTypeSource)
}

func (s *EntityMappingTransformService) TransformDetailName(ctx context.Context, entityName, detailName string) (string, error) {
	entityName, err := s.transform(ctx, entityName, model.MappingTypeEntity)
	if err != nil {
		return "", err
	}

	detailName = strings.ReplaceAll(detailName, entityName, "")
	return detailName, nil
}

func (s *EntityMappingTransformService) TransformEntityNameAndDetailName(ctx context.Context, entityName, detailName string) (string, string, error) {
	entityName, err := s.transform(ctx, entityName, model.MappingTypeEntity)
	if err != nil {
		return "", "", err
	}

	detailName = strings.ReplaceAll(detailName, entityName, "")
	return entityName, detailName, nil
}
