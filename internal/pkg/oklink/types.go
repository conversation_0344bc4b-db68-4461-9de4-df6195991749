package oklink

import (
	"net/url"
	"strconv"
)

const (
	MaxLimit            = 100
	DefaultProtocolType = "token_20"
)

// 基础请求参数
type BaseTxReq struct {
	ChainShortName   string `json:"chainShortName"`   // 必须，公链缩写符号
	Address          string `json:"address"`          // 必选，地址
	StartBlockHeight string `json:"startBlockHeight"` // 非必选，开始区块高度
	EndBlockHeight   string `json:"endBlockHeight"`   // 非必选，结束区块高度
	IsFromOrTo       string `json:"isFromOrTo"`       // 非必选，from，筛选from地址为查询地址的交易；to，筛选to地址为查询地址的交易；仅支持 Tron 链和 EVM 系公链
	Page             int    `json:"page"`             // 非必选，页码，默认返回第一页
	Limit            int    `json:"limit"`            // 非必选，返回条数，默认返回最近的20条，最多100条
}

// 普通交易请求
type NormalTxReq struct {
	BaseTxReq
}

// 代币交易请求
type TokenTxReq struct {
	BaseTxReq
	ProtocolType         string `json:"protocolType"`         // 必选，代币类型，20代币：token_20，721代币：token_721，1155代币：token_1155，10代币：token_10，默认为token_20
	TokenContractAddress string `json:"tokenContractAddress"` // 非必选，代币合约地址
}

// 分页信息
type PaginationData struct {
	Limit     string `json:"limit"`     // 当前页共多少条数据
	Page      string `json:"page"`      // 当前页码
	TotalPage string `json:"totalPage"` // 总共多少页
}

// 基础交易信息
type BaseTx struct {
	TxId            string `json:"txId"`            // 交易哈希
	BlockHash       string `json:"blockHash"`       // 区块哈希
	Height          string `json:"height"`          // 区块高度
	TransactionTime string `json:"transactionTime"` // 交易时间；Unix时间戳的毫秒数格式，如 1597026383085
	From            string `json:"from"`            // 交易的发起方
	To              string `json:"to"`              // 交易的接收方
	Amount          string `json:"amount"`          // 交易数量，对于UTXO系列的区块链，返回的是这个地址下这笔交易所导致的余额变动
	Symbol          string `json:"symbol"`          // 交易数量对应的币种
	IsFromContract  bool   `json:"isFromContract"`  // From地址是否是合约地址
	IsToContract    bool   `json:"isToContract"`    // To地址是否是合约地址
}

func (bt BaseTx) GetHeight() int64 {
	height, err := strconv.ParseInt(bt.Height, 10, 64)
	if err != nil {
		return 0
	}
	return height
}

func (bt BaseTx) GetTransactionTime() int64 {
	txTime, err := strconv.ParseInt(bt.TransactionTime, 10, 64)
	if err != nil {
		return 0
	}
	return txTime
}

func (bt BaseTx) GetIsFromContract() int16 {
	if bt.IsFromContract {
		return 1
	}
	return 0
}

func (bt BaseTx) GetIsToContract() int16 {
	if bt.IsToContract {
		return 1
	}
	return 0
}

// 普通交易详情
type NormalTx struct {
	BaseTx
	MethodID        string `json:"methodId"`        // 方法
	Nonce           string `json:"nonce"`           // 发起者地址发起的第几笔交易
	GasPrice        string `json:"gasPrice"`        // gas价格
	GasLimit        string `json:"gasLimit"`        // gas限制
	GasUsed         string `json:"gasUsed"`         // 实际消耗的gas
	TxFee           string `json:"txFee"`           // 交易手续费
	State           string `json:"state"`           // 交易状态，pending：待确认；success：成功；failed：失败交易状态
	TransactionType string `json:"transactionType"` // 交易类型，交易类型，0：原始交易类型，1:EIP2930，2:EIP1559，同时支持查询Tron交易类型
}

// 代币交易详情
type TokenTx struct {
	BaseTx
	TokenContractAddress string `json:"tokenContractAddress"` // 代币的合约地址
	TokenID              string `json:"tokenId"`              // NFT 的ID
}

// 基础响应
type BaseResponse struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
}

// 普通交易响应
type NormalTxResp struct {
	BaseResponse
	Data []struct {
		PaginationData
		TransactionList []NormalTx `json:"transactionList"`
	} `json:"data"`
}

// 代币交易响应
type TokenTxResp struct {
	BaseResponse
	Data []struct {
		PaginationData
		TransactionList []TokenTx `json:"transactionList"`
	} `json:"data"`
}

func (req NormalTxReq) isValid() bool {
	return req.ChainShortName != "" && req.Address != ""
}

func (req TokenTxReq) isValid() bool {
	return req.ChainShortName != "" && req.Address != "" && req.ProtocolType != ""
}

func normalTxReqToMap(req NormalTxReq) string {
	query := url.Values{}
	query.Add("chainShortName", req.ChainShortName)
	query.Add("address", req.Address)

	if req.StartBlockHeight != "" {
		query.Add("startBlockHeight", req.StartBlockHeight)
	}
	if req.EndBlockHeight != "" {
		query.Add("endBlockHeight", req.EndBlockHeight)
	}
	if req.IsFromOrTo != "" {
		query.Add("isFromOrTo", req.IsFromOrTo)
	}

	if req.Page > 0 {
		query.Add("page", strconv.Itoa(req.Page))
	}
	if req.Limit > 0 {
		query.Add("limit", strconv.Itoa(req.Limit))
	}

	return query.Encode()
}

func tokenTxReqToMap(req TokenTxReq) string {
	query := url.Values{}
	query.Add("chainShortName", req.ChainShortName)
	query.Add("address", req.Address)
	query.Add("protocolType", req.ProtocolType)

	if req.TokenContractAddress != "" {
		query.Add("tokenContractAddress", req.TokenContractAddress)
	}

	if req.StartBlockHeight != "" {
		query.Add("startBlockHeight", req.StartBlockHeight)
	}
	if req.EndBlockHeight != "" {
		query.Add("endBlockHeight", req.EndBlockHeight)
	}
	if req.IsFromOrTo != "" {
		query.Add("isFromOrTo", req.IsFromOrTo)
	}
	if req.Page > 0 {
		query.Add("page", strconv.Itoa(req.Page))
	}
	if req.Limit > 0 {
		query.Add("limit", strconv.Itoa(req.Limit))
	}

	return query.Encode()
}
