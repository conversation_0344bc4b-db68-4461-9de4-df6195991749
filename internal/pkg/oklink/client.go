package oklink

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"net/http"
	"strconv"
	"time"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/client/bhttpclient"
	"github.com/go-resty/resty/v2"
	"golang.org/x/time/rate"
)

type TxGetter interface {
	GetNormalTx(ctx context.Context, req NormalTxReq) (*NormalTxResp, error)
	GetTokenTx(ctx context.Context, req TokenTxReq) (*TokenTxResp, error)
}

type RateLimitClient struct {
	cli         *Client
	rateLimiter *rate.Limiter
}

type Client struct {
	cli *bhttpclient.Client
	cfg Config
}

type Config struct {
	bhttpclient.Config
	APIKey               string
	NormalTransactionURI string  `json:",default=/api/v5/explorer/address/normal-transaction-list"`
	TokenTransactionURI  string  `json:",default=/api/v5/explorer/address/token-transaction-list"`
	RateLimit            float64 `json:",default=20"` // 每秒请求数限制
	RateBurst            int     `json:",default=30"` // 突发请求数限制
}

func NewRateLimitClient(c Config) *RateLimitClient {
	return &RateLimitClient{
		cli:         NewClient(c),
		rateLimiter: rate.NewLimiter(rate.Limit(c.RateLimit), c.RateBurst),
	}
}

func NewClient(c Config) *Client {
	cli := bhttpclient.New(c.Config)
	cli.SetHeader("Content-Type", "application/json").
		SetHeader("Accept", "application/json").
		SetHeader("Ok-Access-Key", c.APIKey).
		SetRetryCount(5).
		SetRetryWaitTime(500 * time.Millisecond).
		SetRetryMaxWaitTime(3 * time.Second).
		AddRetryCondition(retryCondition).
		SetRetryAfter(func(client *resty.Client, resp *resty.Response) (time.Duration, error) {
			// 如果是限速错误，使用指数退避
			if resp.StatusCode() == http.StatusTooManyRequests {
				attempt := client.RetryCount + 1
				baseWait := time.Duration(math.Pow(2, float64(attempt))) * 200 * time.Millisecond
				// 添加±20%的随机抖动
				jitter := time.Duration(float64(baseWait) * (0.8 + 0.4*rand.Float64()))
				if jitter > client.RetryMaxWaitTime {
					jitter = client.RetryMaxWaitTime
				}
				if jitter > client.RetryMaxWaitTime {
					jitter = client.RetryMaxWaitTime
				}
				return jitter, nil
			}
			return client.RetryWaitTime, nil
		})

	return &Client{
		cli: cli,
		cfg: c,
	}
}

func retryCondition(response *resty.Response, err error) bool {
	// 网络错误需要重试
	if err != nil {
		return true
	}

	var baseResp BaseResponse
	if err_ := json.Unmarshal(response.Body(), &baseResp); err_ != nil {
		return false
	}

	code, err := strconv.Atoi(baseResp.Code)
	if err != nil {
		return false
	}

	// 根据HTTP状态码和业务码判断是否需要重试
	switch response.StatusCode() {
	case http.StatusOK:
		return code == BizSystemBusy || code == BizNoBlockData || code == BizTxNotParsed
	case http.StatusBadRequest:
		return code == BizTimeout
	case http.StatusUnauthorized:
		return code == BizTimestampExpired
	case http.StatusTooManyRequests:
		return code == BizRateLimitExceed
	case http.StatusInternalServerError:
		return code == BizSystemError
	case http.StatusServiceUnavailable:
		return code == BizUnavailable

	default:
		return false
	}
}

func (c *Client) GetNormalTx(ctx context.Context, req NormalTxReq) (*NormalTxResp, error) {
	if !req.isValid() {
		return nil, fmt.Errorf("invalid request: %+v", req)
	}

	resp := &NormalTxResp{}
	_, err := c.cli.R().
		SetContext(ctx).
		SetQueryString(normalTxReqToMap(req)).
		SetResult(resp).
		Get(c.cfg.NormalTransactionURI)

	return resp, err
}

func (c *Client) GetTokenTx(ctx context.Context, req TokenTxReq) (*TokenTxResp, error) {
	if !req.isValid() {
		return nil, fmt.Errorf("invalid request: %+v", req)
	}

	resp := &TokenTxResp{}
	_, err := c.cli.R().
		SetContext(ctx).
		SetQueryString(tokenTxReqToMap(req)).
		SetResult(resp).
		Get(c.cfg.TokenTransactionURI)

	return resp, err
}

func (c *RateLimitClient) GetNormalTx(ctx context.Context, req NormalTxReq) (*NormalTxResp, error) {
	if err := c.rateLimiter.Wait(ctx); err != nil {
		return nil, fmt.Errorf("rate limit: %w", err)
	}

	return c.cli.GetNormalTx(ctx, req)
}

func (c *RateLimitClient) GetTokenTx(ctx context.Context, req TokenTxReq) (*TokenTxResp, error) {
	if err := c.rateLimiter.Wait(ctx); err != nil {
		return nil, fmt.Errorf("rate limit: %w", err)
	}

	return c.cli.GetTokenTx(ctx, req)
}
