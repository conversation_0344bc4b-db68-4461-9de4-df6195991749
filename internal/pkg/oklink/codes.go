package oklink

const (
	// HTTP 200 Success responses
	BizErrCode            = 0     // http status: 200 操作成功
	BizAccountBanned      = 50007 // http status: 200 账户被封禁
	BizAccountNotExist    = 50008 // http status: 200 账户不存在
	BizAccountSuspended   = 50009 // http status: 200 账户因清算被暂停使用
	BizEmptyUserID        = 50010 // http status: 200 用户ID为空
	BizSystemBusy         = 50013 // http status: 200 当前系统繁忙，请稍后重试
	BizParamConflict      = 50024 // http status: 200 参数不能同时存在
	BizParamExceedLimit   = 50025 // http status: 200 参数传值个数超过最大限制
	BizTokenNotExist      = 50037 // http status: 200 代币不存在
	BizChainNotSupport    = 50038 // http status: 200 该公链不支持
	BizNoAlertHistory     = 50039 // http status: 200 该警报ID没有历史处理记录
	BizNoBlockData        = 50040 // http status: 200 此区块高度无数据
	BizNoTokenBalance     = 50041 // http status: 200 当前不支持该代币的历史余额查询
	BizNoTokenRiskScan    = 50042 // http status: 200 该代币不支持风险扫描
	BizAccountFrozen      = 50043 // http status: 200 账户行为触发风险控制，API功能已冻结
	BizOutputAddrNotFound = 51001 // http status: 200 填写的outputAddress地址在该交易中没有找到
	BizOutputIndexInvalid = 51002 // http status: 200 索引所指定的outputAddress在这个交易中不存在
	BizTxNotParsed        = 51003 // http status: 200 该笔交易Hash在区块链上未解析到交易信息

	// HTTP 400 Bad Request
	BizEmptyBody      = 50000 // http status: 400 body不能为空
	BizInvalidJson    = 50002 // http status: 400 非法的json数据
	BizTimeout        = 50004 // http status: 400 接口请求超时
	BizInvalidCType   = 50006 // http status: 400 无效的Content_Type，请使用"application/json"格式
	BizInvalidParam   = 50014 // http status: 400 必填参数不能为空
	BizParamBothEmpty = 50015 // http status: 400 参数不能同时为空
	BizParamMismatch  = 50016 // http status: 400 参数不匹配
	BizParamError     = 50036 // http status: 400 参数错误
	BizDataNotExist   = 50044 // http status: 400 数据不存在
	BizClientError    = 50045 // http status: 400 客户端操作错误
	BizRegionBlocked  = 50047 // http status: 400 根据法律法规，所在国家或地区无法使用服务
	BizApiFrozen      = 50100 // http status: 400 Api已被冻结，请联系客服处理
	BizHistoryExceed  = 50117 // http status: 400 超过了历史数据查询的范围

	// HTTP 401 Unauthorized
	BizTimestampExpired  = 50102 // http status: 401 请求时间戳过期
	BizAccessKeyEmpty    = 50103 // http status: 401 请求头"OK_ACCESS_KEY"不能为空
	BizPassphraseEmpty   = 50104 // http status: 401 请求头"OK_ACCESS_PASSPHRASE"不能为空
	BizPassphraseInvalid = 50105 // http status: 401 请求头"OK_ACCESS_PASSPHRASE"错误
	BizSignEmpty         = 50106 // http status: 401 请求头"OK_ACCESS_SIGN"不能为空
	BizTimestampEmpty    = 50107 // http status: 401 请求头"OK_ACCESS_TIMESTAMP"不能为空
	BizInvalidIP         = 50110 // http status: 401 无效的IP
	BizInvalidAccessKey  = 50111 // http status: 401 无效的OK_ACCESS_KEY
	BizInvalidTimestamp  = 50112 // http status: 401 无效的OK_ACCESS_TIMESTAMP
	BizInvalidSign       = 50113 // http status: 401 无效的签名
	BizInvalidAuth       = 50114 // http status: 401 无效的授权

	// HTTP 403 Forbidden
	BizNoApiAccess = 50030 // http status: 403 没有该API接口的访问权限，需要升级您的账户付费等级

	// HTTP 404 Not Found
	BizParamNotFound = 51000 // http status: 404 参数错误

	// HTTP 405 Method Not Allowed
	BizMethodNotAllowed = 50115 // http status: 405 无效的请求类型

	// HTTP 410 Gone
	BizDeprecated = 50005 // http status: 410 接口已下线或无法使用

	// HTTP 429 Too Many Requests
	BizRateLimitExceed   = 50011 // http status: 429 用户请求频率过快，超过该接口允许的限额
	BizInvalidAcctStatus = 50012 // http status: 429 账户状态无效

	// HTTP 500 Server Error
	BizSystemError = 50026 // http status: 500 系统错误

	// HTTP 503 Service Unavailable
	BizUnavailable = 50001 // http status: 503 服务暂时不可用，请稍后重试
)
