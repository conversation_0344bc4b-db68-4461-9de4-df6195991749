package oklink

import (
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNormalTransactionReqToMap(t *testing.T) {
	tests := []struct {
		name     string
		req      NormalTxReq
		expected string
	}{
		{
			name: "full request",
			req: NormalTxReq{
				BaseTxReq: BaseTxReq{
					ChainShortName:   "ETH",
					Address:          "0x123",
					StartBlockHeight: "100",
					EndBlockHeight:   "200",
					IsFromOrTo:       "from",
					Page:             1,
					Limit:            20,
				},
			},
			expected: "address=0x123&chainShortName=ETH&endBlockHeight=200&isFromOrTo=from&limit=20&page=1&startBlockHeight=100",
		},
		{
			name: "minimal request",
			req: NormalTxReq{
				BaseTxReq: BaseTxReq{
					ChainShortName: "ETH",
					Address:        "0x123",
				},
			},
			expected: "address=0x123&chainShortName=ETH",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := normalTxReqToMap(tt.req)
			// Parse both URLs to compare them regardless of parameter order
			expectedQuery, _ := url.ParseQuery(tt.expected)
			resultQuery, _ := url.ParseQuery(result)
			assert.Equal(t, expectedQuery, resultQuery)
		})
	}
}

func TestTokenTransactionReqToMap(t *testing.T) {
	tests := []struct {
		name     string
		req      TokenTxReq
		expected string
	}{
		{
			name: "full request",
			req: TokenTxReq{
				BaseTxReq: BaseTxReq{
					ChainShortName: "ETH",
					Address:        "0x123",
				},
				ProtocolType:         "token_20",
				TokenContractAddress: "0x456",
			},
			expected: "address=0x123&chainShortName=ETH&protocolType=token_20&tokenContractAddress=0x456",
		},
		{
			name: "minimal request",
			req: TokenTxReq{
				BaseTxReq: BaseTxReq{
					ChainShortName: "ETH",
					Address:        "0x123",
				},
				ProtocolType: "token_20",
			},
			expected: "address=0x123&chainShortName=ETH&protocolType=token_20",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tokenTxReqToMap(tt.req)
			// Parse both URLs to compare them regardless of parameter order
			expectedQuery, _ := url.ParseQuery(tt.expected)
			resultQuery, _ := url.ParseQuery(result)
			assert.Equal(t, expectedQuery, resultQuery)
		})
	}
}

func TestRequestValidation(t *testing.T) {
	t.Run("NormalTransactionReq validation", func(t *testing.T) {
		tests := []struct {
			name     string
			req      NormalTxReq
			expected bool
		}{
			{
				name: "valid request",
				req: NormalTxReq{
					BaseTxReq: BaseTxReq{
						ChainShortName: "ETH",
						Address:        "0x123",
					},
				},
				expected: true,
			},
			{
				name: "missing chain",
				req: NormalTxReq{
					BaseTxReq: BaseTxReq{
						Address: "0x123",
					},
				},
				expected: false,
			},
			{
				name: "missing address",
				req: NormalTxReq{
					BaseTxReq: BaseTxReq{
						ChainShortName: "ETH",
					},
				},
				expected: false,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				assert.Equal(t, tt.expected, tt.req.isValid())
			})
		}
	})

	t.Run("TokenTransactionReq validation", func(t *testing.T) {
		tests := []struct {
			name     string
			req      TokenTxReq
			expected bool
		}{
			{
				name: "valid request",
				req: TokenTxReq{
					BaseTxReq: BaseTxReq{
						ChainShortName: "ETH",
						Address:        "0x123",
					},
					ProtocolType: "token_20",
				},
				expected: true,
			},
			{
				name: "missing protocol type",
				req: TokenTxReq{
					BaseTxReq: BaseTxReq{
						ChainShortName: "ETH",
						Address:        "0x123",
					},
				},
				expected: false,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				assert.Equal(t, tt.expected, tt.req.isValid())
			})
		}
	})
}

func TestBaseTx_GetHeight(t *testing.T) {
	tests := []struct {
		name     string
		height   string
		expected int64
	}{
		{
			name:     "valid height",
			height:   "12345",
			expected: 12345,
		},
		{
			name:     "empty height",
			height:   "",
			expected: 0,
		},
		{
			name:     "invalid height",
			height:   "abc",
			expected: 0,
		},
		{
			name:     "large height",
			height:   "9223372036854775807", // max int64
			expected: 9223372036854775807,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bt := BaseTx{Height: tt.height}
			if got := bt.GetHeight(); got != tt.expected {
				t.Errorf("BaseTx.GetHeight() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestBaseTx_GetTransactionTime(t *testing.T) {
	tests := []struct {
		name     string
		txTime   string
		expected int64
	}{
		{
			name:     "valid transaction time",
			txTime:   "1597026383085",
			expected: 1597026383085,
		},
		{
			name:     "empty transaction time",
			txTime:   "",
			expected: 0,
		},
		{
			name:     "invalid transaction time",
			txTime:   "abc",
			expected: 0,
		},
		{
			name:     "large transaction time",
			txTime:   "9223372036854775807", // max int64
			expected: 9223372036854775807,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bt := BaseTx{TransactionTime: tt.txTime}
			if got := bt.GetTransactionTime(); got != tt.expected {
				t.Errorf("BaseTx.GetTransactionTime() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestBaseTx_GetIsFromContract(t *testing.T) {
	tests := []struct {
		name     string
		isFrom   bool
		expected int16
	}{
		{
			name:     "is from contract",
			isFrom:   true,
			expected: 1,
		},
		{
			name:     "is not from contract",
			isFrom:   false,
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bt := BaseTx{IsFromContract: tt.isFrom}
			if got := bt.GetIsFromContract(); got != tt.expected {
				t.Errorf("BaseTx.GetIsFromContract() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestBaseTx_GetIsToContract(t *testing.T) {
	tests := []struct {
		name     string
		isTo     bool
		expected int16
	}{
		{
			name:     "is to contract",
			isTo:     true,
			expected: 1,
		},
		{
			name:     "is not to contract",
			isTo:     false,
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bt := BaseTx{IsToContract: tt.isTo}
			if got := bt.GetIsToContract(); got != tt.expected {
				t.Errorf("BaseTx.GetIsToContract() = %v, want %v", got, tt.expected)
			}
		})
	}
}
