package oklink

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestClient(t *testing.T) {
	// Mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify request headers
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
		assert.Equal(t, "application/json", r.Header.Get("Accept"))
		assert.Equal(t, "test-api-key", r.Header.Get("Ok-Access-Key"))

		// Parse query parameters
		query := r.URL.Query()
		assert.NotEmpty(t, query.Get("chainShortName"))
		assert.NotEmpty(t, query.Get("address"))

		// Return mock response based on the endpoint
		var resp interface{}
		switch r.URL.Path {
		case "/api/v5/explorer/address/normal-transaction-list":
			resp = &NormalTxResp{
				BaseResponse: BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					PaginationData
					TransactionList []NormalTx `json:"transactionList"`
				}{},
			}
		case "/api/v5/explorer/address/token-transaction-list":
			resp = &TokenTxResp{
				BaseResponse: BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					PaginationData
					TransactionList []TokenTx `json:"transactionList"`
				}{},
			}
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(resp)
	}))
	defer server.Close()

	// Create client with mock server URL and rate limiter config
	client := NewClient(Config{
		APIKey:               "test-api-key",
		NormalTransactionURI: server.URL + "/api/v5/explorer/address/normal-transaction-list",
		TokenTransactionURI:  server.URL + "/api/v5/explorer/address/token-transaction-list",
		RateLimit:            10,
		RateBurst:            20,
	})

	t.Run("GetNormalTransaction", func(t *testing.T) {
		tests := []struct {
			name        string
			req         NormalTxReq
			expectError bool
		}{
			{
				name: "valid request",
				req: NormalTxReq{
					BaseTxReq: BaseTxReq{
						ChainShortName: "ETH",
						Address:        "0x123",
					},
				},
				expectError: false,
			},
			{
				name: "invalid request",
				req: NormalTxReq{
					BaseTxReq: BaseTxReq{
						Address: "0x123",
					},
				},
				expectError: true,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				resp, err := client.GetNormalTx(context.Background(), tt.req)
				if tt.expectError {
					assert.Error(t, err)
					assert.Nil(t, resp)
				} else {
					assert.NoError(t, err)
					if assert.NotNil(t, resp) {
						assert.Equal(t, "0", resp.Code)
						assert.Equal(t, "success", resp.Msg)
						assert.NotNil(t, resp.Data)
					}
				}
			})
		}
	})

	t.Run("GetTokenTransaction", func(t *testing.T) {
		tests := []struct {
			name        string
			req         TokenTxReq
			expectError bool
		}{
			{
				name: "valid request",
				req: TokenTxReq{
					BaseTxReq: BaseTxReq{
						ChainShortName: "ETH",
						Address:        "0x123",
					},
					ProtocolType: "token_20",
				},
				expectError: false,
			},
			{
				name: "invalid request",
				req: TokenTxReq{
					BaseTxReq: BaseTxReq{
						ChainShortName: "ETH",
						Address:        "0x123",
					},
				},
				expectError: true,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				resp, err := client.GetTokenTx(context.Background(), tt.req)
				if tt.expectError {
					assert.Error(t, err)
					assert.Nil(t, resp)
				} else {
					assert.NoError(t, err)
					if assert.NotNil(t, resp) {
						assert.Equal(t, "0", resp.Code)
						assert.Equal(t, "success", resp.Msg)
						assert.NotNil(t, resp.Data)
					}
				}
			})
		}
	})
}
