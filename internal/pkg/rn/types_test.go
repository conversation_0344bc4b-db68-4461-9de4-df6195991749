package rn

import (
	"reflect"
	"testing"
)

func TestOutcomesFiled_ConvertToOutcomes(t1 *testing.T) {
	type fields struct {
		HighRiskCustomer bool
		PEP              bool
		HighRiskCountry  bool
		IsKyb            bool
		VIP              bool
	}
	tests := []struct {
		name   string
		fields fields
		want   []*ExternalRuleOutcomes
	}{
		{
			name: "all false",
			fields: fields{
				HighRiskCustomer: false,
				PEP:              false,
				HighRiskCountry:  false,
				IsKyb:            false,
				VIP:              false,
			},
			want: []*ExternalRuleOutcomes{
				{
					Code:    OutcomesHighRiskCustomer,
					Outcome: "FALSE",
				},
				{
					Code:    OutcomesPEP,
					Outcome: "FALSE",
				},
				{
					Code:    OutcomesHighRiskCountry,
					Outcome: "FALSE",
				},
				{
					Code:    OutcomesCustomerType,
					Outcome: "Person",
				},
				{
					Code:    OutcomesVIP,
					Outcome: "FALSE",
				},
				{
					Code:    OutcomesAccountAge,
					Outcome: "0",
				},
			},
		},
		{
			name: "all true",
			fields: fields{
				HighRiskCustomer: true,
				PEP:              true,
				HighRiskCountry:  true,
				IsKyb:            true,
				VIP:              true,
			},
			want: []*ExternalRuleOutcomes{
				{
					Code:    OutcomesHighRiskCustomer,
					Outcome: "TRUE",
				},
				{
					Code:    OutcomesPEP,
					Outcome: "TRUE",
				},
				{
					Code:    OutcomesHighRiskCountry,
					Outcome: "TRUE",
				},
				{
					Code:    OutcomesCustomerType,
					Outcome: "Business",
				},
				{
					Code:    OutcomesVIP,
					Outcome: "TRUE",
				},
				{
					Code:    OutcomesAccountAge,
					Outcome: "0",
				},
			},
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &OutcomesFiled{
				HighRiskCustomer: tt.fields.HighRiskCustomer,
				PEP:              tt.fields.PEP,
				HighRiskCountry:  tt.fields.HighRiskCountry,
				IsKyb:            tt.fields.IsKyb,
				VIP:              tt.fields.VIP,
			}
			if got := t.ConvertToOutcomes(); !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("ConvertToOutcomes() = %v, want %v", got, tt.want)
			}
		})
	}
}
