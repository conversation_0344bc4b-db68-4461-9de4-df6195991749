package rn

import (
	"context"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/client/bhttpclient"
)

type (
	// Client is the interface for the security client. https://uponly.larksuite.com/wiki/GG33wczl2iT3Cek6E46ufQvVsff
	Client interface {
		SendReq(ctx context.Context, req *Request, org *OrgConfig) (*Response, *RawResponse, error)
	}

	Config struct {
		bhttpclient.Config

		CreateUserPath  string `json:",default=/TruAccountAPI/rest/Accounts/v1/RunStrategy"`
		TransactionPath string `json:",default=/TruAccountAPI/rest/Accounts/v1/RunStrategy"`

		FiatChannelOrg   *OrgConfig
		CardEEAOrg       *OrgConfig
		CardNonEEAOrg    *OrgConfig
		FiatChannelOrgEU *OrgConfig
		CardEEAOrgEU     *OrgConfig
	}

	OrgConfig struct {
		Username   string
		Password   string
		StrategyId int64 // 1896
	}

	client struct {
		c       Config
		bClient *bhttpclient.Client
	}
)

func NewClient(c Config) Client {
	bClient := bhttpclient.New(c.Config)
	bClient.SetHeader("Content-Type", "application/json")
	bClient.SetHeader("Accept", "application/json")

	return &client{
		c:       c,
		bClient: bClient,
	}
}

func (c *client) SendReq(ctx context.Context, req *Request, org *OrgConfig) (*Response, *RawResponse, error) {
	resp := &Response{}
	r, err := c.bClient.R().
		SetBasicAuth(org.Username, org.Password).
		SetContext(ctx).
		SetBody(req).
		SetResult(resp).
		Post(c.c.CreateUserPath)

	return resp, r, err
}
