package rn

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/client/bhttpclient"
	"github.com/stretchr/testify/assert"
)

func Test_client_SendReq(t *testing.T) {
	svr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
	}))
	c := &Config{
		Config: bhttpclient.Config{
			Name:                             "test",
			Addr:                             svr.URL,
			Debug:                            false,
			ReadTimeout:                      time.Second * 3,
			SlowLogThreshold:                 time.Second * 1,
			EnableMetricInterceptor:          false,
			EnableAccessInterceptor:          false,
			EnableAccessInterceptorReq:       false,
			EnableAccessInterceptorReqHeader: false,
			EnableAccessInterceptorRes:       false,
			Proxy:                            "",
		},
		CreateUserPath:  "/TruAccountAPI/rest/Accounts/v1/RunStrategy",
		TransactionPath: "/TruAccountAPI/rest/Accounts/v1/RunStrategy",
		FiatChannelOrg: &OrgConfig{
			Username:   "abc",
			Password:   "abc",
			StrategyId: 1989,
		},
		CardEEAOrg: &OrgConfig{
			Username:   "abc",
			Password:   "abc",
			StrategyId: 1989,
		},
		CardNonEEAOrg: &OrgConfig{
			Username:   "abc",
			Password:   "abc",
			StrategyId: 1989,
		},
	}
	client := NewClient(*c)
	_, _, err := client.SendReq(context.Background(), &Request{}, c.FiatChannelOrg)
	assert.NoError(t, err)
}
