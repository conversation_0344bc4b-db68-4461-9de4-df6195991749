package rn

import (
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	OutcomesHighRiskCustomer = "HIGH-RISK-CUSTOMER"
	OutcomesPEP              = "PEP"
	OutcomesHighRiskCountry  = "HIGH-RISK-COUNTRY"
	OutcomesCustomerType     = "CUSTOMER-TYPE"
	OutcomesVIP              = "VIP"
	OutcomesAccountAge       = "ACCOUNTAGE"
)

type (
	// Request @see https://api.trunarrative.com/#environments
	Request struct {
		AccountStrategyId int64  `json:"accountStrategyId,omitempty"`
		AccountReference  string `json:"accountReference,omitempty"`
		Application       struct {
			CustomerReference        string `json:"customerReference,omitempty"`
			ClientApplicationPurpose string `json:"clientApplicationPurpose,omitempty"`
			Country                  string `json:"country,omitempty"`
		} `json:"application,omitempty"`
		Person []*Person `json:"person,omitempty"`

		FinancialAccount struct {
			FinancialTransaction []*FinancialTransaction `json:"financialTransaction,omitempty"`
		} `json:"financialAccount,omitempty"`
	}

	// ----------------------------------------------------------------------------
	// person

	// Person is the request body for RN
	Person struct {
		ClientReference    string                `json:"clientReference,omitempty"`
		Gender             string                `json:"gender,omitempty"`
		DateOfBirth        string                `json:"dateOfBirth,omitempty"`
		FirstName          string                `json:"firstName,omitempty"`
		LastName           string                `json:"lastName,omitempty"`
		Title              string                `json:"title,omitempty"`
		IsPrimary          bool                  `json:"isPrimary,omitempty"`
		Role               string                `json:"role,omitempty"`
		ResidentialAddress []*ResidentialAddress `json:"residentialAddress,omitempty"`
	}

	ResidentialAddress struct {
		Standardised *PersonResidentialAddress `json:"standardised,omitempty"`
	}

	PersonResidentialAddress struct {
		Country string `json:"country,omitempty"`
		City    string `json:"city,omitempty"`
		Street  string `json:"street,omitempty"`
	}

	// ----------------------------------------------------------------------------
	// financial transaction

	FinancialTransaction struct {
		ClientReference      string                  `json:"clientReference,omitempty"`
		BicCode              string                  `json:"bicCode,omitempty"`
		SortCode             string                  `json:"sortCode,omitempty"`
		AccountNumber        string                  `json:"accountNumber,omitempty"`
		AccountHolder        string                  `json:"accountHolder,omitempty"`
		IBAN                 string                  `json:"IBAN,omitempty"`
		ThirdPartyId         string                  `json:"thirdPartyId,omitempty"`
		CreditOrDebit        string                  `json:"creditOrDebit,omitempty"`
		CurrencyCode         string                  `json:"currencyCode,omitempty"`
		PaymentType          string                  `json:"paymentType,omitempty"`
		PaymentSubtype       string                  `json:"paymentSubtype,omitempty"`
		CountryOfTransaction string                  `json:"countryOfTransaction,omitempty"`
		Amount               float64                 `json:"amount"`
		PaymentReference     string                  `json:"paymentReference,omitempty"`
		Balance              float64                 `json:"balance,omitempty"`
		EffectiveDateTime    string                  `json:"effectiveDateTime,omitempty"`
		ExternalRuleOutcomes []*ExternalRuleOutcomes `json:"externalRuleOutcomes,omitempty"`
	}

	ExternalRuleOutcomes struct {
		Code    string `json:"code,omitempty"`
		Outcome string `json:"outcome,omitempty"`
	}

	OutcomesFiled struct {
		AccountAge       int  `json:"ACCOUNTAGE,omitempty"`
		HighRiskCustomer bool `json:"HIGH-RISK-CUSTOMER,omitempty"`
		PEP              bool `json:"PEP,omitempty"`
		HighRiskCountry  bool `json:"HIGH-RISK-COUNTRY,omitempty"`
		IsKyb            bool `json:"CUSTOMER-TYPE,omitempty"`
		VIP              bool `json:"VIP,omitempty"`
	}

	// ----------------------------------------------------------------------------
	// Response

	// Response for RN Response
	Response struct {
		AccountId        int    `json:"accountId,omitempty"`
		AccountReference string `json:"accountReference,omitempty"`
		Status           struct {
			Code  string `json:"code,omitempty"`
			Label string `json:"label,omitempty"`
		} `json:"status,omitempty"`
		Decision struct {
			Code  string `json:"code,omitempty"`
			Label string `json:"label,omitempty"`
		} `json:"decision,omitempty"`
		Applications []struct {
			Uid            string `json:"uid,omitempty"`
			AuditReference string `json:"auditReference,omitempty"`
			RunId          int    `json:"runId,omitempty"`
			JourneyId      struct {
				Id   int    `json:"id,omitempty"`
				Name string `json:"name,omitempty"`
			} `json:"journeyId,omitempty"`
			OrganisationId struct {
				Id int `json:"id,omitempty"`
			} `json:"organisationId,omitempty"`
			Country string `json:"country,omitempty"`
			User    struct {
				Id   int    `json:"id,omitempty"`
				Name string `json:"name,omitempty"`
			} `json:"user,omitempty"`
			NameReference string `json:"nameReference,omitempty"`
			RiskLevel     string `json:"riskLevel,omitempty"`
			JourneyType   struct {
				Id    int    `json:"id,omitempty"`
				Label string `json:"label,omitempty"`
			} `json:"journeyType,omitempty"`
			StartDateTime  time.Time `json:"startDateTime,omitempty"`
			EndDateTime    time.Time `json:"endDateTime,omitempty"`
			ProgressStatus struct {
				Code  string `json:"code,omitempty"`
				Label string `json:"label,omitempty"`
			} `json:"progressStatus,omitempty"`
			Decision struct {
			} `json:"decision,omitempty"`
			RulesetOutcomes []struct {
				Reference    string `json:"reference,omitempty"`
				Score        int    `json:"score,omitempty"`
				RuleOutcomes []struct {
					Outcome              bool   `json:"outcome,omitempty"`
					Score                int    `json:"score,omitempty"`
					Description          string `json:"description,omitempty"`
					Name                 string `json:"name,omitempty"`
					VersionNumber        int    `json:"versionNumber,omitempty"`
					RevisionNumber       int    `json:"revisionNumber,omitempty"`
					NumberOfMatchRecords int    `json:"numberOfMatchRecords,omitempty"`
				} `json:"ruleOutcomes,omitempty"`
			} `json:"rulesetOutcomes,omitempty"`
			Links []struct {
				Code string `json:"code,omitempty"`
				Url  string `json:"url,omitempty"`
			} `json:"links,omitempty"`
			Links1 []struct {
				Code string `json:"code,omitempty"`
				Url  string `json:"url,omitempty"`
			} `json:"Links,omitempty"`
		} `json:"Applications,omitempty"`
	}

	RawResponse = resty.Response // 映射 http 的resp

)

// ----------------------------------------------------------------------------
// webhook body

type ReferralWebhookBody struct {
	Name              string    `json:"name,omitempty"`
	ReferralReference string    `json:"referralReference,omitempty"`
	Uid               string    `json:"uid,omitempty"`
	Status            string    `json:"status,omitempty"`
	DateTime          time.Time `json:"dateTime,omitempty"`
	RiskLevel         string    `json:"riskLevel,omitempty"`
	CustomerReference string    `json:"customerReference,omitempty"` // 对应交易中的 accountReference
	NextAction        struct {
		Id    int    `json:"id,omitempty"`
		Label string `json:"label,omitempty"`
	} `json:"nextAction,omitempty"`
	ClientApplicationReference string `json:"clientApplicationReference,omitempty"` // 对应交易中的 financialTransaction.clientReference
	Documents                  []struct {
		Id    string `json:"id,omitempty"`
		Type  string `json:"type,omitempty"`
		Pages []struct {
			Filename string `json:"filename,omitempty"`
			Tag      string `json:"tag,omitempty"`
		} `json:"pages,omitempty"`
	} `json:"documents,omitempty"`
	Flags []struct {
		Code  string `json:"code,omitempty"`
		Label string `json:"label,omitempty"`
	} `json:"flags,omitempty"`
	DecisionReason struct {
		Id    int    `json:"id,omitempty"`
		Label string `json:"label,omitempty"`
	} `json:"decisionReason,omitempty"`
}

func (t *OutcomesFiled) ConvertToOutcomes() []*ExternalRuleOutcomes {
	//HighRiskCustomer bool `json:"HIGH-RISK-CUSTOMER,omitempty"`
	//PEP              bool `json:"PEP,omitempty"`
	//HighRiskCountry  bool `json:"HIGH-RISK-COUNTRY,omitempty"`
	//CustomerType     bool `json:"CUSTOMER-TYPE,omitempty"`
	//VIP              bool `json:"VIP,omitempty"`
	var r []*ExternalRuleOutcomes
	if t.HighRiskCustomer {
		r = append(r, &ExternalRuleOutcomes{
			Code:    OutcomesHighRiskCustomer,
			Outcome: "TRUE",
		})
	} else {
		r = append(r, &ExternalRuleOutcomes{
			Code:    OutcomesHighRiskCustomer,
			Outcome: "FALSE",
		})
	}
	if t.PEP {
		r = append(r, &ExternalRuleOutcomes{
			Code:    OutcomesPEP,
			Outcome: "TRUE",
		})
	} else {
		r = append(r, &ExternalRuleOutcomes{
			Code:    OutcomesPEP,
			Outcome: "FALSE",
		})
	}
	if t.HighRiskCountry {
		r = append(r, &ExternalRuleOutcomes{
			Code:    OutcomesHighRiskCountry,
			Outcome: "TRUE",
		})
	} else {
		r = append(r, &ExternalRuleOutcomes{
			Code:    OutcomesHighRiskCountry,
			Outcome: "FALSE",
		})
	}
	if t.IsKyb {
		r = append(r, &ExternalRuleOutcomes{
			Code:    OutcomesCustomerType,
			Outcome: "Business",
		})
	} else {
		r = append(r, &ExternalRuleOutcomes{
			Code:    OutcomesCustomerType,
			Outcome: "Person",
		})
	}
	if t.VIP {
		r = append(r, &ExternalRuleOutcomes{
			Code:    OutcomesVIP,
			Outcome: "TRUE",
		})
	} else {
		r = append(r, &ExternalRuleOutcomes{
			Code:    OutcomesVIP,
			Outcome: "FALSE",
		})
	}
	r = append(r, &ExternalRuleOutcomes{
		Code:    OutcomesAccountAge,
		Outcome: fmt.Sprintf("%d", t.AccountAge),
	})

	return r
}
