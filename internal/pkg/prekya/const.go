package prekya

import aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/bybit/fiat/aml/v1"

type PreKyaResult struct {
	Key             string
	Title           string
	TitleCkey       string
	Description     string
	DescriptionCkey string
}

type ChainCoinAmount struct {
	Coin      string
	Amount    string
	AmountUsd string
}

const (
	PreKyaResultNoRisk          = "NoRisk"
	PreKyaResultLowRisk         = "LowRisk"
	PreKyaResultHighRisk        = "HighRisk"
	PreKyaResultErrorNoVIP      = "ErrorNoVIP"
	PreKyaResultErrorNoQuota    = "ErrorNoQuota"
	PreKyaResultErrorInProgress = "ErrorInProgress"
	PreKyaResultError           = "Error"
)

var (
	NoErrorResult = []string{PreKyaResultNoRisk, PreKyaResultLowRisk, PreKyaResultHighRisk}
	SupportChain  = []*aml_insightv1.SupportChain{
		{
			Chain:   "MATIC",
			Display: "MATIC",
		},
		{
			Chain:   "BSC",
			Display: "BSC",
		},
		{
			Chain:   "ETH",
			Display: "ETH",
		},
		{
			Chain:   "OP",
			Display: "OP",
		},
		{
			Chain:   "ARBI",
			Display: "ARBI",
		},
		{
			Chain:   "SOL",
			Display: "SOL",
		},
		{
			Chain:   "TRX",
			Display: "TRX",
		},
		{
			Chain:   "BTC",
			Display: "BTC",
		},
		{
			Chain:   "XRP",
			Display: "XRP",
		},
	}

	chainCoinAmountMap = map[string]*ChainCoinAmount{
		// 2025-03-03 数据
		"XRP": {
			Coin:      "XRP",
			Amount:    "6852", // 6951.165800000000000000000000000000
			AmountUsd: "15024.380400000000000000000000000000",
		},
		"BTC": {
			Coin:      "BTC",
			Amount:    "0.16149111", // 0.178791390000000000000000000000
			AmountUsd: "15011.956814735100000000000000000000",
		},
		"SOL": {
			Coin:      "SOL",
			Amount:    "86.6999", // 86.699900000000000000000000000000
			AmountUsd: "15035.496657999998000000000000000000",
		},
	}
)

var (
	// key: VIP level, val: quota
	VipPreCheckQuota = map[int32]int32{
		1:  4,
		2:  5,
		3:  6,
		4:  7,
		5:  8,
		99: 10,
	}

	PreKyaResultMap = map[string]*PreKyaResult{
		PreKyaResultNoRisk: &PreKyaResult{
			Key:             "NoRisk",
			Title:           "No Risks Detected",
			TitleCkey:       "amlNoRiskTitleCkey",
			Description:     "No risks have been identified for this address.",
			DescriptionCkey: "amlNoRiskDescCkey",
		},
		PreKyaResultLowRisk: &PreKyaResult{
			Key:             "LowRisk",
			Title:           "Low risk",
			TitleCkey:       "amlLowRiskTitleCkey",
			Description:     "We couldn't identify any significant connections between the transaction and factors related to risk or reliability.",
			DescriptionCkey: "amlLowRiskDescCkey",
		},
		PreKyaResultHighRisk: &PreKyaResult{
			Key:             "HighRisk",
			Title:           "High risk",
			TitleCkey:       "amlHighRiskTitleCkey",
			Description:     "Significant risk connection identified: We recommended changing the deposit sender's or withdrawal receiver's wallet address due to the high risk associated with the current wallet address.",
			DescriptionCkey: "amlHighRiskDescCkey",
		},
		PreKyaResultErrorNoVIP: &PreKyaResult{
			Key:             "ErrorNoVIP",
			Title:           "VIP Access Required",
			TitleCkey:       "amlErrorNoVIPTitleCkey",
			Description:     "This service is for VIP users only. Please upgrade to access it.",
			DescriptionCkey: "amlErrorNoVIPDescCkey",
		},
		PreKyaResultErrorNoQuota: &PreKyaResult{
			Key:             "ErrorNoQuota",
			Title:           "Max Query Attempts Reached",
			TitleCkey:       "amlErrorNoQuotaTitleCkey",
			Description:     "You've reached your query limit for this week. Please try again next week.",
			DescriptionCkey: "amlErrorNoQuotaDescCkey",
		},
		PreKyaResultErrorInProgress: &PreKyaResult{
			Key:             "ErrorInProgress",
			Title:           "Scan Queued",
			TitleCkey:       "amlErrorInProgressTitleCkey",
			Description:     "Network congestion detected. Your scan is in the queue — please wait patiently.",
			DescriptionCkey: "amlErrorInProgressDescCkey",
		},
		PreKyaResultError: &PreKyaResult{
			Key:             "Error",
			Title:           "System error",
			TitleCkey:       "amlErrorTitleCkey",
			Description:     "Try again later.",
			DescriptionCkey: "amlErrorDescCkey",
		},
	}
)

func GetCoinAmountByChain(chain string) (string, string) {
	if coinAmount, ok := chainCoinAmountMap[chain]; ok {
		return coinAmount.Coin, coinAmount.Amount
	}

	return "USDT", "15000"

}
