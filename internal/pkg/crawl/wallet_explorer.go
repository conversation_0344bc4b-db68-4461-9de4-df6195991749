package crawl

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"net/http"
	"time"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/client/bhttpclient"
	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/core/mr"
	"github.com/go-resty/resty/v2"
)

const (
	// API constants
	defaultWalletExplorerBaseURL = "http://www.walletexplorer.com/api/1"
	defaultPageSize              = 100
	defaultMaxRetries            = 3
	defaultRetryDelayMs          = 2000
	defaultMaxConcurrency        = 3
	defaultCallerEmail           = "<EMAIL>"

	defaultConcurrencyLimit = 50
)

// WalletExplorerCrawlConfig configuration for wallet explorer crawler
type WalletExplorerCrawlConfig struct {
	bhttpclient.Config
	BaseURL            string `json:",default=http://www.walletexplorer.com/api/1"`
	MaxRetries         int    `json:",default=60"`
	RetryDelayMs       int    `json:",default=1000"`  // milliseconds
	RetryMaxWaitTimeMs int    `json:",default=15000"` // milliseconds
	MaxConcurrency     int    `json:",default=3"`
	PageSize           int    `json:",default=100"`
	CallerEmail        string `json:",default=<EMAIL>"`
}

// Validate validates the configuration
func (c *WalletExplorerCrawlConfig) Validate() error {
	if c.BaseURL == "" {
		c.BaseURL = defaultWalletExplorerBaseURL
	}
	if c.MaxRetries <= 0 {
		c.MaxRetries = defaultMaxRetries
	}
	if c.RetryDelayMs <= 0 {
		c.RetryDelayMs = defaultRetryDelayMs
	}
	if c.MaxConcurrency <= 0 {
		c.MaxConcurrency = defaultMaxConcurrency
	}
	if c.PageSize <= 0 {
		c.PageSize = defaultPageSize
	}
	if c.CallerEmail == "" {
		c.CallerEmail = defaultCallerEmail
	}

	// Validate reasonable limits
	if c.PageSize > 1000 {
		return fmt.Errorf("page size too large: %d (max: 1000)", c.PageSize)
	}

	return nil
}

// WalletExplorerCrawler wallet explorer API crawler
type WalletExplorerCrawler struct {
	client *bhttpclient.Client
	config WalletExplorerCrawlConfig
}

// WalletExplorerResponse API response structure
type WalletExplorerResponse struct {
	Found          bool                `json:"found"`
	WalletID       string              `json:"wallet_id"`
	AddressesCount int                 `json:"addresses_count"`
	Addresses      []WalletAddressInfo `json:"addresses"`
}

// AddressLookupResponse address lookup response structure
type AddressLookupResponse struct {
	Found    bool   `json:"found"`
	WalletID string `json:"wallet_id"`
}

// WalletAddressInfo wallet address information from API
type WalletAddressInfo struct {
	Address         string  `json:"address"`
	Balance         float64 `json:"balance"`
	IncomingTxs     int64   `json:"incoming_txs"`
	LastUsedInBlock int64   `json:"last_used_in_block"`
}

type BaseLabelInfo struct {
	Chain      string `json:"chain"`
	Category   string `json:"category"`
	EntityName string `json:"entity_name"`
	DetailName string `json:"detail_name"`
	Editor     string `json:"editor"`
	Source     string `json:"source"`
	Remark     string `json:"remark"`
	Valid      int32  `json:"valid"`
}

// AddressLabelInfo address label information for output (matches AllAddressLabel)
type AddressLabelInfo struct {
	Address string `json:"address"`
	BaseLabelInfo
}

// ProcessCallback defines the callback function type for processing wallet address data
// Parameters: addresses - batch of address data, params - base label information
// Returns: error - return error to interrupt further processing
type ProcessCallback func(addresses []AddressLabelInfo) error

// NewWalletExplorerCrawler create new wallet explorer crawler
func NewWalletExplorerCrawler(config WalletExplorerCrawlConfig) (*WalletExplorerCrawler, error) {
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	client := bhttpclient.New(config.Config)

	// Configure client with retry and timeout settings
	// Configuration explanation:
	// 1. RetryCount: Maximum number of retry attempts
	// 2. RetryWaitTime: Base wait time for network errors and non-HTTP status errors
	// 3. RetryMaxWaitTime: Maximum wait time limit for exponential backoff
	// 4. RetryAfter: Custom retry logic with different strategies for different HTTP status codes
	client.SetHeader("Content-Type", "application/json").
		SetHeader("Accept", "application/json").
		SetHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36").
		SetRetryCount(config.MaxRetries).
		SetRetryWaitTime(time.Duration(config.RetryDelayMs) * time.Millisecond).          // Base wait time for network errors
		SetRetryMaxWaitTime(time.Duration(config.RetryMaxWaitTimeMs) * time.Millisecond). // Maximum wait time limit
		AddRetryCondition(retryCondition).
		SetRetryAfter(func(client *resty.Client, resp *resty.Response) (time.Duration, error) {
			// Strategy 1: For 4xx/5xx errors, use exponential backoff with random jitter
			if is4xxOr5xxStatus(resp.StatusCode()) {
				attempt := client.RetryCount + 1
				// Exponential backoff: 200ms * 2^attempt
				baseWait := time.Duration(math.Pow(2, float64(attempt))) * 200 * time.Millisecond
				// Random jitter: ±20%
				jitter := time.Duration(float64(baseWait) * (0.8 + 0.4*rand.Float64()))
				if jitter > client.RetryMaxWaitTime {
					jitter = client.RetryMaxWaitTime
				}
				return jitter, nil
			}
			// Strategy 2: For network errors, use fixed wait time
			return client.RetryWaitTime, nil
		})

	return &WalletExplorerCrawler{
		client: client,
		config: config,
	}, nil
}

func is4xxOr5xxStatus(statusCode int) bool {
	return statusCode >= 400 && statusCode < 600
}

// retryCondition determine whether to retry based on response
func retryCondition(response *resty.Response, err error) bool {
	// Network errors should be retried
	if err != nil {
		incCrawlRetry("network_error")
		return true
	}

	// Retry on server errors and rate limiting
	switch response.StatusCode() {
	case http.StatusOK:
		return false
	case http.StatusTooManyRequests:
		incCrawlRetry("rate_limit")
		return true
	case http.StatusInternalServerError:
		incCrawlRetry("server_error")
		return true
	case http.StatusServiceUnavailable:
		incCrawlRetry("service_unavailable")
		return true
	case http.StatusGatewayTimeout:
		incCrawlRetry("timeout")
		return true
	case http.StatusBadGateway:
		incCrawlRetry("bad_gateway")
		return true
	default:
		return false
	}
}

// GetWalletData get complete wallet data by address
func (c *WalletExplorerCrawler) GetWalletData(ctx context.Context, walletAddress string, params BaseLabelInfo) (ret []AddressLabelInfo, err_ error) {
	defer func(startTime time.Time) {
		if len(ret) > 0 {
			observeCrawlAddressCount(walletAddress, len(ret))
		}

		duration := time.Since(startTime)
		if err_ == nil {
			observeCrawlDuration("success", duration)
		} else {
			observeCrawlDuration("fail", duration)
		}

		logc.Infow(ctx, "completed wallet data crawling",
			logc.Field("duration_ms", duration.Milliseconds()),
			logc.Field("error", err_))
	}(time.Now())

	walletID, err := c.getWalletID(ctx, walletAddress)
	if err != nil {
		incCrawlError("wallet_id_lookup")
		return nil, fmt.Errorf("failed to get wallet ID: %w", err)
	}

	logc.Infow(ctx, "successfully retrieved wallet ID",
		logc.Field("wallet_id", walletID),
		logc.Field("address", walletAddress))

	totalCount, initialAddresses, err := c.getWalletTotal(ctx, walletID)
	if err != nil {
		incCrawlError("wallet_total")
		return nil, fmt.Errorf("failed to get wallet total: %w", err)
	}

	logc.Infow(ctx, "retrieved wallet total information",
		logc.Field("total_count", totalCount),
		logc.Field("initial_addresses", len(initialAddresses)))

	allAddresses := make([]WalletAddressInfo, 0, totalCount)
	allAddresses = append(allAddresses, initialAddresses...)

	if totalCount > c.config.PageSize {
		remainingAddresses, err := c.getWalletDataConcurrently(ctx, walletID, totalCount)
		if err != nil {
			incCrawlError("paginated_data")
			return nil, fmt.Errorf("failed to get remaining wallet data: %w", err)
		}
		allAddresses = append(allAddresses, remainingAddresses...)
	}

	labeledData := make([]AddressLabelInfo, 0, len(allAddresses))
	for _, addr := range allAddresses {
		labelInfo := AddressLabelInfo{
			Address:       addr.Address,
			BaseLabelInfo: params,
		}
		labeledData = append(labeledData, labelInfo)
	}

	logc.Infow(ctx, "successfully completed wallet data collection",
		logc.Field("wallet_address", walletAddress),
		logc.Field("total_addresses_count", len(allAddresses)),
		logc.Field("labeled_data_count", len(labeledData)),
		logc.Field("expected_total_count", totalCount))

	return labeledData, nil
}

// getWalletID get wallet ID by address
func (c *WalletExplorerCrawler) getWalletID(ctx context.Context, address string) (string, error) {
	url := fmt.Sprintf("%s/address-lookup", c.config.BaseURL)

	var resp AddressLookupResponse
	r, err := c.client.R().
		SetContext(ctx).
		SetQueryParam("address", address).
		SetQueryParam("caller", c.config.CallerEmail).
		SetResult(&resp).
		Get(url)

	// Record request metrics
	if err != nil {
		incCrawlRequest("address_lookup", "error")
		return "", fmt.Errorf("failed to lookup address: %w", err)
	}

	if r.StatusCode() != http.StatusOK {
		incCrawlRequest("address_lookup", "failed")
		return "", fmt.Errorf("API request failed with status %d", r.StatusCode())
	}

	incCrawlRequest("address_lookup", "success")

	if !resp.Found {
		return "", fmt.Errorf("wallet not found for address: %s", address)
	}

	return resp.WalletID, nil
}

// getWalletTotal get wallet total address count and initial addresses
func (c *WalletExplorerCrawler) getWalletTotal(ctx context.Context, walletID string) (int, []WalletAddressInfo, error) {
	url := fmt.Sprintf("%s/wallet-addresses", c.config.BaseURL)

	var resp WalletExplorerResponse
	r, err := c.client.R().
		SetContext(ctx).
		SetQueryParam("wallet", walletID).
		SetQueryParam("from", "0").
		SetQueryParam("count", fmt.Sprintf("%d", c.config.PageSize)).
		SetResult(&resp).
		Get(url)

	// Record request metrics
	if err != nil {
		incCrawlRequest("wallet_addresses", "error")
		return 0, nil, fmt.Errorf("failed to get wallet total: %w", err)
	}

	if r.StatusCode() != http.StatusOK {
		incCrawlRequest("wallet_addresses", "failed")
		return 0, nil, fmt.Errorf("API request failed with status %d", r.StatusCode())
	}

	incCrawlRequest("wallet_addresses", "success")

	if !resp.Found {
		incCrawlRequest("wallet_addresses", "not_found")
		return 0, nil, fmt.Errorf("wallet data not found")
	}

	return resp.AddressesCount, resp.Addresses, nil
}

// getWalletDataConcurrently get remaining wallet data concurrently using mr framework
func (c *WalletExplorerCrawler) getWalletDataConcurrently(ctx context.Context, walletID string, total int) ([]WalletAddressInfo, error) {
	pageIndices := make([]int, 0, total/c.config.PageSize+1)
	for page := c.config.PageSize; page < total; page += c.config.PageSize {
		pageIndices = append(pageIndices, page)
	}

	if len(pageIndices) == 0 {
		return []WalletAddressInfo{}, nil
	}

	logc.Infow(ctx, "starting concurrent data fetching",
		logc.Field("total_pages", len(pageIndices)),
		logc.Field("max_concurrency", c.config.MaxConcurrency),
		logc.Field("wallet_id", walletID))

	allAddresses, err := mr.MapReduce(
		func(source chan<- int) {
			for _, page := range pageIndices {
				source <- page
			}
		},
		// Mapper: fetch data for each page
		func(page int, writer mr.Writer[[]WalletAddressInfo], cancel func(error)) {
			addresses, err := c.getPageData(ctx, walletID, page)
			if err != nil {
				logc.Warnw(ctx, "failed to retrieve paginated data",
					logc.Field("page", page),
					logc.Field("error", err))
				// TODO: cannot cancel other tasks
				// cancel(err)
				return
			}

			writer.Write(addresses)
		},
		// Reducer: combine all address arrays
		func(pipe <-chan []WalletAddressInfo, writer mr.Writer[[]WalletAddressInfo], cancel func(error)) {
			var allAddresses []WalletAddressInfo
			for addresses := range pipe {
				allAddresses = append(allAddresses, addresses...)
			}
			writer.Write(allAddresses)
		},
		mr.WithWorkers(c.config.MaxConcurrency),
		mr.WithContext(ctx),
	)

	if err != nil {
		return nil, fmt.Errorf("failed to fetch paginated data concurrently: %w", err)
	}

	logc.Infow(ctx, "completed concurrent data fetching",
		logc.Field("total_addresses_count", len(allAddresses)),
		logc.Field("pages_processed_count", len(pageIndices)))

	return allAddresses, nil
}

// getPageData get single page of wallet data
func (c *WalletExplorerCrawler) getPageData(ctx context.Context, walletID string, fromIndex int) ([]WalletAddressInfo, error) {
	url := fmt.Sprintf("%s/wallet-addresses", c.config.BaseURL)

	var resp WalletExplorerResponse
	r, err := c.client.R().
		SetContext(ctx).
		SetQueryParam("wallet", walletID).
		SetQueryParam("from", fmt.Sprintf("%d", fromIndex)).
		SetQueryParam("count", fmt.Sprintf("%d", c.config.PageSize)).
		SetResult(&resp).
		Get(url)

	// Record request metrics
	if err != nil {
		incCrawlRequest("wallet_addresses_page", "error")
		return nil, fmt.Errorf("failed to get page data: %w", err)
	}

	if r.StatusCode() != http.StatusOK {
		incCrawlRequest("wallet_addresses_page", "failed")
		return nil, fmt.Errorf("API request failed with status %d", r.StatusCode())
	}

	if !resp.Found {
		incCrawlRequest("wallet_addresses_page", "not_found")
		return nil, fmt.Errorf("wallet data not found")
	}

	incCrawlRequest("wallet_addresses_page", "success")
	return resp.Addresses, nil
}

// ProcessWalletDataWithCallback processes wallet data using streaming with callback function
// This function retrieves data and processes it through callback as data arrives, supporting concurrent processing
// maxConcurrency: maximum concurrency level, if <= 0 then uses default value from config
func (c *WalletExplorerCrawler) ProcessWalletDataWithCallback(ctx context.Context, walletAddress string,
	params BaseLabelInfo, callback ProcessCallback, maxConcurrency int) (totalProcessed int, err_ error) {
	defer func(startTime time.Time) {
		duration := time.Since(startTime)
		if err_ == nil {
			observeCrawlDuration("stream_success", duration)
		} else {
			observeCrawlDuration("stream_fail", duration)
		}
		observeCrawlAddressCount(walletAddress, totalProcessed)
	}(time.Now())

	actualConcurrency := maxConcurrency
	if actualConcurrency <= 0 {
		actualConcurrency = c.config.MaxConcurrency
	} else if actualConcurrency > defaultConcurrencyLimit {
		actualConcurrency = defaultConcurrencyLimit
	}

	walletID, err := c.getWalletID(ctx, walletAddress)
	if err != nil {
		incCrawlError("wallet_id_lookup")
		return 0, fmt.Errorf("failed to get wallet ID: %w", err)
	}

	totalCount, initialAddresses, err := c.getWalletTotal(ctx, walletID)
	if err != nil {
		incCrawlError("wallet_total")
		return 0, fmt.Errorf("failed to get wallet total: %w", err)
	}

	logc.Infow(ctx, "retrieved wallet total for streaming",
		logc.Field("total_count", totalCount),
		logc.Field("initial_addresses", len(initialAddresses)))

	if len(initialAddresses) > 0 {
		labeledData := c.convertToLabeledData(initialAddresses, params)
		if err := callback(labeledData); err != nil {
			return 0, fmt.Errorf("callback failed for initial data: %w", err)
		}
		totalProcessed += len(labeledData)
	}

	if totalCount > c.config.PageSize {
		processed, err := c.processRemainingDataWithCallback(ctx, walletID, totalCount, params, callback, actualConcurrency)
		if err != nil {
			incCrawlError("stream_paginated_data")
			return totalProcessed, fmt.Errorf("failed to process remaining data: %w", err)
		}
		totalProcessed += processed
	}

	logc.Infow(ctx, "successfully completed streaming wallet data processing",
		logc.Field("wallet_address", walletAddress),
		logc.Field("initial_addresses", len(initialAddresses)),
		logc.Field("total_addresses_processed", totalProcessed),
		logc.Field("expected_total_count", totalCount),
		logc.Field("used_concurrency", actualConcurrency))

	return totalProcessed, nil
}

// processRemainingDataWithCallback processes remaining wallet data concurrently
func (c *WalletExplorerCrawler) processRemainingDataWithCallback(ctx context.Context, walletID string, total int,
	params BaseLabelInfo, callback ProcessCallback, actualConcurrency int) (int, error) {
	pageIndices := make([]int, 0, total/c.config.PageSize+1)
	for page := c.config.PageSize; page < total; page += c.config.PageSize {
		pageIndices = append(pageIndices, page)
	}

	if len(pageIndices) == 0 {
		return 0, nil
	}

	logc.Infow(ctx, "starting concurrent streaming data processing",
		logc.Field("total_pages", len(pageIndices)),
		logc.Field("actual_concurrency", actualConcurrency),
		logc.Field("wallet_id", walletID))

	totalProcessed, err := mr.MapReduce(
		func(source chan<- int) {
			for _, page := range pageIndices {
				source <- page
			}
		},
		// Mapper: retrieve page data and process immediately via callback
		func(page int, writer mr.Writer[int], cancel func(error)) {
			addresses, err := c.getPageData(ctx, walletID, page)
			if err != nil {
				logc.Warnw(ctx, "failed to retrieve paginated data for streaming",
					logc.Field("page", page),
					logc.Field("error", err))
				// Consider whether to cancel other tasks, here we continue processing other pages for now
				// cancel(err)
				writer.Write(0)
				return
			}

			if len(addresses) > 0 {
				labeledData := c.convertToLabeledData(addresses, params)
				if err := callback(labeledData); err != nil {
					logc.Errorw(ctx, "callback failed for page data",
						logc.Field("page", page),
						logc.Field("error", err))
					// cancel(fmt.Errorf("callback failed for page %d: %w", page, err))
					return
				}
				writer.Write(len(labeledData))
			} else {
				writer.Write(0)
			}
		},
		// Reducer: aggregate processed data count
		func(pipe <-chan int, writer mr.Writer[int], cancel func(error)) {
			total := 0
			for count := range pipe {
				total += count
			}
			writer.Write(total)
		},
		mr.WithWorkers(actualConcurrency),
		mr.WithContext(ctx),
	)

	if err != nil {
		return 0, fmt.Errorf("failed to process paginated data with callback: %w", err)
	}

	logc.Infow(ctx, "completed concurrent streaming data processing",
		logc.Field("pages_processed_count", len(pageIndices)),
		logc.Field("addresses_processed", totalProcessed),
		logc.Field("used_concurrency", actualConcurrency))

	return totalProcessed, nil
}

// convertToLabeledData converts wallet address information to labeled address information
func (c *WalletExplorerCrawler) convertToLabeledData(addresses []WalletAddressInfo, params BaseLabelInfo) []AddressLabelInfo {
	labeledData := make([]AddressLabelInfo, 0, len(addresses))
	for _, addr := range addresses {
		labelInfo := AddressLabelInfo{
			Address:       addr.Address,
			BaseLabelInfo: params,
		}
		labeledData = append(labeledData, labelInfo)
	}
	return labeledData
}
