package crawl

import (
	"time"

	"code.bydev.io/frameworks/byone/core/metric"
)

var (
	// Metrics for monitoring wallet explorer crawler
	crawlDurationHistogram = metric.NewHistogramVec(
		&metric.HistogramVecOpts{
			Namespace: "aml_insight",
			Subsystem: "wallet_explorer",
			Name:      "crawl_duration_seconds",
			Help:      "Time spent crawling wallet data",
			Labels:    []string{"result"},
			Buckets:   []float64{0.1, 0.5, 1, 2, 5, 10, 30, 60, 120},
		},
	)

	crawlAddressCountHistogram = metric.NewHistogramVec(
		&metric.HistogramVecOpts{
			Namespace: "aml_insight",
			Subsystem: "wallet_explorer",
			Name:      "crawl_address_count",
			Help:      "Number of addresses crawled",
			Labels:    []string{"wallet_address"},
			Buckets:   []float64{100, 1000, 5000, 10000, 100000, 1000000, 10000000, 100000000},
		},
	)

	crawlRequestCounter = metric.NewCounterVec(
		&metric.CounterVecOpts{
			Namespace: "aml_insight",
			Subsystem: "wallet_explorer",
			Name:      "crawl_requests_total",
			Help:      "Total number of API requests made",
			Labels:    []string{"endpoint", "status"},
		},
	)

	crawlErrorCounter = metric.NewCounterVec(
		&metric.CounterVecOpts{
			Namespace: "aml_insight",
			Subsystem: "wallet_explorer",
			Name:      "crawl_errors_total",
			Help:      "Total number of crawling errors",
			Labels:    []string{"error_type"},
		},
	)

	crawlRetryCounter = metric.NewCounterVec(
		&metric.CounterVecOpts{
			Namespace: "aml_insight",
			Subsystem: "wallet_explorer",
			Name:      "crawl_retries_total",
			Help:      "Total number of request retries",
			Labels:    []string{"endpoint"},
		},
	)
)

// Helper functions for metrics

// incCrawlRequest increments the request counter with endpoint and status labels
func incCrawlRequest(endpoint, status string) {
	crawlRequestCounter.Inc(endpoint, status)
}

// incCrawlError increments the error counter with error type label
func incCrawlError(errorType string) {
	crawlErrorCounter.Inc(errorType)
}

// incCrawlRetry increments the retry counter with endpoint label
func incCrawlRetry(endpoint string) {
	crawlRetryCounter.Inc(endpoint)
}

// observeCrawlDuration records the duration of crawling operation
func observeCrawlDuration(result string, duration time.Duration) {
	crawlDurationHistogram.Observe(int64(duration.Milliseconds()), result)
}

// observeCrawlAddressCount records the number of addresses crawled
func observeCrawlAddressCount(walletAddress string, count int) {
	crawlAddressCountHistogram.Observe(int64(count), walletAddress)
}
