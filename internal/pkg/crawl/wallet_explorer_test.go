package crawl

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"testing"
	"time"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/client/bhttpclient"
)

func TestBaseLabelInfo_Creation(t *testing.T) {
	// Test BaseLabelInfo structure creation
	params := BaseLabelInfo{
		Chain:      "BTC",
		Category:   "exchange",
		EntityName: "Binance.com",
		DetailName: "Binance.com_user",
		Editor:     "alex.gao",
		Source:     "bybit",
		Remark:     "AI Expanding",
		Valid:      1,
	}

	if params.Chain != "BTC" {
		t.Errorf("Expected chain BTC, got %s", params.Chain)
	}
	if params.Category != "exchange" {
		t.<PERSON>rf("Expected category exchange, got %s", params.Category)
	}
	if params.EntityName != "Binance.com" {
		t.Errorf("Expected entity name Binance.com, got %s", params.EntityName)
	}
}

func TestWalletExplorerConfig_Validate(t *testing.T) {
	tests := []struct {
		name    string
		config  WalletExplorerCrawlConfig
		wantErr bool
	}{
		{
			name: "valid config with defaults",
			config: WalletExplorerCrawlConfig{
				Config: bhttpclient.Config{},
			},
			wantErr: false,
		},
		{
			name: "config with large page size",
			config: WalletExplorerCrawlConfig{
				Config:   bhttpclient.Config{},
				PageSize: 2000,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("WalletExplorerConfig.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// Example function that demonstrates usage
func ExampleWalletExplorerCrawler_GetWalletData() {
	config := WalletExplorerCrawlConfig{
		Config: bhttpclient.Config{
			// HTTP client configuration can be set here
		},
	}

	crawler, err := NewWalletExplorerCrawler(config)
	if err != nil {
		panic(err)
	}

	ctx := context.Background()
	walletAddress := "**********************************"

	params := BaseLabelInfo{
		Chain:      "BTC",
		Category:   "exchange",
		EntityName: "Binance.com",
		DetailName: "Binance.com_user",
		Editor:     "alex.gao",
		Source:     "bybit",
		Remark:     "AI Expanding",
		Valid:      1,
	}

	// Get complete wallet data
	labeledData, err := crawler.GetWalletData(ctx, walletAddress, params)
	if err != nil {
		// Handle error appropriately in real code
		return
	}

	// Use labeled data
	for _, labelInfo := range labeledData {
		// Process each address label info
		_ = labelInfo // Use labelInfo as needed
	}
}

func TestNewWalletExplorerCrawler(t *testing.T) {
	tests := []struct {
		name    string
		config  WalletExplorerCrawlConfig
		wantErr bool
	}{
		{
			name: "valid config",
			config: WalletExplorerCrawlConfig{
				Config:         bhttpclient.Config{Name: "test"},
				BaseURL:        "http://test.com/api",
				MaxRetries:     3,
				RetryDelayMs:   1000,
				MaxConcurrency: 3,
				PageSize:       100,
				CallerEmail:    "<EMAIL>",
			},
			wantErr: false,
		},
		{
			name: "default config",
			config: WalletExplorerCrawlConfig{
				Config: bhttpclient.Config{Name: "test"},
			},
			wantErr: false,
		},
		{
			name: "invalid config - page size too large",
			config: WalletExplorerCrawlConfig{
				Config:   bhttpclient.Config{Name: "test"},
				PageSize: 2000,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			crawler, err := NewWalletExplorerCrawler(tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewWalletExplorerCrawler() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && crawler == nil {
				t.Error("NewWalletExplorerCrawler() returned nil crawler when expecting success")
			}

			if !tt.wantErr && crawler != nil {
				if crawler.client == nil {
					t.Error("NewWalletExplorerCrawler() returned crawler with nil client")
				}
			}
		})
	}
}

// TestWalletExplorerIntegration integration test with real Bitcoin address
func TestWalletExplorerIntegration(t *testing.T) {
	// Skip integration test if not explicitly enabled
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Real Bitcoin address for testing
	testAddress := "**********************************"

	params := BaseLabelInfo{
		Chain:      "BTC",
		Category:   "test",
		EntityName: "test_entity",
		DetailName: "test_entity_user",
		Editor:     "alex.gao",
		Source:     "bybit",
		Remark:     "AI Expanding",
		Valid:      1,
	}

	// Create crawler with default configuration
	config := WalletExplorerCrawlConfig{
		Config: bhttpclient.Config{
			Name: "integration-test",
		},
	}

	crawler, err := NewWalletExplorerCrawler(config)
	if err != nil {
		t.Fatalf("Failed to create crawler: %v", err)
	}

	// Create context
	ctx := context.Background()

	// Get wallet data
	t.Logf("Fetching wallet data for address: %s", testAddress)
	labeledData, err := crawler.GetWalletData(ctx, testAddress, params)
	if err != nil {
		t.Fatalf("Failed to get wallet data: %v", err)
	}

	// Log basic information
	t.Logf("Retrieved Labeled Data Count: %d", len(labeledData))

	// Print actual results
	fmt.Println("Labeled Data:")
	fmt.Println("address,chain,category,entity_name,detail_name,editor,source,remark,valid")
	for _, labelInfo := range labeledData {
		fmt.Printf("%s,%s,%s,%s,%s,%s,%s,%s,%d\n",
			labelInfo.Address,
			labelInfo.Chain,
			labelInfo.Category,
			labelInfo.EntityName,
			labelInfo.DetailName,
			labelInfo.Editor,
			labelInfo.Source,
			labelInfo.Remark,
			labelInfo.Valid)
	}

	// Validate the results
	if len(labeledData) == 0 {
		t.Error("Expected at least one address, got none")
	}

	// Validate each returned address
	for _, labelInfo := range labeledData {
		if labelInfo.Address == "" {
			t.Error("Address should not be empty")
		}
		if labelInfo.Chain != params.Chain {
			t.Errorf("Expected chain %s, got %s", params.Chain, labelInfo.Chain)
		}
		if labelInfo.Category != params.Category {
			t.Errorf("Expected category %s, got %s", params.Category, labelInfo.Category)
		}
		if labelInfo.EntityName != params.EntityName {
			t.Errorf("Expected entity name %s, got %s", params.EntityName, labelInfo.EntityName)
		}
	}

	fmt.Printf("\nValidation Summary: Found %d labeled addresses\n", len(labeledData))
}

// TestProcessWalletDataWithCallback tests the callback-based processing function
func TestWalletExplorerCrawler_ProcessWalletDataWithCallback(t *testing.T) {
	// Skip if in short mode to avoid network calls
	if testing.Short() {
		t.Skip("Skipping callback test in short mode")
	}

	config := WalletExplorerCrawlConfig{
		Config: bhttpclient.Config{
			Name: "callback-test",
		},
		MaxConcurrency: 2, // Set default concurrency
	}

	crawler, err := NewWalletExplorerCrawler(config)
	if err != nil {
		t.Fatalf("Failed to create crawler: %v", err)
	}

	testAddress := "**********************************"
	params := BaseLabelInfo{
		Chain:      "BTC",
		Category:   "test",
		EntityName: "test_entity",
		DetailName: "test_entity_user",
		Editor:     "test",
		Source:     "unit_test",
		Remark:     "callback test",
		Valid:      1,
	}

	ctx := context.Background()

	t.Run("successful callback processing with default concurrency", func(t *testing.T) {
		var processedAddresses []AddressLabelInfo
		var mu sync.Mutex
		callbackCount := 0

		callback := func(addresses []AddressLabelInfo) error {
			mu.Lock()
			defer mu.Unlock()
			processedAddresses = append(processedAddresses, addresses...)
			callbackCount++
			t.Logf("Callback called %d times with %d addresses", callbackCount, len(addresses))
			return nil
		}

		// Test with default concurrency (maxConcurrency = 0)
		totalProcessed, err := crawler.ProcessWalletDataWithCallback(ctx, testAddress, params, callback, 0)
		if err != nil {
			t.Fatalf("ProcessWalletDataWithCallback failed: %v", err)
		}

		if totalProcessed == 0 {
			t.Error("Expected to process at least one address")
		}

		if len(processedAddresses) != totalProcessed {
			t.Errorf("Expected %d processed addresses, got %d", totalProcessed, len(processedAddresses))
		}

		if callbackCount == 0 {
			t.Error("Callback should have been called at least once")
		}

		t.Logf("Successfully processed %d addresses with %d callback calls", totalProcessed, callbackCount)
	})

	t.Run("callback processing with custom concurrency", func(t *testing.T) {
		var processedAddresses []AddressLabelInfo
		var mu sync.Mutex
		callbackCount := 0

		callback := func(addresses []AddressLabelInfo) error {
			mu.Lock()
			defer mu.Unlock()
			processedAddresses = append(processedAddresses, addresses...)
			callbackCount++
			return nil
		}

		// Test with custom concurrency
		totalProcessed, err := crawler.ProcessWalletDataWithCallback(ctx, testAddress, params, callback, 5)
		if err != nil {
			t.Fatalf("ProcessWalletDataWithCallback failed: %v", err)
		}

		if totalProcessed == 0 {
			t.Error("Expected to process at least one address")
		}

		if len(processedAddresses) != totalProcessed {
			t.Errorf("Expected %d processed addresses, got %d", totalProcessed, len(processedAddresses))
		}

		t.Logf("Successfully processed %d addresses with custom concurrency", totalProcessed)
	})

	t.Run("callback processing with high concurrency capped", func(t *testing.T) {
		var processedAddresses []AddressLabelInfo
		var mu sync.Mutex

		callback := func(addresses []AddressLabelInfo) error {
			mu.Lock()
			defer mu.Unlock()
			processedAddresses = append(processedAddresses, addresses...)
			return nil
		}

		// Test with very high concurrency (should be capped at 50)
		totalProcessed, err := crawler.ProcessWalletDataWithCallback(ctx, testAddress, params, callback, 100)
		if err != nil {
			t.Fatalf("ProcessWalletDataWithCallback failed: %v", err)
		}

		if totalProcessed == 0 {
			t.Error("Expected to process at least one address")
		}

		t.Logf("Successfully processed %d addresses with capped concurrency", totalProcessed)
	})

	t.Run("callback error handling", func(t *testing.T) {
		callbackError := errors.New("callback processing error")
		callback := func(addresses []AddressLabelInfo) error {
			return callbackError
		}

		// Should fail due to callback error
		totalProcessed, err := crawler.ProcessWalletDataWithCallback(ctx, testAddress, params, callback, 2)

		if err == nil {
			t.Error("Expected error due to callback failure")
		}

		if !errors.Is(err, callbackError) && !contains(err.Error(), "callback failed") {
			t.Errorf("Expected callback error in error chain, got: %v", err)
		}

		t.Logf("Correctly handled callback error: %v, processed: %d", err, totalProcessed)
	})
}

// TestProcessCallback_Concurrency tests the concurrency parameter validation
func TestWalletExplorerCrawler_ProcessWalletDataWithCallback_Concurrency(t *testing.T) {
	config := WalletExplorerCrawlConfig{
		Config: bhttpclient.Config{
			Name: "concurrency-test",
		},
		MaxConcurrency: 3,
	}

	crawler, err := NewWalletExplorerCrawler(config)
	if err != nil {
		t.Fatalf("Failed to create crawler: %v", err)
	}

	params := BaseLabelInfo{
		Chain:      "BTC",
		Category:   "test",
		EntityName: "test_entity",
		DetailName: "test_entity_user",
		Editor:     "test",
		Source:     "unit_test",
		Remark:     "concurrency test",
		Valid:      1,
	}

	// Mock callback that just counts calls
	callback := func(addresses []AddressLabelInfo) error {
		return nil
	}

	testCases := []struct {
		name           string
		maxConcurrency int
		expectError    bool
		description    string
	}{
		{
			name:           "zero concurrency uses default",
			maxConcurrency: 0,
			expectError:    false,
			description:    "Should use config default when 0",
		},
		{
			name:           "negative concurrency uses default",
			maxConcurrency: -1,
			expectError:    false,
			description:    "Should use config default when negative",
		},
		{
			name:           "normal concurrency",
			maxConcurrency: 5,
			expectError:    false,
			description:    "Should use provided concurrency",
		},
		{
			name:           "high concurrency gets capped",
			maxConcurrency: 100,
			expectError:    false,
			description:    "Should cap at maximum allowed",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Use a non-existent address to avoid network calls in unit test
			// This will fail at getWalletID stage, which is fine for concurrency validation
			_, err := crawler.ProcessWalletDataWithCallback(
				context.Background(),
				"invalid-address",
				params,
				callback,
				tc.maxConcurrency,
			)

			// We expect this to fail due to invalid address, not concurrency issues
			// The test is about validating the concurrency parameter handling
			if tc.expectError && err == nil {
				t.Errorf("Expected error for test case: %s", tc.description)
			}

			t.Logf("Test case '%s': %s - handled correctly", tc.name, tc.description)
		})
	}
}

// TestProcessCallback_EdgeCases tests edge cases and error scenarios
func TestWalletExplorerCrawler_ProcessWalletDataWithCallback_EdgeCases(t *testing.T) {
	config := WalletExplorerCrawlConfig{
		Config: bhttpclient.Config{
			Name: "edge-case-test",
		},
	}

	crawler, err := NewWalletExplorerCrawler(config)
	if err != nil {
		t.Fatalf("Failed to create crawler: %v", err)
	}

	params := BaseLabelInfo{
		Chain:      "BTC",
		Category:   "test",
		EntityName: "test_entity",
		DetailName: "test_entity_user",
		Editor:     "test",
		Source:     "unit_test",
		Remark:     "edge case test",
		Valid:      1,
	}

	t.Run("invalid wallet address", func(t *testing.T) {
		callback := func(addresses []AddressLabelInfo) error {
			return nil
		}

		_, err := crawler.ProcessWalletDataWithCallback(
			context.Background(),
			"invalid-wallet-address",
			params,
			callback,
			2,
		)

		if err == nil {
			t.Error("Expected error for invalid wallet address")
		}

		if !contains(err.Error(), "failed to get wallet ID") {
			t.Errorf("Expected wallet ID error, got: %v", err)
		}
	})

	t.Run("context cancellation", func(t *testing.T) {
		callback := func(addresses []AddressLabelInfo) error {
			return nil
		}

		// Create a cancelled context
		ctx, cancel := context.WithCancel(context.Background())
		cancel()

		_, err := crawler.ProcessWalletDataWithCallback(
			ctx,
			"**********************************",
			params,
			callback,
			2,
		)

		if err == nil {
			t.Error("Expected error due to cancelled context")
		}
	})

	t.Run("context timeout", func(t *testing.T) {
		callback := func(addresses []AddressLabelInfo) error {
			// Simulate slow processing
			time.Sleep(100 * time.Millisecond)
			return nil
		}

		// Create a context with very short timeout
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
		defer cancel()

		_, err := crawler.ProcessWalletDataWithCallback(
			ctx,
			"**********************************",
			params,
			callback,
			2,
		)

		if err == nil {
			t.Error("Expected error due to context timeout")
		}
	})
}

// TestProcessCallback_CallbackDataIntegrity tests that callback receives correct data
func TestWalletExplorerCrawler_ProcessWalletDataWithCallback_CallbackDataIntegrity(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping data integrity test in short mode")
	}

	config := WalletExplorerCrawlConfig{
		Config: bhttpclient.Config{
			Name: "data-integrity-test",
		},
	}

	crawler, err := NewWalletExplorerCrawler(config)
	if err != nil {
		t.Fatalf("Failed to create crawler: %v", err)
	}

	testParams := BaseLabelInfo{
		Chain:      "BTC",
		Category:   "test",
		EntityName: "test_entity",
		DetailName: "test_entity_user",
		Editor:     "test",
		Source:     "unit_test",
		Remark:     "data integrity test",
		Valid:      1,
	}

	t.Run("callback receives correct parameters", func(t *testing.T) {
		var mu sync.Mutex

		callback := func(addresses []AddressLabelInfo) error {
			mu.Lock()
			defer mu.Unlock()

			// Validate each address has correct params
			for _, addr := range addresses {
				if addr.Chain != testParams.Chain {
					return fmt.Errorf("expected chain %s, got %s", testParams.Chain, addr.Chain)
				}
				if addr.Category != testParams.Category {
					return fmt.Errorf("expected category %s, got %s", testParams.Category, addr.Category)
				}
				if addr.EntityName != testParams.EntityName {
					return fmt.Errorf("expected entity name %s, got %s", testParams.EntityName, addr.EntityName)
				}
			}
			return nil
		}

		_, err := crawler.ProcessWalletDataWithCallback(
			context.Background(),
			"**********************************",
			testParams,
			callback,
			2,
		)

		if err != nil {
			t.Fatalf("ProcessWalletDataWithCallback failed: %v", err)
		}
	})
}

// Example function for the callback-based processing
func ExampleWalletExplorerCrawler_ProcessWalletDataWithCallback() {
	config := WalletExplorerCrawlConfig{
		Config: bhttpclient.Config{
			Name: "example",
		},
	}

	crawler, err := NewWalletExplorerCrawler(config)
	if err != nil {
		panic(err)
	}

	ctx := context.Background()
	walletAddress := "**********************************"

	params := BaseLabelInfo{
		Chain:      "BTC",
		Category:   "exchange",
		EntityName: "Binance.com",
		DetailName: "Binance.com_user",
		Editor:     "alex.gao",
		Source:     "bybit",
		Remark:     "AI Expanding",
		Valid:      1,
	}

	// Define callback function
	callback := func(addresses []AddressLabelInfo) error {
		return nil
	}

	// Process with default concurrency
	total, err := crawler.ProcessWalletDataWithCallback(ctx, walletAddress, params, callback, 0)
	if err != nil {
		return
	}

	fmt.Printf("Total addresses processed: %d\n", total)

	// Process with custom concurrency
	total, err = crawler.ProcessWalletDataWithCallback(ctx, walletAddress, params, callback, 5)
	if err != nil {
		return
	}

	fmt.Printf("Total addresses processed with custom concurrency: %d\n", total)
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			(len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					containsInMiddle(s, substr))))
}

func containsInMiddle(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
