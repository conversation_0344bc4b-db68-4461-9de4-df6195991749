package event

import (
	"context"
	"encoding/json"
	"time"

	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/kafka"

	kafkatool "aml-insight/internal/lib/kafka"
	"aml-insight/internal/model"
	"aml-insight/pkg/common"
)

// ----------------------------------------------------------------------------
// collection

const (
	riskGatewayProxyTopic = "aml_gateway_proxy" // 用于给 risk-aml-gateway proxy 发送消息
)

type (
	// MQBody 采用和工单一样的结构，未来可以随意扣除 event
	RiskGatewayProxyBody struct {
		T        int64  `json:"t,omitempty"`     // 秒级时间戳
		MQSource string `json:"ms,omitempty"`    // 消息来源
		Event    string `json:"event,omitempty"` // 事件类型; OnchainAMLStatus: 链上 AML 状态变更的事件
		Body     string `json:"body,omitempty"`  // 消息体，消费者自己解析
	}

	OnchainAMLStatusBody struct {
		MemberId  int64  `json:"memberId,omitempty"`
		RequestId string `json:"requestId,omitempty"`
		AMLFlow   int    `json:"aml_flow,omitempty"`
		Chain     string `json:"chain"`
		Coin      string `json:"coin"`
		TxHash    string `json:"txHash,omitempty"`
		TxIndex   int    `json:"txIndex,omitempty"`
	}

	event string
)

var (
	OnchainAMLStatusEvent  event = "OnchainAMLStatus"
	AdditionalKyaScanEvent event = "AdditionalKyaScan"
)

func DoSendRiskGatewayProxyMsg(ctx context.Context, fiatProducer kafka.Producer, sfCase *model.SfCase, txIndex int) {
	body := &OnchainAMLStatusBody{
		MemberId:  int64(sfCase.MemberId),
		RequestId: sfCase.RequestId,
		AMLFlow:   int(sfCase.AmlFlow),
		Chain:     sfCase.Chain,
		Coin:      sfCase.Country,
		TxHash:    sfCase.Txhash,
		TxIndex:   txIndex,
	}
	bytes, _ := json.Marshal(body)

	msg := &RiskGatewayProxyBody{
		T:        time.Now().Unix(),
		MQSource: "aml-insight",
		Event:    "OnchainAMLStatus",
		Body:     string(bytes),
	}
	msgBytes, _ := json.Marshal(msg)

	if err := fiatProducer.Send(ctx, &kafka.Message{
		Topic: riskGatewayProxyTopic,
		Value: msgBytes,
		Time:  time.Now(),
	}); err != nil {
		logc.Errorw(ctx, "DoSendRiskGatewayProxyMsg failed", logc.Field("err", kafkatool.ExtractPErr(err)),
			logc.Field("msg", msg), logc.Field("topic", riskGatewayProxyTopic))
	} else {
		logc.Infow(ctx, "DoSendRiskGatewayProxyMsg success", logc.Field("msg", msg),
			logc.Field("topic", riskGatewayProxyTopic))
	}
}

// ----------------------------------------------------------------------------
// asset

// AssetAmlStatus aml 传给 asset 的状态
type AssetAmlStatus struct {
	RequestId        string `json:"requestId,omitempty"`
	GroupId          string `json:"groupId,omitempty"`
	WorkOrderChannel string `json:"workOrderChannel,omitempty"`
	WebEddLink       string `json:"webEddLink,omitempty"`
	AppEddLink       string `json:"appEddLink,omitempty"`
	CaseStatus       string `json:"caseStatus,omitempty"`
}

// doSendNoticeToAsset 给资产发送消息 topic: aml_asset_edd_event
func DoSendNoticeToAsset(ctx context.Context, fiatProducer kafka.Producer,
	sfCase *model.SfCase, appealStatus string) {
	// 发送资产消息
	s := &AssetAmlStatus{
		RequestId:        sfCase.RequestId,
		GroupId:          sfCase.GroupId,
		WorkOrderChannel: sfCase.WorkOrderChannel,
		WebEddLink:       sfCase.WebEddLink,
		AppEddLink:       sfCase.AppEddLink,
		CaseStatus:       common.ConvertAssetCaseStatus(sfCase, appealStatus),
	}
	body, _ := json.Marshal(s)
	err := fiatProducer.Send(ctx, &kafka.Message{
		Topic: "aml_asset_edd_event",
		Value: body,
		Time:  time.Now(),
	})
	if err != nil {
		logc.Errorw(ctx, "sendNoticeToAsset failed", logc.Field("err", kafkatool.ExtractPErr(err)), logc.Field("msg", s), logc.Field("topic", "aml_asset_edd_event"))
	} else {
		logc.Infow(ctx, "sendNoticeToAsset success", logc.Field("msg", s), logc.Field("topic", "aml_asset_edd_event"))
	}
}

func DoSendToAMLGatewayProxyMsg(ctx context.Context, fiatProducer kafka.Producer, e event, msgBody string) error {
	msg := &RiskGatewayProxyBody{
		T:        time.Now().Unix(),
		MQSource: "aml-insight",
		Event:    string(e),
		Body:     msgBody,
	}
	msgBytes, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	if err = fiatProducer.Send(ctx, &kafka.Message{
		Topic: riskGatewayProxyTopic,
		Value: msgBytes,
		Time:  time.Now(),
	}); err != nil {
		logc.Errorw(ctx, "DoSendToAMLGatewayProxyMsg failed", logc.Field("err", kafkatool.ExtractPErr(err)),
			logc.Field("msg", msg), logc.Field("topic", riskGatewayProxyTopic))
		return err
	}
	logc.Infow(ctx, "DoSendToAMLGatewayProxyMsg success", logc.Field("msg", msg),
		logc.Field("topic", riskGatewayProxyTopic))
	return nil
}
