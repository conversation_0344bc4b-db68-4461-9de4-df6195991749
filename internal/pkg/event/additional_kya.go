package event

const (
	AdditionalKyaScanRequestFormat = "AS-%d-%s" // AS: Additional Scan, AS-01-requestId
)

type AdditionalKyaMsg struct {
	AdditionalKyaScanTaskId uint64           `json:"task_id"`
	RequestId               string           `json:"request_id"`
	OriginRequestId         string           `json:"origin_request_id"`
	ActionType              int32            `json:"action_type"`
	WithdrawTrigger         *WithdrawTrigger `json:"withdraw_trigger"`
	DepositTrigger          *DepositTrigger  `json:"deposit_trigger"`
}

// WithdrawTrigger copy from pb.go struct
type WithdrawTrigger struct {
	RequestId    string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	MemberId     uint64 `protobuf:"varint,2,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Chain        string `protobuf:"bytes,3,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin         string `protobuf:"bytes,4,opt,name=coin,proto3" json:"coin,omitempty"`
	FromAddress  string `protobuf:"bytes,5,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	ToAddress    string `protobuf:"bytes,6,opt,name=to_address,json=toAddress,proto3" json:"to_address,omitempty"`
	Tag          string `protobuf:"bytes,7,opt,name=tag,proto3" json:"tag,omitempty"`
	ActionType   int32  `protobuf:"varint,8,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
	Amount       string `protobuf:"bytes,9,opt,name=amount,proto3" json:"amount,omitempty"`
	CreateTime   int64  `protobuf:"varint,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	BrokerId     string `protobuf:"bytes,11,opt,name=broker_id,json=brokerId,proto3" json:"broker_id,omitempty"`
	SpendSubType string `protobuf:"bytes,12,opt,name=spend_sub_type,json=spendSubType,proto3" json:"spend_sub_type,omitempty"`
}

// DepositTrigger copy from pb.go struct
type DepositTrigger struct {
	RequestId   string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	MemberId    uint64 `protobuf:"varint,2,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Chain       string `protobuf:"bytes,3,opt,name=chain,proto3" json:"chain,omitempty"`
	Coin        string `protobuf:"bytes,4,opt,name=coin,proto3" json:"coin,omitempty"`
	FromAddress string `protobuf:"bytes,5,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	ToAddress   string `protobuf:"bytes,6,opt,name=to_address,json=toAddress,proto3" json:"to_address,omitempty"`
	Tag         string `protobuf:"bytes,7,opt,name=tag,proto3" json:"tag,omitempty"`
	ActionType  int32  `protobuf:"varint,8,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
	Amount      string `protobuf:"bytes,9,opt,name=amount,proto3" json:"amount,omitempty"`
	TxHash      string `protobuf:"bytes,10,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	TxIndex     string `protobuf:"bytes,11,opt,name=tx_index,json=txIndex,proto3" json:"tx_index,omitempty"`
	BlockHash   string `protobuf:"bytes,12,opt,name=block_hash,json=blockHash,proto3" json:"block_hash,omitempty"`
	State       string `protobuf:"bytes,13,opt,name=state,proto3" json:"state,omitempty"`
	BrokerId    string `protobuf:"bytes,14,opt,name=broker_id,json=brokerId,proto3" json:"broker_id,omitempty"`
}
