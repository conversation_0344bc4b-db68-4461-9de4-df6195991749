package rpc

import (
	"errors"
	"net/http"
	"testing"

	"github.com/go-resty/resty/v2"
)

func TestRetryCondition(t *testing.T) {
	err := errors.New("mock error")
	if !retryCondition(nil, err) {
		t.Error("error case should return true, but got false")
	}

	testCases := []struct {
		statusCode int
		expected   bool
		desc       string
	}{
		{http.StatusOK, false, "HTTP 200 OK"},
		{http.StatusBadRequest, true, "HTTP 400 Bad Request"},
		{http.StatusUnauthorized, false, "HTTP 401 Unauthorized"},
		{http.StatusTooManyRequests, true, "HTTP 429 Too Many Requests"},
		{http.StatusInternalServerError, true, "HTTP 500 Internal Server Error"},
		{http.StatusServiceUnavailable, true, "HTTP 503 Service Unavailable"},
		{http.StatusCreated, false, "HTTP 201 Created"},
	}

	for _, tc := range testCases {
		resp := &resty.Response{
			RawResponse: &http.Response{
				StatusCode: tc.statusCode,
			},
		}

		result := retryCondition(resp, nil)
		if result != tc.expected {
			t.<PERSON><PERSON><PERSON>("for %s: expected %v, got %v", tc.desc, tc.expected, result)
		}
	}
}
