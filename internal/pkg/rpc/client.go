package rpc

import (
	"net/http"
	"time"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/client/bhttpclient"
	"github.com/go-resty/resty/v2"
)

type Client struct {
	*bhttpclient.Client
}

type Config struct {
	bhttpclient.Config
	RetryCount       int    `json:",default=5"`
	RetryWaitTime    int    `json:",default=500"`
	RetryMaxWaitTime int    `json:",default=3000"`
	ContentType      string `json:",default=application/json"`
}

func NewClient(c Config) *Client {
	cli := bhttpclient.New(c.Config)
	cli.SetHeader("Content-Type", c.ContentType).
		SetRetryCount(c.RetryCount).
		AddRetryCondition(retryCondition).
		SetRetryWaitTime(time.Duration(c.RetryWaitTime) * time.Millisecond).
		SetRetryMaxWaitTime(time.Duration(c.RetryMaxWaitTime) * time.Millisecond)

	return &Client{cli}
}

func retryCondition(response *resty.Response, err error) bool {
	if err != nil {
		return true
	}

	switch response.StatusCode() {
	case http.StatusOK:
		return false
	case http.StatusBadRequest:
		return true
	case http.StatusUnauthorized:
		return false
	case http.StatusTooManyRequests:
		return true
	case http.StatusInternalServerError:
		return true
	case http.StatusServiceUnavailable:
		return true
	default:
		return false
	}
}
