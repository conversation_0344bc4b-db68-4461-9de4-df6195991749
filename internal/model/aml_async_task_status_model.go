package model

import (
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"context"
	"fmt"
)

var _ AmlAsyncTaskStatusModel = (*customAmlAsyncTaskStatusModel)(nil)

type (
	// AmlAsyncTaskStatusModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAmlAsyncTaskStatusModel.
	AmlAsyncTaskStatusModel interface {
		amlAsyncTaskStatusModel
		InsertOrUpdate(ctx context.Context, data *AmlAsyncTaskStatus) error
	}

	customAmlAsyncTaskStatusModel struct {
		*defaultAmlAsyncTaskStatusModel
	}
)

const (
	AmlAsyncTaskStatusRunning = 1
	AmlAsyncTaskStatusDone    = 0
)

// MustNewAmlAsyncTaskStatusModel returns a model for the database table.
func MustNewAmlAsyncTaskStatusModel(conf sqlx.Config, opts ...sqlx.SqlOption) AmlAsyncTaskStatusModel {
	return &customAmlAsyncTaskStatusModel{
		defaultAmlAsyncTaskStatusModel: mustNewAmlAsyncTaskStatusModel(conf.MustNewMysql(opts...)),
	}
}

func (m *customAmlAsyncTaskStatusModel) InsertOrUpdate(ctx context.Context, data *AmlAsyncTaskStatus) error {
	args := []any{data.Key,
		data.ExtendedInfo, data.Status, data.Valid,
		data.ExtendedInfo, data.Status, data.Valid}

	stmt := fmt.Sprintf(`insert into aml_async_task_status (%s) values (?, ?, ?, ?) 
on DUPLICATE KEY update extended_info = ?, status = ?, valid = ?`, amlAsyncTaskStatusRowsExpectAutoSet)
	_, err := m.conn.ExecCtx(ctx, stmt, args...)
	return err
}
