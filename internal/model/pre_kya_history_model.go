package model

import (
	"aml-insight/internal/pkg/prekya"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"context"
	"fmt"
	"strings"
	"time"
)

var _ PreKyaHistoryModel = (*customPreKyaHistoryModel)(nil)

type (
	// PreKyaHistoryModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPreKyaHistoryModel.
	PreKyaHistoryModel interface {
		preKyaHistoryModel
		QueryKyaHistoryByTime(ctx context.Context, memberId uint64, startTime time.Time, endTime time.Time, address string, result string, limit, offset int32) ([]*PreKyaHistory, error)
		CountKyaHistoryResultIn(ctx context.Context, memberId uint64, startTime time.Time, endTime time.Time, address string, result []string) (int32, error)
	}

	customPreKyaHistoryModel struct {
		*defaultPreKyaHistoryModel
	}
)

const (
	PreKyaStatusPending = 1
	PreKyaStatusSuccess = 2
)

// MustNewPreKyaHistoryModel returns a model for the database table.
func MustNewPreKyaHistoryModel(conf sqlx.Config, opts ...sqlx.SqlOption) PreKyaHistoryModel {
	return &customPreKyaHistoryModel{
		defaultPreKyaHistoryModel: mustNewPreKyaHistoryModel(conf.MustNewMysql(opts...)),
	}
}

func (m *customPreKyaHistoryModel) QueryKyaHistoryByTime(ctx context.Context, memberId uint64, startTime time.Time, endTime time.Time, address string, result string, limit, offset int32) ([]*PreKyaHistory, error) {
	var (
		resp         = make([]*PreKyaHistory, 0)
		whereClauses []string
		args         []any
	)

	whereClauses = append(whereClauses, "member_id = ?")
	whereClauses = append(whereClauses, "created_at BETWEEN ? AND ?")
	whereClauses = append(whereClauses, "status = ?")
	args = append(args, memberId)
	args = append(args, startTime, endTime)
	args = append(args, PreKyaStatusSuccess)
	if len(address) > 0 {
		whereClauses = append(whereClauses, "address = ?")
		args = append(args, address)
	}
	if len(result) > 0 {
		whereClauses = append(whereClauses, "result = ?")
		args = append(args, result)
	}

	query := fmt.Sprintf("SELECT %s FROM %s", preKyaHistoryRows, m.table)
	query += " WHERE " + strings.Join(whereClauses, " AND ")
	query += " ORDER BY created_at DESC"

	if limit > 0 {
		query += fmt.Sprintf(" LIMIT ? OFFSET ?")
		args = append(args, limit, offset)
	}

	if err := m.conn.QueryRowsCtx(ctx, &resp, query, args...); err != nil {
		return nil, err
	}

	return resp, nil
}

func (m *customPreKyaHistoryModel) CountKyaHistoryResultIn(ctx context.Context, memberId uint64, startTime time.Time, endTime time.Time, address string, results []string) (int32, error) {
	// 检查时间参数是否有效
	if startTime.IsZero() || endTime.IsZero() {
		endTime = time.Now().UTC()
		startTime = endTime.AddDate(0, 1, -30).UTC()
	}
	if endTime.Sub(startTime) > time.Hour*24*180 {
		endTime = startTime.AddDate(0, 0, 180).UTC()
	}

	var (
		count        int32
		whereClauses []string
		args         []any
	)

	whereClauses = append(whereClauses, "member_id = ?")
	whereClauses = append(whereClauses, "created_at BETWEEN ? AND ?")
	whereClauses = append(whereClauses, "status = ?")
	args = append(args, memberId)
	args = append(args, startTime, endTime)
	args = append(args, PreKyaStatusSuccess)

	if len(address) > 0 {
		whereClauses = append(whereClauses, "address = ?")
		args = append(args, address)
	}

	if len(results) > 0 {
		inClause := make([]string, 0)
		for _, r := range results {
			if len(r) == 0 {
				continue
			}
			inClause = append(inClause, "?")
			args = append(args, r)
		}
		if len(inClause) > 0 {
			whereClauses = append(whereClauses, fmt.Sprintf("result IN (%s)", strings.Join(inClause, ",")))
		}
	}

	queryCount := fmt.Sprintf("SELECT COUNT(id) FROM %s", m.table)
	queryCount += " WHERE " + strings.Join(whereClauses, " AND ")

	if err := m.conn.QueryRowCtx(ctx, &count, queryCount, args...); err != nil {
		return 0, err
	}

	return count, nil
}

func (h *PreKyaHistory) NoError() bool {
	if h.Status == PreKyaStatusSuccess && (h.Result == prekya.PreKyaResultNoRisk || h.Result == prekya.PreKyaResultLowRisk || h.Result == prekya.PreKyaResultHighRisk) {
		return true
	}
	return false
}
