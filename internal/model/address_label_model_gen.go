// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	addressLabelFieldNames          = builder.RawFieldNames(&AddressLabel{})
	addressLabelRows                = strings.Join(addressLabelFieldNames, ",")
	addressLabelRowsExpectAutoSet   = strings.Join(stringx.Remove(addressLabelFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	addressLabelRowsWithPlaceHolder = strings.Join(stringx.Remove(addressLabelFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	addressLabelModel interface {
		Insert(ctx context.Context, data *AddressLabel) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*AddressLabel, error)
		FindOneByAddressChain(ctx context.Context, address string, chain string) (*AddressLabel, error)
		Update(ctx context.Context, newData *AddressLabel) error
	}

	defaultAddressLabelModel struct {
		conn  sqlx.SqlConn
		table string
	}

	AddressLabel struct {
		Id         uint64    `db:"id" json:"id"`                   // 兼容字段
		Chain      string    `db:"chain" json:"chain"`             // 链
		Address    string    `db:"address" json:"address"`         // 查询的地址
		Category   string    `db:"category" json:"category"`       // 标签
		EntityName string    `db:"entity_name" json:"entity_name"` // 地址所属控制实体的标识
		DetailName string    `db:"detail_name" json:"detail_name"` // 地址详情列表
		Editor     string    `db:"editor" json:"editor"`           // 查询的地址
		Source     string    `db:"source" json:"source"`           // 标签来源
		Remark     string    `db:"remark" json:"remark"`           // 标签备注
		Valid      int32     `db:"valid" json:"valid"`             // 是否有效【0:标签无效，1:标签有效; 如果对标签删除，可以批量对地址进行valid字段置为0】
		CreatedAt  time.Time `db:"created_at" json:"created_at"`   // 创建时间
		UpdatedAt  time.Time `db:"updated_at" json:"updated_at"`   // 更新时间
	}
)

func mustNewAddressLabelModel(conn sqlx.SqlConn) *defaultAddressLabelModel {
	return &defaultAddressLabelModel{
		conn:  conn,
		table: "`address_label`",
	}
}

func (m *defaultAddressLabelModel) FindOne(ctx context.Context, id uint64) (*AddressLabel, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", addressLabelRows, m.table)
	var resp AddressLabel
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAddressLabelModel) FindOneByAddressChain(ctx context.Context, address string, chain string) (*AddressLabel, error) {
	var resp AddressLabel
	query := fmt.Sprintf("select %s from %s where `address` = ? and `chain` = ? limit 1", addressLabelRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, address, chain)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAddressLabelModel) Insert(ctx context.Context, data *AddressLabel) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, addressLabelRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Chain, data.Address, data.Category, data.EntityName, data.DetailName, data.Editor, data.Source, data.Remark, data.Valid)
	return ret, err
}

func (m *defaultAddressLabelModel) Update(ctx context.Context, newData *AddressLabel) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, addressLabelRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Chain, newData.Address, newData.Category, newData.EntityName, newData.DetailName, newData.Editor, newData.Source, newData.Remark, newData.Valid, newData.Id)
	return err
}

func (m *defaultAddressLabelModel) tableName() string {
	return m.table
}
