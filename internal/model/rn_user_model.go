package model

import (
	"code.bydev.io/frameworks/byone/core/stores/cache"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
)

var _ RnUserModel = (*customRnUserModel)(nil)

type (
	// RnUserModel is an interface to be customized, add more methods here,
	// and implement the added methods in customRnUserModel.
	RnUserModel interface {
		rnUserModel
	}

	customRnUserModel struct {
		*defaultRnUserModel
	}
)

// MustNewRnUserModel returns a model for the database table.
func MustNewRnUserModel(conf sqlx.Config, c cache.CacheConf, opts ...sqlx.SqlOption) RnUserModel {
	conn := conf.MustNewMysql(opts...)
	return &customRnUserModel{
		defaultRnUserModel: mustNewRnUserModel(sqlc.NewConn(conn, c)),
	}
}
