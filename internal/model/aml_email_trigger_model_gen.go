// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	amlEmailTriggerFieldNames          = builder.RawFieldNames(&AmlEmailTrigger{})
	amlEmailTriggerRows                = strings.Join(amlEmailTriggerFieldNames, ",")
	amlEmailTriggerRowsExpectAutoSet   = strings.Join(stringx.Remove(amlEmailTriggerFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	amlEmailTriggerRowsWithPlaceHolder = strings.Join(stringx.Remove(amlEmailTriggerFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	amlEmailTriggerModel interface {
		Insert(ctx context.Context, data *AmlEmailTrigger) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*AmlEmailTrigger, error)
		Update(ctx context.Context, newData *AmlEmailTrigger) error
	}

	defaultAmlEmailTriggerModel struct {
		conn  sqlx.SqlConn
		table string
	}

	AmlEmailTrigger struct {
		Id                 uint64 `db:"id"`                    // id
		TriggerLabel       string `db:"trigger_label"`         // trigger_label
		IsBrokerSubAccount string `db:"is_broker_sub_account"` // is_broker_sub_account
		FirstEmailTemplate string `db:"first_email_template"`  // first_email_template
		Status             int32  `db:"status"`                // status
	}
)

func mustNewAmlEmailTriggerModel(conn sqlx.SqlConn) *defaultAmlEmailTriggerModel {
	return &defaultAmlEmailTriggerModel{
		conn:  conn,
		table: "`aml_email_trigger`",
	}
}

func (m *defaultAmlEmailTriggerModel) FindOne(ctx context.Context, id uint64) (*AmlEmailTrigger, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", amlEmailTriggerRows, m.table)
	var resp AmlEmailTrigger
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAmlEmailTriggerModel) Insert(ctx context.Context, data *AmlEmailTrigger) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, amlEmailTriggerRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.TriggerLabel, data.IsBrokerSubAccount, data.FirstEmailTemplate, data.Status)
	return ret, err
}

func (m *defaultAmlEmailTriggerModel) Update(ctx context.Context, data *AmlEmailTrigger) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, amlEmailTriggerRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.TriggerLabel, data.IsBrokerSubAccount, data.FirstEmailTemplate, data.Status, data.Id)
	return err
}

func (m *defaultAmlEmailTriggerModel) tableName() string {
	return m.table
}
