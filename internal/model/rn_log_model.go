package model

import "code.bydev.io/frameworks/byone/core/stores/sqlx"

var _ RnLogModel = (*customRnLogModel)(nil)

var (
	RNLogMethodCreateUser  = "CreateUser"
	RNLogMethodTransaction = "Transaction"
	RNLogMethodWebhook     = "Webhook"
)

type (
	// RnLogModel is an interface to be customized, add more methods here,
	// and implement the added methods in customRnLogModel.
	RnLogModel interface {
		rnLogModel
	}

	customRnLogModel struct {
		*defaultRnLogModel
	}
)

// MustNewRnLogModel returns a model for the database table.
func MustNewRnLogModel(conf sqlx.Config, opts ...sqlx.SqlOption) RnLogModel {
	return &customRnLogModel{
		defaultRnLogModel: mustNewRnLogModel(conf.MustNewMysql(opts...)),
	}
}
