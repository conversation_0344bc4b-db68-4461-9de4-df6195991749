package model

import (
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"context"
	"database/sql"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestQueryByIdOrCategory(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAmlHitCategoryModel{
			defaultAmlHitCategoryModel: mustNewAmlHitCategoryModel(conn),
		}

		// 模拟查询结果：id 和 category 都有条件
		mock.ExpectQuery("SELECT (.+)").
			WithArgs(int64(1), "testCategory", 10, 0).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "provider", "category", "entity", "action_type", "exposure_type",
				"risk_level", "risk_level_score", "status", "created_at", "updated_at",
				"category_id", "editor", "remark",
			}).AddRow(
				1, "provider1", "testCategory", "entity1", 1, "direct", "high", 80, 1, time.Now(), time.Now(), "catId1", "editor1", "remark1",
			))

		// 调用方法
		rows, err := m.QueryByIdOrCategory(context.Background(), 1, "testCategory", 10, 1)
		assert.NoError(t, err)
		assert.Len(t, rows, 1)
		assert.Equal(t, uint64(1), rows[0].Id)
		assert.Equal(t, "provider1", rows[0].Provider)
		assert.Equal(t, "testCategory", rows[0].Category)
		assert.Equal(t, "entity1", rows[0].Entity)
		assert.Equal(t, int32(1), rows[0].ActionType)
		assert.Equal(t, "direct", rows[0].ExposureType)
		assert.Equal(t, "high", rows[0].RiskLevel)
		assert.Equal(t, int32(80), rows[0].RiskLevelScore)
		assert.Equal(t, int32(1), rows[0].Status)
		assert.Equal(t, "catId1", rows[0].CategoryId)
		assert.Equal(t, "editor1", rows[0].Editor)
		assert.Equal(t, "remark1", rows[0].Remark)

		// 确保所有期望被满足
		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})

	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAmlHitCategoryModel{
			defaultAmlHitCategoryModel: mustNewAmlHitCategoryModel(conn),
		}

		// 模拟查询结果：id 为 0 时只查询 category
		mock.ExpectQuery("SELECT (.+)").
			WithArgs("testCategory", 10, 0).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "provider", "category", "entity", "action_type", "exposure_type",
				"risk_level", "risk_level_score", "status", "created_at", "updated_at",
				"category_id", "editor", "remark",
			}).AddRow(
				2, "provider2", "testCategory", "entity2", 2, "indirect", "medium", 60, 1, time.Now(), time.Now(), "catId2", "editor2", "remark2",
			))

		// 调用方法
		rows, err := m.QueryByIdOrCategory(context.Background(), 0, "testCategory", 10, 1)
		assert.NoError(t, err)
		assert.Len(t, rows, 1)
		assert.Equal(t, uint64(2), rows[0].Id)
		assert.Equal(t, "provider2", rows[0].Provider)
		assert.Equal(t, "testCategory", rows[0].Category)
		assert.Equal(t, "entity2", rows[0].Entity)
		assert.Equal(t, int32(2), rows[0].ActionType)
		assert.Equal(t, "indirect", rows[0].ExposureType)
		assert.Equal(t, "medium", rows[0].RiskLevel)
		assert.Equal(t, int32(60), rows[0].RiskLevelScore)
		assert.Equal(t, int32(1), rows[0].Status)
		assert.Equal(t, "catId2", rows[0].CategoryId)
		assert.Equal(t, "editor2", rows[0].Editor)
		assert.Equal(t, "remark2", rows[0].Remark)

		// 确保所有期望被满足
		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})
}
func TestAmlHitCategoryModelFindOne(t *testing.T) {
	// 使用 sqlmock 创建一个 mock 数据库连接
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("could not mock db: %v", err)
	}
	defer db.Close()

	conn := sqlx.NewSqlConnFromDB(db)
	m := &defaultAmlHitCategoryModel{
		conn:  conn,
		table: "`aml_hit_category`",
	}

	// 模拟查询，假设返回 id 为 1 的记录
	mock.ExpectQuery("select (.+)").
		WithArgs(uint64(1)).
		WillReturnRows(sqlmock.NewRows([]string{
			"id", "provider", "category", "entity", "action_type", "exposure_type", "risk_level", "risk_level_score",
			"status", "created_at", "updated_at", "category_id", "editor", "remark",
		}).AddRow(
			1, "Provider1", "Category1", "Entity1", 1, "direct", "high", 80, 1, time.Now(), time.Now(), "CatID1", "Editor1", "Remark1",
		))

	// 调用 FindOne 方法
	result, err := m.FindOne(context.Background(), 1)

	// 断言结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, uint64(1), result.Id)
	assert.Equal(t, "Provider1", result.Provider)

	// 验证期望
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestAmlHitCategoryModelInsert(t *testing.T) {
	// 使用 sqlmock 创建一个 mock 数据库连接
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("could not mock db: %v", err)
	}
	defer db.Close()

	conn := sqlx.NewSqlConnFromDB(db)
	m := &defaultAmlHitCategoryModel{
		conn:  conn,
		table: "`aml_hit_category`",
	}

	// 模拟插入操作
	mock.ExpectExec("insert (.+)").
		WithArgs("Provider1", "Category1", "Entity1", 1, "direct", "high", 80, 1, "CatID1", "Editor1", "Remark1").
		WillReturnResult(sqlmock.NewResult(1, 1))

	// 调用 Insert 方法
	data := &AmlHitCategory{
		Provider:       "Provider1",
		Category:       "Category1",
		Entity:         "Entity1",
		ActionType:     1,
		ExposureType:   "direct",
		RiskLevel:      "high",
		RiskLevelScore: 80,
		Status:         1,
		CategoryId:     "CatID1",
		Editor:         "Editor1",
		Remark:         "Remark1",
	}
	_, err = m.Insert(context.Background(), data)

	// 断言结果
	assert.NoError(t, err)

	// 验证期望
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestAmlHitCategoryModelUpdate(t *testing.T) {
	// 使用 sqlmock 创建一个 mock 数据库连接
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("could not mock db: %v", err)
	}
	defer db.Close()

	conn := sqlx.NewSqlConnFromDB(db)
	m := &defaultAmlHitCategoryModel{
		conn:  conn,
		table: "`aml_hit_category`",
	}

	// 模拟更新操作
	mock.ExpectExec("update (.+)").
		WithArgs("Provider1", "Category1", "Entity1", 1, "direct", "high", 80, 1, "CatID1", "Editor1", "Remark1", uint64(1)).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// 调用 Update 方法
	data := &AmlHitCategory{
		Id:             1,
		Provider:       "Provider1",
		Category:       "Category1",
		Entity:         "Entity1",
		ActionType:     1,
		ExposureType:   "direct",
		RiskLevel:      "high",
		RiskLevelScore: 80,
		Status:         1,
		CategoryId:     "CatID1",
		Editor:         "Editor1",
		Remark:         "Remark1",
	}
	err = m.Update(context.Background(), data)

	// 断言结果
	assert.NoError(t, err)

	// 验证期望
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)
}
