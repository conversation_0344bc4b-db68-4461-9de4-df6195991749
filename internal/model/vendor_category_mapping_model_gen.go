// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	vendorCategoryMappingFieldNames          = builder.RawFieldNames(&VendorCategoryMapping{})
	vendorCategoryMappingRows                = strings.Join(vendorCategoryMappingFieldNames, ",")
	vendorCategoryMappingRowsExpectAutoSet   = strings.Join(stringx.Remove(vendorCategoryMappingFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	vendorCategoryMappingRowsWithPlaceHolder = strings.Join(stringx.Remove(vendorCategoryMappingFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	vendorCategoryMappingModel interface {
		Insert(ctx context.Context, data *VendorCategoryMapping) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VendorCategoryMapping, error)
		Update(ctx context.Context, newData *VendorCategoryMapping) error
	}

	defaultVendorCategoryMappingModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VendorCategoryMapping struct {
		Id          uint64    `db:"id"`           // id
		Vendor      string    `db:"vendor"`       // 供应商
		CategoryKey string    `db:"category_key"` // 映射的 key
		CategoryVal string    `db:"category_val"` // 映射的 val
		Valid       int32     `db:"valid"`        // 是否有效【0:无效，1:有效
		Editor      string    `db:"editor"`       // 最后一次修改的用户
		Remark      string    `db:"remark"`       // 备注
		CreatedAt   time.Time `db:"created_at"`   // 创建时间
		UpdatedAt   time.Time `db:"updated_at"`   // 更新时间
	}
)

func mustNewVendorCategoryMappingModel(conn sqlx.SqlConn) *defaultVendorCategoryMappingModel {
	return &defaultVendorCategoryMappingModel{
		conn:  conn,
		table: "`vendor_category_mapping`",
	}
}

func (m *defaultVendorCategoryMappingModel) FindOne(ctx context.Context, id uint64) (*VendorCategoryMapping, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vendorCategoryMappingRows, m.table)
	var resp VendorCategoryMapping
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVendorCategoryMappingModel) Insert(ctx context.Context, data *VendorCategoryMapping) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?)", m.table, vendorCategoryMappingRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Vendor, data.CategoryKey, data.CategoryVal, data.Valid, data.Editor, data.Remark)
	return ret, err
}

func (m *defaultVendorCategoryMappingModel) Update(ctx context.Context, data *VendorCategoryMapping) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vendorCategoryMappingRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Vendor, data.CategoryKey, data.CategoryVal, data.Valid, data.Editor, data.Remark, data.Id)
	return err
}

func (m *defaultVendorCategoryMappingModel) tableName() string {
	return m.table
}
