package model

import (
	"context"
	"database/sql"
	"errors"
	"testing"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestSyncAddressLabelModel_FindAllChain(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)

			m := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			// Mock the query result
			mock.ExpectQuery("SELECT DISTINCT `chain_type` FROM `sync_address_label`").
				WillReturnRows(sqlmock.NewRows([]string{"chain_type"}).
					AddRow("chain1").
					AddRow("chain2").
					AddRow("chain3"))

			// Call the method under test
			chains, err := m.<PERSON>(context.Background())
			assert.NoError(t, err)
			assert.Len(t, chains, 3)
			assert.Equal(t, []string{"chain1", "chain2", "chain3"}, chains)
		})
	})

	t.Run("empty_result", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)

			m := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			// Mock empty result
			mock.ExpectQuery("SELECT DISTINCT `chain_type` FROM `sync_address_label`").
				WillReturnRows(sqlmock.NewRows([]string{"chain_type"}))

			// Call the method under test
			chains, err := m.FindAllChain(context.Background())
			assert.NoError(t, err)
			assert.Empty(t, chains)
		})
	})

	t.Run("db_error", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)

			m := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			// Mock database error
			mock.ExpectQuery("SELECT DISTINCT `chain_type` FROM `sync_address_label`").
				WillReturnError(sql.ErrConnDone)

			// Call the method under test
			chains, err := m.FindAllChain(context.Background())
			assert.Error(t, err)
			assert.Nil(t, chains)
			assert.Contains(t, err.Error(), "FindAllChain error")
		})
	})
}

func TestSyncAddressLabelModel_FindByChain(t *testing.T) {
	t.Run("success_with_pagination", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			mock.ExpectQuery("SELECT \\* FROM `sync_address_label` WHERE `chain_type` = \\? ORDER BY id DESC LIMIT \\? OFFSET \\?").
				WithArgs("chain1", int32(10), int32(0)).
				WillReturnRows(sqlmock.NewRows([]string{"id", "chain_type", "address", "is_black", "tx_type", "created_at", "updated_at"}).
					AddRow(1, "chain1", "address1", 0, 1, time.Now(), time.Now()).
					AddRow(2, "chain1", "address2", 0, 2, time.Now(), time.Now()))

			labels, err := model.FindByChain(context.Background(), "chain1", 10, 1)
			assert.NoError(t, err)
			assert.Len(t, labels, 2)
			assert.Equal(t, "chain1", labels[0].ChainType)
			assert.Equal(t, "address1", labels[0].Address)
		})
	})

	t.Run("empty_chain", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			labels, err := model.FindByChain(context.Background(), "", 10, 1)
			assert.Error(t, err)
			assert.Nil(t, labels)
		})
	})

	t.Run("db_error", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			mock.ExpectQuery("SELECT \\* FROM `sync_address_label` WHERE `chain_type` = \\? ORDER BY id DESC LIMIT \\? OFFSET \\?").
				WithArgs("chain1", int32(10), int32(0)).
				WillReturnError(errors.New("db error"))

			labels, err := model.FindByChain(context.Background(), "chain1", 10, 1)
			assert.Error(t, err)
			assert.Nil(t, labels)
		})
	})
}

func TestSyncAddressLabelModel_CountByChain(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)

			m := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			// Mock the query result
			mock.ExpectQuery("SELECT COUNT\\(\\*\\) FROM `sync_address_label` WHERE `chain_type` = \\?").
				WithArgs("chain1").
				WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(5))

			// Call the method under test
			count, err := m.CountByChain(context.Background(), "chain1")
			assert.NoError(t, err)
			assert.Equal(t, int64(5), count)
		})
	})

	t.Run("zero_count", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)

			m := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			// Mock the query result
			mock.ExpectQuery("SELECT COUNT\\(\\*\\) FROM `sync_address_label` WHERE `chain_type` = \\?").
				WithArgs("chain1").
				WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

			// Call the method under test
			count, err := m.CountByChain(context.Background(), "chain1")
			assert.NoError(t, err)
			assert.Equal(t, int64(0), count)
		})
	})

	t.Run("db_error", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)

			m := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			// Mock database error
			mock.ExpectQuery("SELECT COUNT\\(\\*\\) FROM `sync_address_label` WHERE `chain_type` = \\?").
				WithArgs("chain1").
				WillReturnError(sql.ErrConnDone)

			// Call the method under test
			count, err := m.CountByChain(context.Background(), "chain1")
			assert.Error(t, err)
			assert.Equal(t, int64(0), count)
			assert.Contains(t, err.Error(), "CountByChain error")
		})
	})
}

func TestSyncAddressLabelModel_FindAllByChain(t *testing.T) {
	t.Run("less_than_limit", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			mock.ExpectQuery("SELECT COUNT\\(\\*\\) FROM `sync_address_label` WHERE `chain_type` = \\?").
				WithArgs("chain1").
				WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))

			mock.ExpectQuery("SELECT \\* FROM `sync_address_label` WHERE `chain_type` = \\?").
				WithArgs("chain1").
				WillReturnRows(sqlmock.NewRows([]string{"id", "chain_type", "address", "is_black", "tx_type", "created_at", "updated_at"}).
					AddRow(1, "chain1", "addr1", 0, 1, time.Now(), time.Now()).
					AddRow(2, "chain1", "addr2", 0, 2, time.Now(), time.Now()))

			labels, err := model.FindAllByChain(context.Background(), "chain1")
			assert.NoError(t, err)
			assert.Len(t, labels, 2)
			assert.Equal(t, "chain1", labels[0].ChainType)
			assert.Equal(t, "addr1", labels[0].Address)
		})
	})

	t.Run("more_than_limit", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			mock.ExpectQuery("SELECT COUNT\\(\\*\\) FROM `sync_address_label` WHERE `chain_type` = \\?").
				WithArgs("chain1").
				WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1001))

			mock.ExpectQuery("SELECT \\* FROM `sync_address_label` WHERE `chain_type` = \\? ORDER BY id DESC LIMIT \\? OFFSET \\?").
				WithArgs("chain1", int32(1000), int32(0)).
				WillReturnRows(sqlmock.NewRows([]string{"id", "chain_type", "address", "is_black", "tx_type", "created_at", "updated_at"}).
					AddRow(1, "chain1", "addr1", 0, 1, time.Now(), time.Now()))

			mock.ExpectQuery("SELECT \\* FROM `sync_address_label` WHERE `chain_type` = \\? ORDER BY id DESC LIMIT \\? OFFSET \\?").
				WithArgs("chain1", int32(1000), int32(1000)).
				WillReturnRows(sqlmock.NewRows([]string{"id", "chain_type", "address", "is_black", "tx_type", "created_at", "updated_at"}).
					AddRow(2, "chain1", "addr2", 0, 2, time.Now(), time.Now()))

			labels, err := model.FindAllByChain(context.Background(), "chain1")
			assert.NoError(t, err)
			assert.Len(t, labels, 2)
			assert.Equal(t, "chain1", labels[0].ChainType)
			assert.Equal(t, "addr1", labels[0].Address)
		})
	})

	t.Run("count_error", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			mock.ExpectQuery("SELECT COUNT\\(\\*\\) FROM `sync_address_label` WHERE `chain_type` = \\?").
				WithArgs("chain1").
				WillReturnError(sql.ErrConnDone)

			labels, err := model.FindAllByChain(context.Background(), "chain1")
			assert.Error(t, err)
			assert.Nil(t, labels)
			assert.Contains(t, err.Error(), "CountByChain error")
		})
	})

	t.Run("find_error", func(t *testing.T) {
		RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customSyncAddressLabelModel{
				defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conn),
			}

			mock.ExpectQuery("SELECT COUNT\\(\\*\\) FROM `sync_address_label` WHERE `chain_type` = \\?").
				WithArgs("chain1").
				WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))

			mock.ExpectQuery("SELECT \\* FROM `sync_address_label` WHERE `chain_type` = \\?").
				WithArgs("chain1").
				WillReturnError(sql.ErrConnDone)

			labels, err := model.FindAllByChain(context.Background(), "chain1")
			assert.Error(t, err)
			assert.Nil(t, labels)
		})
	})
}
