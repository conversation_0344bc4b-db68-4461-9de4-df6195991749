// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	tokenTransactionFieldNames          = builder.RawFieldNames(&TokenTransaction{})
	tokenTransactionRows                = strings.Join(tokenTransactionFieldNames, ",")
	tokenTransactionRowsExpectAutoSet   = strings.Join(stringx.Remove(tokenTransactionFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	tokenTransactionRowsWithPlaceHolder = strings.Join(stringx.Remove(tokenTransactionFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	tokenTransactionModel interface {
		Insert(ctx context.Context, data *TokenTransaction) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*TokenTransaction, error)
		FindOneByTransactionHash(ctx context.Context, transactionHash string) (*TokenTransaction, error)
		Update(ctx context.Context, newData *TokenTransaction) error
	}

	defaultTokenTransactionModel struct {
		conn  sqlx.SqlConn
		table string
	}

	TokenTransaction struct {
		Id                   uint64    `db:"id"`                     // 主键id
		TransactionHash      string    `db:"transaction_hash"`       // 交易哈希
		BlockHash            string    `db:"block_hash"`             // 区块哈希
		ChainType            string    `db:"chain_type"`             // 区块链网络标识
		Address              string    `db:"address"`                // 查询的地址
		Height               int64     `db:"height"`                 // 区块高度
		TransactionTime      int64     `db:"transaction_time"`       // 交易时间戳(毫秒)
		FromAddress          string    `db:"from_address"`           // 发送方地址
		ToAddress            string    `db:"to_address"`             // 接收方地址
		IsFromContract       int16     `db:"is_from_contract"`       // 发送方是否合约地址
		IsToContract         int16     `db:"is_to_contract"`         // 接收方是否合约地址
		TokenContractAddress string    `db:"token_contract_address"` // 代币合约地址
		Amount               string    `db:"amount"`                 // 转账金额(支持大数字和小数)
		TokenSymbol          string    `db:"token_symbol"`           // 代币符号
		TokenId              string    `db:"token_id"`               // NFT代币ID(支持大数字)
		CreatedAt            time.Time `db:"created_at"`             // 创建时间
		UpdatedAt            time.Time `db:"updated_at"`             // 更新时间
	}
)

func mustNewTokenTransactionModel(conn sqlx.SqlConn) *defaultTokenTransactionModel {
	return &defaultTokenTransactionModel{
		conn:  conn,
		table: "`token_transaction`",
	}
}

func (m *defaultTokenTransactionModel) FindOne(ctx context.Context, id uint64) (*TokenTransaction, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", tokenTransactionRows, m.table)
	var resp TokenTransaction
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTokenTransactionModel) FindOneByTransactionHash(ctx context.Context, transactionHash string) (*TokenTransaction, error) {
	var resp TokenTransaction
	query := fmt.Sprintf("select %s from %s where `transaction_hash` = ? limit 1", tokenTransactionRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, transactionHash)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTokenTransactionModel) Insert(ctx context.Context, data *TokenTransaction) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, tokenTransactionRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.TransactionHash, data.BlockHash, data.ChainType, data.Address, data.Height, data.TransactionTime, data.FromAddress, data.ToAddress, data.IsFromContract, data.IsToContract, data.TokenContractAddress, data.Amount, data.TokenSymbol, data.TokenId)
	return ret, err
}

func (m *defaultTokenTransactionModel) Update(ctx context.Context, newData *TokenTransaction) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, tokenTransactionRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.TransactionHash, newData.BlockHash, newData.ChainType, newData.Address, newData.Height, newData.TransactionTime, newData.FromAddress, newData.ToAddress, newData.IsFromContract, newData.IsToContract, newData.TokenContractAddress, newData.Amount, newData.TokenSymbol, newData.TokenId, newData.Id)
	return err
}

func (m *defaultTokenTransactionModel) tableName() string {
	return m.table
}
