package model

import (
	"context"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"github.com/go-xorm/builder"
)

var _ SfCaseModel = (*customSfCaseModel)(nil)

type (
	// SfCaseModel is an interface to be customized, add more methods here,
	// and implement the added methods in customSfCaseModel.
	SfCaseModel interface {
		sfCaseModel
		FindListByRequestIdOrMemberId(ctx context.Context, requestId string, memberId int64, limit, page int32) ([]*SfCase, error)
		FindListByGroupID(ctx context.Context, groupId string) ([]*SfCase, error)
		FindStrListByMemberID(ctx context.Context, memberId int64) ([]*CaseTypeAndNumber, error)
	}

	customSfCaseModel struct {
		*defaultSfCaseModel
	}
)

// MustNewSfCaseModel returns a model for the database table.
func MustNewSfCaseModel(conf sqlx.Config, opts ...sqlx.SqlOption) SfCaseModel {
	return &customSfCaseModel{
		defaultSfCaseModel: mustNewSfCaseModel(conf.MustNewMysql(opts...)),
	}
}

func (m *customSfCaseModel) FindListByRequestIdOrMemberId(ctx context.Context, requestId string, memberId int64, limit, page int32) ([]*SfCase, error) {
	b := builder.MySQL().Select(sfCaseRows).From(m.table)
	if requestId != "" {
		b.Where(builder.Eq{"request_id": requestId})
	}
	if memberId != 0 {
		b.Where(builder.Eq{"member_id": memberId})
	}
	if page > 0 && limit > 0 {
		b = b.Limit(int(limit), int((page-1)*limit))
	} else {
		b.Limit(10, 0)
	}
	b.OrderBy("id desc")
	query, args, err := b.ToSQL()
	if err != nil {
		return nil, err
	}
	var resp []*SfCase
	if err := m.conn.QueryRowsCtx(ctx, &resp, query, args...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *customSfCaseModel) FindListByGroupID(ctx context.Context, groupId string) ([]*SfCase, error) {
	query, args, err := builder.
		MySQL().
		Select(sfCaseRows).
		From(m.table).
		Where(builder.Eq{"group_id": groupId}).
		Limit(100).ToSQL()
	if err != nil {
		return nil, err
	}
	var resp []*SfCase
	if err := m.conn.QueryRowsCtx(ctx, &resp, query, args...); err != nil {
		return nil, err
	}
	return resp, nil
}

type CaseTypeAndNumber struct {
	CaseType   int32  `db:"case_type"`
	CaseNumber string `db:"case_number"`
	GroupId    string `db:"group_id"`
}

// STR报告生成用到的信息
func (m *customSfCaseModel) FindStrListByMemberID(ctx context.Context, memberId int64) ([]*CaseTypeAndNumber, error) {
	// 数字的含义参考 https://uponly.larksuite.com/wiki/wikusTvqF9u9ztvyfOmMWcalMBi
	query := `select case_type,case_number,group_id   from sf_case where member_id = ? and case_type IN (7,25,6,8,23,24,21,22,10,26,27) and case_number is not null and case_number != ''`
	var resp []*CaseTypeAndNumber
	if err := m.conn.QueryRowsCtx(ctx, &resp, query, memberId); err != nil {
		return nil, err
	}
	return resp, nil
}
