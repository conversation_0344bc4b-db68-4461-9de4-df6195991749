package model

import (
	"context"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"github.com/go-xorm/builder"
)

var _ FiatAmlTransactionModel = (*customFiatAmlTransactionModel)(nil)

var (
	TransactionTypeDeposit  = "deposit"
	TransactionTypeWithdraw = "withdraw"
	TransactionTypeTrade    = "trade"
	TransactionTypeCard     = "card"

	TransactionCategoryOnlychain = "LTU" // onlychain
	TransactionCategoryKZ        = "KZ"
	TransactionCategoryTR        = "TR"
	TransactionCategoryFiatOther = "FiatOther"
	TransactionCategoryCardEEA   = "CardEEA" // EEA: Card product ID 0
	TransactionCategoryCardAU    = "CardAU"  // AU: Card product ID 1
	TransactionCategoryCardAR    = "CardAR"  // AR: Card product ID 2
	TransactionCategoryCardBR    = "CardBR"  // BR: Card product ID 3
	TransactionCategoryCardKZ    = "CardKZ"  // KZ: Card product ID 4
	TransactionCategoryCardHKG   = "CardHKG" // KZ: Card product ID 5
	TransactionCategoryCardGEO   = "CardGEO" // KZ: Card product ID 6
	//TransactionCategoryCardMEX   = "CardMEX" // KZ: Card product ID 7
	TransactionCategoryCardOther = "CardOther"

	// EU Site
	TransactionCategoryFiatEU = "FiatEU"    //
	TransactionCategoryCardEU = "CardEUEEA" // EEA: Card product ID 0

	TransactionStatusInit     = "INIT"
	TransactionStatusPending  = "PENDING"
	TransactionStatusSent     = "SENT"
	TransactionStatusRejected = "REJECTED"
	TransactionStatusApproved = "APPROVED"

	RnCallbackNone    = ""
	RnCallbackAccept  = "ACCEPT"
	RnCallbackDecline = "DECLINE"
	RnCallbackClosed  = "CLOSED"
)

type (
	// FiatAmlTransactionModel is an interface to be customized, add more methods here,
	// and implement the added methods in customFiatAmlTransactionModel.
	FiatAmlTransactionModel interface {
		fiatAmlTransactionModel
		FindListByUidAndRequestIdAndOrderId(ctx context.Context,
			uid int64, transactionType, requestId, orderId string, limit, page int) ([]*FiatAmlTransaction, error)
	}

	customFiatAmlTransactionModel struct {
		*defaultFiatAmlTransactionModel
	}
)

// MustNewFiatAmlTransactionModel returns a model for the database table.
func MustNewFiatAmlTransactionModel(conf sqlx.Config, opts ...sqlx.SqlOption) FiatAmlTransactionModel {
	return &customFiatAmlTransactionModel{
		defaultFiatAmlTransactionModel: mustNewFiatAmlTransactionModel(conf.MustNewMysql(opts...)),
	}
}

func (m *customFiatAmlTransactionModel) FindListByUidAndRequestIdAndOrderId(ctx context.Context,
	uid int64, transactionType, requestId, orderId string, limit, page int) ([]*FiatAmlTransaction, error) {
	cond := builder.NewCond()
	if uid != 0 {
		cond = cond.And(builder.Eq{"member_id": uid})
	}
	if transactionType != "" {
		cond = cond.And(builder.Eq{"transaction_type": transactionType})
	}
	if requestId != "" {
		cond = cond.And(builder.Eq{"request_id": requestId})
	}
	if orderId != "" {
		cond = cond.And(builder.Eq{"order_no": orderId})
	}
	if limit <= 0 {
		limit = 10
	}
	if page <= 0 {
		page = 1
	}

	query, args, err := builder.MySQL().
		Select(fiatAmlTransactionRows).
		From(m.table).Where(cond).
		Limit(limit, (page-1)*limit).
		OrderBy("id desc").ToSQL()
	if err != nil {
		return nil, err
	}
	var resp []*FiatAmlTransaction
	if err := m.conn.QueryRowsCtx(ctx, &resp, query, args...); err != nil {
		return nil, err
	}
	return resp, nil
}
