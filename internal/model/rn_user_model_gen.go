// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	rnUserFieldNames          = builder.RawFieldNames(&RnUser{})
	rnUserRows                = strings.Join(rnUserFieldNames, ",")
	rnUserRowsExpectAutoSet   = strings.Join(stringx.Remove(rnUserFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	rnUserRowsWithPlaceHolder = strings.Join(stringx.Remove(rnUserFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	amlInsightRnUserIdPrefix     = "amlInsight:rnUser:id:"
	amlInsightRnUserUserIdPrefix = "amlInsight:rnUser:userId:"
)

type (
	rnUserModel interface {
		Insert(ctx context.Context, data *RnUser) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*RnUser, error)
		FindOneByUserId(ctx context.Context, userId uint64) (*RnUser, error)
		Update(ctx context.Context, newData *RnUser) error
	}

	defaultRnUserModel struct {
		sqlc.CachedConn
		table string
	}

	RnUser struct {
		Id                uint64         `db:"id"`                  // 主键
		UserId            uint64         `db:"user_id"`             // 原始用户id
		ClientReferenceId string         `db:"client_reference_id"` // RN 唯一标识
		ExtInfo           sql.NullString `db:"ext_info"`            // 额外扩展字段
		CreatedAt         time.Time      `db:"created_at"`          // 创建时间
		UpdatedAt         time.Time      `db:"updated_at"`          // 更新时间
	}
)

func mustNewRnUserModel(conn sqlc.CachedConn) *defaultRnUserModel {
	return &defaultRnUserModel{
		CachedConn: conn,
		table:      "`rn_user`",
	}
}

func (m *defaultRnUserModel) FindOne(ctx context.Context, id uint64) (*RnUser, error) {
	rnUserIdKey := fmt.Sprintf("%s%v", amlInsightRnUserIdPrefix, id)
	var resp RnUser
	err := m.QueryRowCtx(ctx, &resp, rnUserIdKey, func(ctx context.Context, conn sqlx.SqlConn, v interface{}) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", rnUserRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultRnUserModel) FindOneByUserId(ctx context.Context, userId uint64) (*RnUser, error) {
	rnUserUserIdKey := fmt.Sprintf("%s%v", amlInsightRnUserUserIdPrefix, userId)
	var resp RnUser
	err := m.QueryRowIndexCtx(ctx, &resp, rnUserUserIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v interface{}) (i interface{}, e error) {
		query := fmt.Sprintf("select %s from %s where `user_id` = ? limit 1", rnUserRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, userId); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultRnUserModel) Insert(ctx context.Context, data *RnUser) (sql.Result, error) {
	rnUserIdKey := fmt.Sprintf("%s%v", amlInsightRnUserIdPrefix, data.Id)
	rnUserUserIdKey := fmt.Sprintf("%s%v", amlInsightRnUserUserIdPrefix, data.UserId)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?)", m.table, rnUserRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.UserId, data.ClientReferenceId, data.ExtInfo)
	}, rnUserIdKey, rnUserUserIdKey)
	return ret, err
}

func (m *defaultRnUserModel) Update(ctx context.Context, newData *RnUser) error {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return err
	}

	rnUserIdKey := fmt.Sprintf("%s%v", amlInsightRnUserIdPrefix, data.Id)
	rnUserUserIdKey := fmt.Sprintf("%s%v", amlInsightRnUserUserIdPrefix, data.UserId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, rnUserRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, newData.UserId, newData.ClientReferenceId, newData.ExtInfo, newData.Id)
	}, rnUserIdKey, rnUserUserIdKey)
	return err
}

func (m *defaultRnUserModel) formatPrimary(primary interface{}) string {
	return fmt.Sprintf("%s%v", amlInsightRnUserIdPrefix, primary)
}

func (m *defaultRnUserModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary interface{}) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", rnUserRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultRnUserModel) tableName() string {
	return m.table
}
