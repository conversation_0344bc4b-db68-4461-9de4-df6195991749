package model

import (
	"context"
	"fmt"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
)

var _ AddressLabelHistoryModel = (*customAddressLabelHistoryModel)(nil)

type (
	// AddressLabelHistoryModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAddressLabelHistoryModel.
	AddressLabelHistoryModel interface {
		addressLabelHistoryModel
		BatchInsert(ctx context.Context, data []*AddressLabelHistory) error
	}

	customAddressLabelHistoryModel struct {
		*defaultAddressLabelHistoryModel
	}
)

// MustNewAddressLabelHistoryModel returns a model for the database table.
func MustNewAddressLabelHistoryModel(conf sqlx.Config, opts ...sqlx.SqlOption) AddressLabelHistoryModel {
	return &customAddressLabelHistoryModel{
		defaultAddressLabelHistoryModel: mustNewAddressLabelHistoryModel(conf.MustNewMysql(opts...)),
	}
}

func (m *defaultAddressLabelHistoryModel) BatchInsert(ctx context.Context, data []*AddressLabelHistory) error {
	if len(data) == 0 {
		return nil
	}

	query := fmt.Sprintf("INSERT INTO %s (%s) VALUES (?, ?, ?, ?, ?, ?, ?)", m.table, addressLabelHistoryRowsExpectAutoSet)

	for _, record := range data {
		_, err := m.conn.ExecCtx(ctx, query, record.OperationLogId, record.AddressLabelId, record.Chain, record.Address, record.OperationType, record.PreviousState, record.CurrentState)
		if err != nil {
			return err
		}
	}
	return nil
}
