package model

import (
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"context"
	"database/sql"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestQueryByIdOrLabel(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAmlLabelTriggerModel{
			defaultAmlLabelTriggerModel: mustNewAmlLabelTriggerModel(conn),
		}

		// 模拟查询结果：id 和 triggerLabel 都有条件
		mock.ExpectQuery("SELECT (.+)").
			WithArgs(uint64(1), "testLabel", 10, 0).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "trigger_label", "risk_level", "exposure_type", "manually_auto", "asset_is_bf",
				"need_edd", "suggested_ac", "suggested_qoa", "suggested_noa", "suggested_atb", "suggested_atb2",
				"suggested_awb", "suggested_str", "vendor_source", "trigger_risk_value", "trigger_risk_usd_value",
				"trigger_risk_ratio", "trigger_risk_entity_list", "trigger_this_category_usd_one_month",
				"trigger_this_category_usd_lifetime", "status", "trigger_this_category_withdraw_one_month",
				"editor", "remark", "created_at", "updated_at",
			}).AddRow(
				1, "testLabel", "high", "direct", "manual", "yes", "no", "ac1", "qoa1", "noa1",
				"atb1", "atb2", "awb1", "str1", "vendor1", "1000", "2000", "0.5", "entityList1", "50000",
				"1000000", 1, "10000", "editor1", "remark1", time.Now(), time.Now(),
			))

		// 调用方法
		rows, err := m.QueryByIdOrLabel(context.Background(), 1, "testLabel", 10, 1)
		assert.NoError(t, err)
		assert.Len(t, rows, 1)
		assert.Equal(t, uint64(1), rows[0].Id)
		assert.Equal(t, "testLabel", rows[0].TriggerLabel)
		assert.Equal(t, "high", rows[0].RiskLevel)
		assert.Equal(t, "direct", rows[0].ExposureType)
		assert.Equal(t, "manual", rows[0].ManuallyAuto)
		assert.Equal(t, "yes", rows[0].AssetIsBf)
		assert.Equal(t, "no", rows[0].NeedEdd)
		assert.Equal(t, "ac1", rows[0].SuggestedAc)
		assert.Equal(t, "qoa1", rows[0].SuggestedQoa)
		assert.Equal(t, "noa1", rows[0].SuggestedNoa)
		assert.Equal(t, "atb1", rows[0].SuggestedAtb)
		assert.Equal(t, "atb2", rows[0].SuggestedAtb2)
		assert.Equal(t, "awb1", rows[0].SuggestedAwb)
		assert.Equal(t, "str1", rows[0].SuggestedStr)
		assert.Equal(t, "vendor1", rows[0].VendorSource)
		assert.Equal(t, "1000", rows[0].TriggerRiskValue)
		assert.Equal(t, "2000", rows[0].TriggerRiskUsdValue)
		assert.Equal(t, "0.5", rows[0].TriggerRiskRatio)
		assert.Equal(t, "entityList1", rows[0].TriggerRiskEntityList)
		assert.Equal(t, "50000", rows[0].TriggerThisCategoryUsdOneMonth)
		assert.Equal(t, "1000000", rows[0].TriggerThisCategoryUsdLifetime)
		assert.Equal(t, int32(1), rows[0].Status)
		assert.Equal(t, "10000", rows[0].TriggerThisCategoryWithdrawOneMonth)
		assert.Equal(t, "editor1", rows[0].Editor)
		assert.Equal(t, "remark1", rows[0].Remark)

		// 确保所有期望被满足
		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})

	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAmlLabelTriggerModel{
			defaultAmlLabelTriggerModel: mustNewAmlLabelTriggerModel(conn),
		}

		// 模拟查询结果：只查询 triggerLabel 条件
		mock.ExpectQuery("SELECT (.+)").
			WithArgs("testLabel", 10, 0).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "trigger_label", "risk_level", "exposure_type", "manually_auto", "asset_is_bf",
				"need_edd", "suggested_ac", "suggested_qoa", "suggested_noa", "suggested_atb", "suggested_atb2",
				"suggested_awb", "suggested_str", "vendor_source", "trigger_risk_value", "trigger_risk_usd_value",
				"trigger_risk_ratio", "trigger_risk_entity_list", "trigger_this_category_usd_one_month",
				"trigger_this_category_usd_lifetime", "status", "trigger_this_category_withdraw_one_month",
				"editor", "remark", "created_at", "updated_at",
			}).AddRow(
				2, "testLabel", "medium", "indirect", "auto", "no", "yes", "ac2", "qoa2", "noa2",
				"atb2", "atb3", "awb2", "str2", "vendor2", "1500", "2500", "0.6", "entityList2", "60000",
				"1200000", 1, "20000", "editor2", "remark2", time.Now(), time.Now(),
			))

		// 调用方法
		rows, err := m.QueryByIdOrLabel(context.Background(), 0, "testLabel", 10, 1)
		assert.NoError(t, err)
		assert.Len(t, rows, 1)
		assert.Equal(t, uint64(2), rows[0].Id)
		assert.Equal(t, "testLabel", rows[0].TriggerLabel)
		assert.Equal(t, "medium", rows[0].RiskLevel)
		assert.Equal(t, "indirect", rows[0].ExposureType)
		assert.Equal(t, "auto", rows[0].ManuallyAuto)
		assert.Equal(t, "no", rows[0].AssetIsBf)
		assert.Equal(t, "yes", rows[0].NeedEdd)
		assert.Equal(t, "ac2", rows[0].SuggestedAc)
		assert.Equal(t, "qoa2", rows[0].SuggestedQoa)
		assert.Equal(t, "noa2", rows[0].SuggestedNoa)
		assert.Equal(t, "atb2", rows[0].SuggestedAtb)
		assert.Equal(t, "atb3", rows[0].SuggestedAtb2)
		assert.Equal(t, "awb2", rows[0].SuggestedAwb)
		assert.Equal(t, "str2", rows[0].SuggestedStr)
		assert.Equal(t, "vendor2", rows[0].VendorSource)
		assert.Equal(t, "1500", rows[0].TriggerRiskValue)
		assert.Equal(t, "2500", rows[0].TriggerRiskUsdValue)
		assert.Equal(t, "0.6", rows[0].TriggerRiskRatio)
		assert.Equal(t, "entityList2", rows[0].TriggerRiskEntityList)
		assert.Equal(t, "60000", rows[0].TriggerThisCategoryUsdOneMonth)
		assert.Equal(t, "1200000", rows[0].TriggerThisCategoryUsdLifetime)
		assert.Equal(t, int32(1), rows[0].Status)
		assert.Equal(t, "20000", rows[0].TriggerThisCategoryWithdrawOneMonth)
		assert.Equal(t, "editor2", rows[0].Editor)
		assert.Equal(t, "remark2", rows[0].Remark)

		// 确保所有期望被满足
		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})
}

func TestAmlLabelTriggerModelFindOne(t *testing.T) {
	// 使用 sqlmock 创建一个 mock 数据库连接
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("could not mock db: %v", err)
	}
	defer db.Close()

	conn := sqlx.NewSqlConnFromDB(db)
	m := &defaultAmlLabelTriggerModel{
		conn:  conn,
		table: "`aml_label_trigger`",
	}

	// 模拟查询，假设返回 id 为 1 的记录
	mock.ExpectQuery("select (.+)").
		WithArgs(uint64(1)).
		WillReturnRows(sqlmock.NewRows([]string{
			"id", "trigger_label", "risk_level", "exposure_type", "manually_auto", "asset_is_bf", "need_edd",
			"suggested_ac", "suggested_qoa", "suggested_noa", "suggested_atb", "suggested_atb2", "suggested_awb", "suggested_str",
			"vendor_source", "trigger_risk_value", "trigger_risk_usd_value", "trigger_risk_ratio", "trigger_risk_entity_list",
			"trigger_this_category_usd_one_month", "trigger_this_category_usd_lifetime", "status", "trigger_this_category_withdraw_one_month",
			"editor", "remark", "created_at", "updated_at",
		}).AddRow(
			1, "Label1", "High", "ExposureType1", "Auto", "Yes", "No", "AC1", "QOA1", "NOA1", "ATB1", "ATB2", "AWB1", "STR1",
			"Vendor1", "RiskValue1", "RiskUsdValue1", "RiskRatio1", "EntityList1", "USD1", "LifetimeUSD1", 1, "Withdraw1", "Editor1", "Remark1",
			time.Now(), time.Now(),
		))

	// 调用 FindOne 方法
	result, err := m.FindOne(context.Background(), 1)

	// 断言结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, uint64(1), result.Id)
	assert.Equal(t, "Label1", result.TriggerLabel)

	// 验证期望
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestAmlLabelTriggerModelInsert(t *testing.T) {
	// 使用 sqlmock 创建一个 mock 数据库连接
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("could not mock db: %v", err)
	}
	defer db.Close()

	conn := sqlx.NewSqlConnFromDB(db)
	m := &defaultAmlLabelTriggerModel{
		conn:  conn,
		table: "`aml_label_trigger`",
	}

	// 准备插入的数据
	data := &AmlLabelTrigger{
		TriggerLabel:                        "Label1",
		RiskLevel:                           "High",
		ExposureType:                        "ExposureType1",
		ManuallyAuto:                        "Auto",
		AssetIsBf:                           "Yes",
		NeedEdd:                             "No",
		SuggestedAc:                         "AC1",
		SuggestedQoa:                        "QOA1",
		SuggestedNoa:                        "NOA1",
		SuggestedAtb:                        "ATB1",
		SuggestedAtb2:                       "ATB2",
		SuggestedAwb:                        "AWB1",
		SuggestedStr:                        "STR1",
		VendorSource:                        "Vendor1",
		TriggerRiskValue:                    "RiskValue1",
		TriggerRiskUsdValue:                 "RiskUsdValue1",
		TriggerRiskRatio:                    "RiskRatio1",
		TriggerRiskEntityList:               "EntityList1",
		TriggerThisCategoryUsdOneMonth:      "USD1",
		TriggerThisCategoryUsdLifetime:      "LifetimeUSD1",
		Status:                              1,
		TriggerThisCategoryWithdrawOneMonth: "Withdraw1",
		Editor:                              "Editor1",
		Remark:                              "Remark1",
	}

	// 模拟插入操作
	mock.ExpectExec("insert (.+) ").
		WithArgs(data.TriggerLabel, data.RiskLevel, data.ExposureType, data.ManuallyAuto, data.AssetIsBf, data.NeedEdd, data.SuggestedAc,
			data.SuggestedQoa, data.SuggestedNoa, data.SuggestedAtb, data.SuggestedAtb2, data.SuggestedAwb, data.SuggestedStr, data.VendorSource,
			data.TriggerRiskValue, data.TriggerRiskUsdValue, data.TriggerRiskRatio, data.TriggerRiskEntityList, data.TriggerThisCategoryUsdOneMonth,
								data.TriggerThisCategoryUsdLifetime, data.Status, data.TriggerThisCategoryWithdrawOneMonth, data.Editor, data.Remark).
		WillReturnResult(sqlmock.NewResult(1, 1)) // 假设插入成功，返回生成的 ID 为 1

	// 调用 Insert 方法
	_, err = m.Insert(context.Background(), data)

	// 断言结果
	assert.NoError(t, err)

	// 验证期望
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestAmlLabelTriggerModelUpdate(t *testing.T) {
	// 使用 sqlmock 创建一个 mock 数据库连接
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("could not mock db: %v", err)
	}
	defer db.Close()

	conn := sqlx.NewSqlConnFromDB(db)
	m := &defaultAmlLabelTriggerModel{
		conn:  conn,
		table: "`aml_label_trigger`",
	}

	// 准备更新的数据
	data := &AmlLabelTrigger{
		Id:                                  1,
		TriggerLabel:                        "Label1 Updated",
		RiskLevel:                           "Low",
		ExposureType:                        "ExposureTypeUpdated",
		ManuallyAuto:                        "Manual",
		AssetIsBf:                           "No",
		NeedEdd:                             "Yes",
		SuggestedAc:                         "ACUpdated",
		SuggestedQoa:                        "QOAUpdated",
		SuggestedNoa:                        "NOAUpdated",
		SuggestedAtb:                        "ATBUpdated",
		SuggestedAtb2:                       "ATB2Updated",
		SuggestedAwb:                        "AWBUpdated",
		SuggestedStr:                        "STRUpdated",
		VendorSource:                        "VendorUpdated",
		TriggerRiskValue:                    "RiskValueUpdated",
		TriggerRiskUsdValue:                 "RiskUsdValueUpdated",
		TriggerRiskRatio:                    "RiskRatioUpdated",
		TriggerRiskEntityList:               "EntityListUpdated",
		TriggerThisCategoryUsdOneMonth:      "USDUpdated",
		TriggerThisCategoryUsdLifetime:      "LifetimeUSDUpdated",
		Status:                              2,
		TriggerThisCategoryWithdrawOneMonth: "WithdrawUpdated",
		Editor:                              "EditorUpdated",
		Remark:                              "RemarkUpdated",
	}

	// 模拟更新操作
	mock.ExpectExec("update (.+)").
		WithArgs(data.TriggerLabel, data.RiskLevel, data.ExposureType, data.ManuallyAuto, data.AssetIsBf, data.NeedEdd, data.SuggestedAc,
			data.SuggestedQoa, data.SuggestedNoa, data.SuggestedAtb, data.SuggestedAtb2, data.SuggestedAwb, data.SuggestedStr, data.VendorSource,
			data.TriggerRiskValue, data.TriggerRiskUsdValue, data.TriggerRiskRatio, data.TriggerRiskEntityList, data.TriggerThisCategoryUsdOneMonth,
								data.TriggerThisCategoryUsdLifetime, data.Status, data.TriggerThisCategoryWithdrawOneMonth, data.Editor, data.Remark, data.Id).
		WillReturnResult(sqlmock.NewResult(1, 1)) // 假设更新成功

	// 调用 Update 方法
	err = m.Update(context.Background(), data)

	// 断言结果
	assert.NoError(t, err)

	// 验证期望
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)
}
