package model

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"aml-insight/pkg/cache"

	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestQueryByEntityName(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		lc, _ := cache.NewLocalCache(1 * time.Second)
		cachedConn := sqlc.NewConnWithCache(conn, lc)

		m := &customEntityNameMappingModel{
			defaultEntityNameMappingModel: mustNewEntityNameMappingModel(cachedConn),
		}

		// 模拟查询结果：id 和 entityName 条件
		mock.ExpectQuery("SELECT (.+) FROM `entity_name_mapping` WHERE id = \\? AND entity_name_key = \\? LIMIT \\? OFFSET \\?").
			WithArgs(uint64(1), "entityNameKey1", 10, 0).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "entity_name_key", "entity_name_val", "category_val", "valid", "mapping_type", "has_mapping", "editor", "remark", "created_at", "updated_at",
			}).AddRow(
				1, "entityNameKey1", "entityNameVal1", "categoryVal1", 1, "entity", 1, "editor1", "remark1", time.Now(), time.Now(),
			))

		// 调用方法
		rows, err := m.QueryByEntityName(context.Background(), 1, "entityNameKey1", 10, 1)
		assert.NoError(t, err)
		assert.Len(t, rows, 1)
		assert.Equal(t, uint64(1), rows[0].Id)
		assert.Equal(t, "entityNameKey1", rows[0].EntityNameKey)
		assert.Equal(t, "entityNameVal1", rows[0].EntityNameVal)
		assert.Equal(t, "categoryVal1", rows[0].CategoryVal)
		assert.Equal(t, int32(1), rows[0].Valid)
		assert.Equal(t, "entity", rows[0].MappingType)
		assert.Equal(t, int32(1), rows[0].HasMapping)
		assert.Equal(t, "editor1", rows[0].Editor)
		assert.Equal(t, "remark1", rows[0].Remark)

		// 确保所有期望被满足
		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})

	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		lc, _ := cache.NewLocalCache(1 * time.Second)
		cachedConn := sqlc.NewConnWithCache(conn, lc)

		m := &customEntityNameMappingModel{
			defaultEntityNameMappingModel: mustNewEntityNameMappingModel(cachedConn),
		}

		// 模拟查询结果：只查询 entityName 条件
		mock.ExpectQuery("SELECT (.+) FROM `entity_name_mapping` WHERE entity_name_key = \\? LIMIT \\? OFFSET \\?").
			WithArgs("entityNameKey2", 10, 0).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "entity_name_key", "entity_name_val", "category_val", "valid", "mapping_type", "has_mapping", "editor", "remark", "created_at", "updated_at",
			}).AddRow(
				2, "entityNameKey2", "entityNameVal2", "categoryVal2", 1, "source", 0, "editor2", "remark2", time.Now(), time.Now(),
			))

		// 调用方法
		rows, err := m.QueryByEntityName(context.Background(), 0, "entityNameKey2", 10, 1)
		assert.NoError(t, err)
		assert.Len(t, rows, 1)
		assert.Equal(t, uint64(2), rows[0].Id)
		assert.Equal(t, "entityNameKey2", rows[0].EntityNameKey)
		assert.Equal(t, "entityNameVal2", rows[0].EntityNameVal)
		assert.Equal(t, "categoryVal2", rows[0].CategoryVal)
		assert.Equal(t, int32(1), rows[0].Valid)
		assert.Equal(t, "source", rows[0].MappingType)
		assert.Equal(t, int32(0), rows[0].HasMapping)
		assert.Equal(t, "editor2", rows[0].Editor)
		assert.Equal(t, "remark2", rows[0].Remark)

		// 确保所有期望被满足
		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})
}

func TestEntityNameMappingModelFindOne(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		lc, _ := cache.NewLocalCache(1 * time.Second)
		cachedConn := sqlc.NewConnWithCache(conn, lc)

		m := mustNewEntityNameMappingModel(cachedConn)

		// 模拟查询结果：id为1的记录
		mock.ExpectQuery("select (.+) from `entity_name_mapping` where `id` = \\? limit 1").
			WithArgs(uint64(1)).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "entity_name_key", "entity_name_val", "category_val", "valid", "mapping_type", "has_mapping", "editor", "remark", "created_at", "updated_at",
			}).AddRow(
				1, "entityNameKey1", "entityNameVal1", "categoryVal1", 1, "entity", 1, "editor1", "remark1", time.Now(), time.Now(),
			))

		// 调用FindOne方法
		result, err := m.FindOne(context.Background(), 1)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, uint64(1), result.Id)
		assert.Equal(t, "entityNameKey1", result.EntityNameKey)
		assert.Equal(t, "entityNameVal1", result.EntityNameVal)
		assert.Equal(t, "categoryVal1", result.CategoryVal)
		assert.Equal(t, int32(1), result.Valid)
		assert.Equal(t, "entity", result.MappingType)
		assert.Equal(t, int32(1), result.HasMapping)
		assert.Equal(t, "editor1", result.Editor)
		assert.Equal(t, "remark1", result.Remark)

		// 确保所有期望被满足
		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})
}

func TestEntityNameMappingModelInsert(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		lc, _ := cache.NewLocalCache(1 * time.Second)
		cachedConn := sqlc.NewConnWithCache(conn, lc)

		m := mustNewEntityNameMappingModel(cachedConn)

		data := &EntityNameMapping{
			EntityNameKey: "entityNameKey1",
			EntityNameVal: "entityNameVal1",
			CategoryVal:   "categoryVal1",
			Valid:         1,
			MappingType:   "entity",
			HasMapping:    1,
			Editor:        "editor1",
			Remark:        "remark1",
		}

		// 模拟插入操作
		mock.ExpectExec("insert (.+)").
			WithArgs(data.EntityNameKey, data.EntityNameVal, data.CategoryVal, data.Valid, data.MappingType, data.HasMapping, data.Editor, data.Remark).
			WillReturnResult(sqlmock.NewResult(1, 1)) // 假设插入成功并返回生成的ID为1

		// 调用Insert方法
		_, err := m.Insert(context.Background(), data)
		assert.NoError(t, err)

		// 确保所有期望被满足
		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})
}

func TestEntityNameMappingModelUpdate(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		lc, _ := cache.NewLocalCache(1 * time.Second)
		cachedConn := sqlc.NewConnWithCache(conn, lc)

		m := mustNewEntityNameMappingModel(cachedConn)

		data := &EntityNameMapping{
			Id:            1,
			EntityNameKey: "entityNameKey1",
			EntityNameVal: "entityNameVal1",
			CategoryVal:   "categoryVal1",
			Valid:         1,
			MappingType:   "entity",
			HasMapping:    1,
			Editor:        "editor1",
			Remark:        "remark1",
		}

		// 模拟FindOne查询（Update方法内部会先调用FindOne）
		mock.ExpectQuery("select (.+) from `entity_name_mapping` where `id` = \\? limit 1").
			WithArgs(uint64(1)).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "entity_name_key", "entity_name_val", "category_val", "valid", "mapping_type", "has_mapping", "editor", "remark", "created_at", "updated_at",
			}).AddRow(
				1, "entityNameKey1", "entityNameVal1", "categoryVal1", 1, "entity", 1, "editor1", "remark1", time.Now(), time.Now(),
			))

		// 模拟更新操作
		mock.ExpectExec("update (.+)").
			WithArgs(data.EntityNameKey, data.EntityNameVal, data.CategoryVal, data.Valid, data.MappingType, data.HasMapping, data.Editor, data.Remark, data.Id).
			WillReturnResult(sqlmock.NewResult(1, 1)) // 假设更新成功

		// 调用Update方法
		err := m.Update(context.Background(), data)
		assert.NoError(t, err)

		// 确保所有期望被满足
		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})
}

func TestExistsByValue(t *testing.T) {
	// case 1: value exists
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		lc, _ := cache.NewLocalCache(1 * time.Second)
		cachedConn := sqlc.NewConnWithCache(conn, lc)

		m := &customEntityNameMappingModel{
			defaultEntityNameMappingModel: mustNewEntityNameMappingModel(cachedConn),
		}

		mock.ExpectQuery("SELECT COUNT\\(\\*\\) FROM `entity_name_mapping` WHERE entity_name_val = \\?").
			WithArgs("existingValue").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))

		exists, err := m.ExistsByValue(context.Background(), "existingValue")
		assert.NoError(t, err)
		assert.True(t, exists)

		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})

	// case 2: value not exists
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		lc, _ := cache.NewLocalCache(1 * time.Second)
		cachedConn := sqlc.NewConnWithCache(conn, lc)

		m := &customEntityNameMappingModel{
			defaultEntityNameMappingModel: mustNewEntityNameMappingModel(cachedConn),
		}

		mock.ExpectQuery("SELECT COUNT\\(\\*\\) FROM `entity_name_mapping` WHERE entity_name_val = \\?").
			WithArgs("nonExistingValue").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		exists, err := m.ExistsByValue(context.Background(), "nonExistingValue")
		assert.NoError(t, err)
		assert.False(t, exists)

		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})

	// case 3: database query error
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		lc, _ := cache.NewLocalCache(1 * time.Second)
		cachedConn := sqlc.NewConnWithCache(conn, lc)

		m := &customEntityNameMappingModel{
			defaultEntityNameMappingModel: mustNewEntityNameMappingModel(cachedConn),
		}

		mock.ExpectQuery("SELECT COUNT\\(\\*\\) FROM `entity_name_mapping` WHERE entity_name_val = \\?").
			WithArgs("errorValue").
			WillReturnError(sql.ErrConnDone)

		exists, err := m.ExistsByValue(context.Background(), "errorValue")
		assert.Error(t, err)
		assert.False(t, exists)
		assert.Equal(t, sql.ErrConnDone, err)

		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})

}
