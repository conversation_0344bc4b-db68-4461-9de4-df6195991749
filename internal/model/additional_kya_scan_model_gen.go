// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	additionalKyaScanFieldNames          = builder.RawFieldNames(&AdditionalKyaScan{})
	additionalKyaScanRows                = strings.Join(additionalKyaScanFieldNames, ",")
	additionalKyaScanRowsExpectAutoSet   = strings.Join(stringx.Remove(additionalKyaScanFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	additionalKyaScanRowsWithPlaceHolder = strings.Join(stringx.Remove(additionalKyaScanFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	additionalKyaScanModel interface {
		Insert(ctx context.Context, data *AdditionalKyaScan) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*AdditionalKyaScan, error)
		Update(ctx context.Context, newData *AdditionalKyaScan) error
	}

	defaultAdditionalKyaScanModel struct {
		conn  sqlx.SqlConn
		table string
	}

	AdditionalKyaScan struct {
		Id             uint64    `db:"id"`               // 主键
		Operator       string    `db:"operator"`         // 操作者
		CaseCount      int32     `db:"case_count"`       // 请求重新扫描的 case 数量
		ValidCaseCount int32     `db:"valid_case_count"` // 有效的 case 数量
		CreatedAt      time.Time `db:"created_at"`       // 创建时间
		UpdatedAt      time.Time `db:"updated_at"`       // 更新时间
	}
)

func mustNewAdditionalKyaScanModel(conn sqlx.SqlConn) *defaultAdditionalKyaScanModel {
	return &defaultAdditionalKyaScanModel{
		conn:  conn,
		table: "`additional_kya_scan`",
	}
}

func (m *defaultAdditionalKyaScanModel) FindOne(ctx context.Context, id uint64) (*AdditionalKyaScan, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", additionalKyaScanRows, m.table)
	var resp AdditionalKyaScan
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAdditionalKyaScanModel) Insert(ctx context.Context, data *AdditionalKyaScan) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?)", m.table, additionalKyaScanRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Operator, data.CaseCount, data.ValidCaseCount)
	return ret, err
}

func (m *defaultAdditionalKyaScanModel) Update(ctx context.Context, data *AdditionalKyaScan) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, additionalKyaScanRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Operator, data.CaseCount, data.ValidCaseCount, data.Id)
	return err
}

func (m *defaultAdditionalKyaScanModel) tableName() string {
	return m.table
}
