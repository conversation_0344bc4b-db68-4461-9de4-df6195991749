// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	amlAsyncTaskStatusFieldNames          = builder.RawFieldNames(&AmlAsyncTaskStatus{})
	amlAsyncTaskStatusRows                = strings.Join(amlAsyncTaskStatusFieldNames, ",")
	amlAsyncTaskStatusRowsExpectAutoSet   = strings.Join(stringx.Remove(amlAsyncTaskStatusFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	amlAsyncTaskStatusRowsWithPlaceHolder = strings.Join(stringx.Remove(amlAsyncTaskStatusFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	amlAsyncTaskStatusModel interface {
		Insert(ctx context.Context, data *AmlAsyncTaskStatus) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*AmlAsyncTaskStatus, error)
		FindOneByKey(ctx context.Context, key string) (*AmlAsyncTaskStatus, error)
		Update(ctx context.Context, newData *AmlAsyncTaskStatus) error
	}

	defaultAmlAsyncTaskStatusModel struct {
		conn  sqlx.SqlConn
		table string
	}

	AmlAsyncTaskStatus struct {
		Id           uint64    `db:"id"`            // 主键id
		Key          string    `db:"key"`           // key
		ExtendedInfo string    `db:"extended_info"` // 扩展信息
		Status       int32     `db:"status"`        // 处理状态
		Valid        int32     `db:"valid"`         // 是否有效
		CreatedAt    time.Time `db:"created_at"`    // 创建时间
		UpdatedAt    time.Time `db:"updated_at"`    // 更新时间
	}
)

func mustNewAmlAsyncTaskStatusModel(conn sqlx.SqlConn) *defaultAmlAsyncTaskStatusModel {
	return &defaultAmlAsyncTaskStatusModel{
		conn:  conn,
		table: "`aml_async_task_status`",
	}
}

func (m *defaultAmlAsyncTaskStatusModel) FindOne(ctx context.Context, id uint64) (*AmlAsyncTaskStatus, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", amlAsyncTaskStatusRows, m.table)
	var resp AmlAsyncTaskStatus
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAmlAsyncTaskStatusModel) FindOneByKey(ctx context.Context, key string) (*AmlAsyncTaskStatus, error) {
	var resp AmlAsyncTaskStatus
	query := fmt.Sprintf("select %s from %s where `key` = ? limit 1", amlAsyncTaskStatusRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, key)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAmlAsyncTaskStatusModel) Insert(ctx context.Context, data *AmlAsyncTaskStatus) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, amlAsyncTaskStatusRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Key, data.ExtendedInfo, data.Status, data.Valid)
	return ret, err
}

func (m *defaultAmlAsyncTaskStatusModel) Update(ctx context.Context, newData *AmlAsyncTaskStatus) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, amlAsyncTaskStatusRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Key, newData.ExtendedInfo, newData.Status, newData.Valid, newData.Id)
	return err
}

func (m *defaultAmlAsyncTaskStatusModel) tableName() string {
	return m.table
}
