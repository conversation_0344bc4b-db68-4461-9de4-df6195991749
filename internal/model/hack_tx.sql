CREATE TABLE `hack_tx` (
    `tx_id` varchar(512) NOT NULL COMMENT '交易ID, 主键',
    `tx_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '交易时间',
    `from_addr` varchar(512) NOT NULL DEFAULT '' COMMENT '发送地址',
    `to_addr` varchar(512) NOT NULL DEFAULT '' COMMENT '接收地址',
    `amount` varchar(78) NOT NULL DEFAULT '' COMMENT '交易金额',
    `tx_symbol` varchar(32) NOT NULL DEFAULT '' COMMENT '交易币种',
    `state` varchar(32) NOT NULL DEFAULT '' COMMENT '交易状态',
    `tx_type` varchar(32) NOT NULL DEFAULT '' COMMENT '交易类型',
    `to_chain_type` varchar(32) NOT NULL DEFAULT '' COMMENT '目标链类型',
    `asset_chain_coin` varchar(32) NOT NULL DEFAULT '' COMMENT '资产链币种',
    `to_chain_addr` varchar(512) NOT NULL DEFAULT '' COMMENT '目标链地址',
    `to_chain_tx_hash` varchar(512) NOT NULL DEFAULT '' COMMENT '目标链交易哈希',
    `chain_amount` varchar(78) NOT NULL DEFAULT '' COMMENT '链上金额',
    `created_at`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`tx_id`),
    KEY `idx_tx_time` (`tx_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='黑客交易记录表';
