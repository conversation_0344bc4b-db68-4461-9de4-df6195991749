// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	addressLabelHistoryFieldNames          = builder.RawFieldNames(&AddressLabelHistory{})
	addressLabelHistoryRows                = strings.Join(addressLabelHistoryFieldNames, ",")
	addressLabelHistoryRowsExpectAutoSet   = strings.Join(stringx.Remove(addressLabelHistoryFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	addressLabelHistoryRowsWithPlaceHolder = strings.Join(stringx.Remove(addressLabelHistoryFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	addressLabelHistoryModel interface {
		Insert(ctx context.Context, data *AddressLabelHistory) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*AddressLabelHistory, error)
		Update(ctx context.Context, newData *AddressLabelHistory) error
	}

	defaultAddressLabelHistoryModel struct {
		conn  sqlx.SqlConn
		table string
	}

	AddressLabelHistory struct {
		Id             uint64         `db:"id"`               // 主键id
		OperationLogId uint64         `db:"operation_log_id"` // 操作记录id
		AddressLabelId uint64         `db:"address_label_id"` // 原表主键id
		Chain          string         `db:"chain"`            // 链
		Address        string         `db:"address"`          // 查询的地址
		OperationType  string         `db:"operation_type"`   // 操作类型 INSET|UPDATE
		PreviousState  sql.NullString `db:"previous_state"`   // 变更前的状态
		CurrentState   sql.NullString `db:"current_state"`    // 变更后的状态
		CreatedAt      time.Time      `db:"created_at"`       // 创建时间
		UpdatedAt      time.Time      `db:"updated_at"`       // 更新时间
	}
)

func mustNewAddressLabelHistoryModel(conn sqlx.SqlConn) *defaultAddressLabelHistoryModel {
	return &defaultAddressLabelHistoryModel{
		conn:  conn,
		table: "`address_label_history`",
	}
}

func (m *defaultAddressLabelHistoryModel) FindOne(ctx context.Context, id uint64) (*AddressLabelHistory, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", addressLabelHistoryRows, m.table)
	var resp AddressLabelHistory
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAddressLabelHistoryModel) Insert(ctx context.Context, data *AddressLabelHistory) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, addressLabelHistoryRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.OperationLogId, data.AddressLabelId, data.Chain, data.Address, data.OperationType, data.PreviousState, data.CurrentState)
	return ret, err
}

func (m *defaultAddressLabelHistoryModel) Update(ctx context.Context, data *AddressLabelHistory) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, addressLabelHistoryRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.OperationLogId, data.AddressLabelId, data.Chain, data.Address, data.OperationType, data.PreviousState, data.CurrentState, data.Id)
	return err
}

func (m *defaultAddressLabelHistoryModel) tableName() string {
	return m.table
}
