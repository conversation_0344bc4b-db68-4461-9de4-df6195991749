// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	preKyaHistoryFieldNames          = builder.RawFieldNames(&PreKyaHistory{})
	preKyaHistoryRows                = strings.Join(preKyaHistoryFieldNames, ",")
	preKyaHistoryRowsExpectAutoSet   = strings.Join(stringx.Remove(preKyaHistoryFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	preKyaHistoryRowsWithPlaceHolder = strings.Join(stringx.Remove(preKyaHistoryFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	preKyaHistoryModel interface {
		Insert(ctx context.Context, data *PreKyaHistory) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*PreKyaHistory, error)
		Update(ctx context.Context, newData *PreKyaHistory) error
	}

	defaultPreKyaHistoryModel struct {
		conn  sqlx.SqlConn
		table string
	}

	PreKyaHistory struct {
		Id        uint64    `db:"id"`         // 主键
		RequestId string    `db:"request_id"` // 请求唯一id
		MemberId  uint64    `db:"member_id"`  // 用户ID
		Chain     string    `db:"chain"`      // 链
		Address   string    `db:"address"`    // 查询的地址
		Status    int32     `db:"status"`     // 状态 , 1 pending，2 end
		VipType   int32     `db:"vip_type"`   // KYA 预检查时的 VIP 类型
		VipLevel  int32     `db:"vip_level"`  // KYA 预检查时的 VIP 等级
		Result    string    `db:"result"`     // KYA 预检查的结果信息
		CreatedAt time.Time `db:"created_at"` // 创建时间
		UpdatedAt time.Time `db:"updated_at"` // 更新时间
	}
)

func mustNewPreKyaHistoryModel(conn sqlx.SqlConn) *defaultPreKyaHistoryModel {
	return &defaultPreKyaHistoryModel{
		conn:  conn,
		table: "`pre_kya_history`",
	}
}

func (m *defaultPreKyaHistoryModel) FindOne(ctx context.Context, id uint64) (*PreKyaHistory, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", preKyaHistoryRows, m.table)
	var resp PreKyaHistory
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPreKyaHistoryModel) Insert(ctx context.Context, data *PreKyaHistory) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, preKyaHistoryRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.RequestId, data.MemberId, data.Chain, data.Address, data.Status, data.VipType, data.VipLevel, data.Result)
	return ret, err
}

func (m *defaultPreKyaHistoryModel) Update(ctx context.Context, data *PreKyaHistory) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, preKyaHistoryRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.RequestId, data.MemberId, data.Chain, data.Address, data.Status, data.VipType, data.VipLevel, data.Result, data.Id)
	return err
}

func (m *defaultPreKyaHistoryModel) tableName() string {
	return m.table
}
