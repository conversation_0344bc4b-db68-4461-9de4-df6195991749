package model

import (
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"context"
	"fmt"
)

var _ AddressLabelChangeLogModel = (*customAddressLabelChangeLogModel)(nil)

const (
	OperationTypeInsert = "INSERT"
	OperationTypeUpdate = "UPDATE"
	OperationTypeUpsert = "UPSERT"

	StatusSuccess = "SUCCESS"
	StatusFailed  = "FAILED"
)

type (
	// AddressLabelChangeLogModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAddressLabelChangeLogModel.
	AddressLabelChangeLogModel interface {
		addressLabelChangeLogModel
		UpdateChangeLogStatus(ctx context.Context, id uint64, status string) error
	}

	customAddressLabelChangeLogModel struct {
		*defaultAddressLabelChangeLogModel
	}
)

// MustNewAddressLabelChangeLogModel returns a model for the database table.
func MustNewAddressLabelChangeLogModel(conf sqlx.Config, opts ...sqlx.SqlOption) AddressLabelChangeLogModel {
	return &customAddressLabelChangeLogModel{
		defaultAddressLabelChangeLogModel: mustNewAddressLabelChangeLogModel(conf.MustNewMysql(opts...)),
	}
}

func (m *defaultAddressLabelChangeLogModel) UpdateChangeLogStatus(ctx context.Context, id uint64, status string) error {
	query := fmt.Sprintf("UPDATE %s SET status = ? WHERE id = ?", m.table)

	_, err := m.conn.ExecCtx(ctx, query, status, id)
	if err != nil {
		return err
	}

	return nil
}
