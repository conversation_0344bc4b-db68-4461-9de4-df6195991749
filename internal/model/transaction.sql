-- sync_address_label
CREATE TABLE `sync_address_label` (
    `id`         bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `chain_type` varchar(32)     NOT NULL DEFAULT '' COMMENT '区块链网络标识',
    `address`    varchar(255)    NOT NULL DEFAULT '' COMMENT '地址',
    `is_black`   tinyint(1)      NOT NULL DEFAULT 0 COMMENT '是否黑名单地址',
    `tx_type`    tinyint         NOT NULL DEFAULT 0 COMMENT '交易类型: 1为本币, 2为代币, 3为两个都同步',
    `created_at` datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_chain_address` (`address`, `chain_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='地址标签同步任务表';


CREATE TABLE `normal_transaction` (
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    -- Transaction identifier
    `transaction_hash`   varchar(512)  NOT NULL DEFAULT '' COMMENT '交易哈希',
    `block_hash`         varchar(512)  NOT NULL DEFAULT '' COMMENT '区块哈希',
    `chain_type`         varchar(32)    NOT NULL DEFAULT '' COMMENT '区块链网络标识',
    `address`            varchar(255)   NOT NULL DEFAULT '' COMMENT '查询的地址',
    -- Transaction details
    `method_id`         varchar(20)  NOT NULL DEFAULT '' COMMENT '方法标识',
    `nonce`             varchar(20)  NOT NULL DEFAULT '' COMMENT '交易nonce值',
    `gas_price`         varchar(30)  NOT NULL DEFAULT '' COMMENT 'Gas价格',
    `gas_limit`         varchar(30)  NOT NULL DEFAULT '' COMMENT 'Gas限制',
    `gas_used`          varchar(30)  NOT NULL DEFAULT '' COMMENT '实际使用Gas',
    `height`            bigint       NOT NULL DEFAULT 0 COMMENT '区块高度',
    `transaction_time`  bigint       NOT NULL DEFAULT 0 COMMENT '交易时间戳(毫秒)',
    -- Addresses information
    `from_address`      varchar(255)  NOT NULL DEFAULT '' COMMENT '发送方地址',
    `to_address`        varchar(255)  NOT NULL DEFAULT '' COMMENT '接收方地址',
    `is_from_contract`  tinyint(1)   NOT NULL DEFAULT 0 COMMENT '发送方是否合约地址',
    `is_to_contract`    tinyint(1)   NOT NULL DEFAULT 0 COMMENT '接收方是否合约地址',
    -- Token/Value information
    `amount`            varchar(78)  NOT NULL DEFAULT '' COMMENT '转账金额(支持大数字和小数)',
    `coin_symbol`       varchar(20)  NOT NULL DEFAULT '' COMMENT '代币符号',
    `tx_fee`            varchar(30)  NOT NULL DEFAULT '' COMMENT '交易费用',
    -- Status
    `state`             varchar(10)  NOT NULL DEFAULT 'pending' COMMENT '交易状态:success/fail/pending',
    `transaction_type`  varchar(10)  NOT NULL DEFAULT '' COMMENT '交易类型:0(原生),1(EIP2930),2(EIP1559)',
    -- Item timestamp
    `created_at`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_transaction_hash` (`transaction_hash`) USING BTREE,
    KEY `idx_chain_height` (`chain_type`, `height`) USING BTREE,
    KEY `idx_chain_address_height` (`chain_type`, `address`, `height` DESC) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='原生加密货币交易记录';

CREATE TABLE `token_transaction` (
    `id`                     bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    -- Transaction identifier
    `transaction_hash`       varchar(512)  NOT NULL DEFAULT '' COMMENT '交易哈希',
    `block_hash`             varchar(512)  NOT NULL DEFAULT '' COMMENT '区块哈希',
    `chain_type`             varchar(32)    NOT NULL DEFAULT '' COMMENT '区块链网络标识',
    `address`                varchar(255)   NOT NULL DEFAULT '' COMMENT '查询的地址',
    -- Transaction details
    `height`                 bigint      NOT NULL DEFAULT 0 COMMENT '区块高度',
    `transaction_time`       bigint      NOT NULL DEFAULT 0 COMMENT '交易时间戳(毫秒)',
    -- Addresses information
    `from_address`          varchar(255)  NOT NULL DEFAULT '' COMMENT '发送方地址',
    `to_address`            varchar(255)  NOT NULL DEFAULT '' COMMENT '接收方地址',
    `is_from_contract`      tinyint(1)   NOT NULL DEFAULT 0 COMMENT '发送方是否合约地址',
    `is_to_contract`        tinyint(1)   NOT NULL DEFAULT 0 COMMENT '接收方是否合约地址',
    -- Token/Value information
    `token_contract_address` varchar(255) NOT NULL DEFAULT '' COMMENT '代币合约地址',
    `amount`                 varchar(78)  NOT NULL DEFAULT '' COMMENT '转账金额(支持大数字和小数)',
    `token_symbol`           varchar(20)  NOT NULL DEFAULT '' COMMENT '代币符号',
    `token_id`               varchar(512) NOT NULL DEFAULT '' COMMENT 'NFT代币ID(支持大数字)',
    -- Item timestamp
    `created_at`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_transaction_hash` (`transaction_hash`) USING BTREE,
    KEY `idx_chain_height` (`chain_type`, `height`) USING BTREE,
    KEY `idx_chain_address_height` (`chain_type`, `address`, `height` DESC) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='代币转账交易记录';
