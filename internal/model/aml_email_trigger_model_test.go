package model

import (
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"context"
	"database/sql"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestQueryByLabel(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAmlEmailTriggerModel{
			defaultAmlEmailTriggerModel: mustNewAmlEmailTriggerModel(conn),
		}

		// Mock the query result for the test
		mock.ExpectQuery("select (.+)").
			WillReturnRows(sqlmock.NewRows([]string{"id", "trigger_label", "is_broker_sub_account", "first_email_template", "status"}).
				AddRow(1, "testLabel", "true", "template1", 1))

		// Call the method under test
		rows, err := m.QueryByLabel(context.Background(), "testLabel", 10, 1)
		assert.NoError(t, err)
		assert.Len(t, rows, 1)
		assert.Equal(t, uint64(1), rows[0].Id)
		assert.Equal(t, "testLabel", rows[0].TriggerLabel)
		assert.Equal(t, "true", rows[0].IsBrokerSubAccount)
		assert.Equal(t, "template1", rows[0].FirstEmailTemplate)
		assert.Equal(t, int32(1), rows[0].Status)

		// Ensure all expectations were met
		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})
}

func TestQueryByLabels(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAmlEmailTriggerModel{
			defaultAmlEmailTriggerModel: mustNewAmlEmailTriggerModel(conn),
		}

		// Mock the query result for the test
		mock.ExpectQuery("select (.+)").
			WillReturnRows(sqlmock.NewRows([]string{"id", "trigger_label", "is_broker_sub_account", "first_email_template", "status"}).
				AddRow(1, "testLabel", "true", "template1", 1))

		// Call the method under test
		rows, err := m.QueryByLabels(context.Background(), []string{"testLabel"}, 10, 1)
		assert.NoError(t, err)
		assert.Len(t, rows, 1)
		assert.Equal(t, uint64(1), rows[0].Id)
		assert.Equal(t, "testLabel", rows[0].TriggerLabel)
		assert.Equal(t, "true", rows[0].IsBrokerSubAccount)
		assert.Equal(t, "template1", rows[0].FirstEmailTemplate)
		assert.Equal(t, int32(1), rows[0].Status)

		// Ensure all expectations were met
		err = mock.ExpectationsWereMet()
		assert.NoError(t, err)
	})
}

func TestAmlEmailTriggerModelFindOne(t *testing.T) {
	// 使用 sqlmock 创建一个 mock 数据库连接
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("could not mock db: %v", err)
	}
	defer db.Close()

	conn := sqlx.NewSqlConnFromDB(db)
	m := &defaultAmlEmailTriggerModel{
		conn:  conn,
		table: "`aml_email_trigger`",
	}

	// 模拟查询，假设返回 id 为 1 的记录
	mock.ExpectQuery("select (.+)").
		WithArgs(uint64(1)).
		WillReturnRows(sqlmock.NewRows([]string{
			"id", "trigger_label", "is_broker_sub_account", "first_email_template", "status",
		}).AddRow(
			1, "TriggerLabel1", "Yes", "Template1", 1,
		))

	// 调用 FindOne 方法
	result, err := m.FindOne(context.Background(), 1)

	// 断言结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, uint64(1), result.Id)
	assert.Equal(t, "TriggerLabel1", result.TriggerLabel)

	// 验证期望
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestAmlEmailTriggerModelInsert(t *testing.T) {
	// 使用 sqlmock 创建一个 mock 数据库连接
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("could not mock db: %v", err)
	}
	defer db.Close()

	conn := sqlx.NewSqlConnFromDB(db)
	m := &defaultAmlEmailTriggerModel{
		conn:  conn,
		table: "`aml_email_trigger`",
	}

	// 模拟插入操作
	mock.ExpectExec("insert  (.+)").
		WithArgs("TriggerLabel1", "Yes", "Template1", 1).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// 调用 Insert 方法
	data := &AmlEmailTrigger{
		TriggerLabel:       "TriggerLabel1",
		IsBrokerSubAccount: "Yes",
		FirstEmailTemplate: "Template1",
		Status:             1,
	}
	_, err = m.Insert(context.Background(), data)

	// 断言结果
	assert.NoError(t, err)

	// 验证期望
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestAmlEmailTriggerModelUpdate(t *testing.T) {
	// 使用 sqlmock 创建一个 mock 数据库连接
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("could not mock db: %v", err)
	}
	defer db.Close()

	conn := sqlx.NewSqlConnFromDB(db)
	m := &defaultAmlEmailTriggerModel{
		conn:  conn,
		table: "`aml_email_trigger`",
	}

	// 模拟更新操作
	mock.ExpectExec("update  (.+)").
		WithArgs("TriggerLabel1", "Yes", "Template1", 1, uint64(1)).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// 调用 Update 方法
	data := &AmlEmailTrigger{
		Id:                 1,
		TriggerLabel:       "TriggerLabel1",
		IsBrokerSubAccount: "Yes",
		FirstEmailTemplate: "Template1",
		Status:             1,
	}
	err = m.Update(context.Background(), data)

	// 断言结果
	assert.NoError(t, err)

	// 验证期望
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)
}
