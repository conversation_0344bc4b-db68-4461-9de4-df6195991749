package model

import (
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"context"
	"fmt"
	"strings"
)

var _ AmlEmailTriggerModel = (*customAmlEmailTriggerModel)(nil)

type (
	// AmlEmailTriggerModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAmlEmailTriggerModel.
	AmlEmailTriggerModel interface {
		amlEmailTriggerModel
		QueryByLabel(ctx context.Context, label string, limit, page int32) ([]*AmlEmailTrigger, error)
		QueryByLabels(ctx context.Context, labels []string, limit, page int32) ([]*AmlEmailTrigger, error)
	}

	customAmlEmailTriggerModel struct {
		*defaultAmlEmailTriggerModel
	}
)

const (
	TriggerEmailLabelBrokerYes = "yes"
	TriggerEmailLabelBrokerNo  = "no"
)

// MustNewAmlEmailTriggerModel returns a model for the database table.
func MustNewAmlEmailTriggerModel(conf sqlx.Config, opts ...sqlx.SqlOption) AmlEmailTriggerModel {
	return &customAmlEmailTriggerModel{
		defaultAmlEmailTriggerModel: mustNewAmlEmailTriggerModel(conf.MustNewMysql(opts...)),
	}
}

func (m *customAmlEmailTriggerModel) QueryByLabel(ctx context.Context, label string, limit, page int32) ([]*AmlEmailTrigger, error) {

	var (
		resp []*AmlEmailTrigger
		args []any
	)
	if len(label) == 0 {
		return resp, nil
	}
	args = append(args, label)

	// trigger_label 没有索引，但整体数据量不大
	query := fmt.Sprintf("select %s from %s where `trigger_label` = ? ", amlEmailTriggerRows, m.table)
	if limit > 0 {
		query += fmt.Sprintf(" LIMIT ? OFFSET ?")
		args = append(args, limit, (page-1)*limit)
	}

	err := m.conn.QueryRowsCtx(ctx, &resp, query, args...)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *customAmlEmailTriggerModel) QueryByLabels(ctx context.Context, labels []string, limit, page int32) ([]*AmlEmailTrigger, error) {

	var (
		resp      []*AmlEmailTrigger
		args      []any
		sqlString []string
	)
	if len(labels) == 0 {
		return resp, nil
	}
	for _, label := range labels {
		args = append(args, label)
		sqlString = append(sqlString, "?")
	}

	// trigger_label 没有索引，但整体数据量不大
	query := fmt.Sprintf("select %s from %s where `trigger_label` in (%s) ", amlEmailTriggerRows, m.table, strings.Join(sqlString, ","))
	if limit > 0 {
		query += fmt.Sprintf(" LIMIT ? OFFSET ?")
		args = append(args, limit, (page-1)*limit)
	}

	err := m.conn.QueryRowsCtx(ctx, &resp, query, args...)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
