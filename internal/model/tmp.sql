CREATE TABLE `aml_case` (
                            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                            `request_id` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求唯一id\n',
                            `token` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '幂等标识',
                            `member_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '原始用户id',
                            `chain` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链',
                            `coin` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '币',
                            `from_address` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '来源地址',
                            `to_address` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '目标地址',
                            `tx_hash` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '交易hash',
                            `amount` decimal(65, 30) NOT NULL DEFAULT '0.000000000000000000000000000000' COMMENT '数量',
                            `action_type` int NOT NULL DEFAULT '0' COMMENT 'deposit/withdrawal',
                            `decision` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending' COMMENT 'pass manual edd reject pending',
                            `suggested_action` varchar(120) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '建议操作',
                            `status` int NOT NULL DEFAULT '1' COMMENT '1 创建 2 等待 7 完成关闭',
                            `remark` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
                            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `amount_usd` decimal(65, 30) NOT NULL DEFAULT '0.000000000000000000000000000000' COMMENT '换算usd金额',
                            `disposal` varchar(500) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '处置',
                            `external_status` int NOT NULL DEFAULT '1' COMMENT '对外一致性状态 1处理中 2命中 3未命中',
                            `external_process` int NOT NULL DEFAULT '0' COMMENT '对外处理状态 1允许上账 2直接可退 3edd 4ac',
                            `tx_index` bigint NOT NULL DEFAULT '0' COMMENT '交易index',
                            `block_hash` varchar(256) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '块hash，只用于查询交易详情',
                            `group_id` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'group_id',
                            `utxo_addresses` varchar(1000) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '逗号分割',
                            `label` varchar(500) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '命中主标签',
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `uniq_aml_case_pk` (`request_id`),
                            KEY `idx_aml_case_token_index` (`token`),
                            KEY `idx_aml_case_updated_at_index` (`updated_at`),
                            KEY `idx_aml_case_tx_hash` (`tx_hash`),
                            KEY `idx_aml_case_created_at` (`created_at`),
                            KEY `idx_aml_case_from_address` (`from_address`),
                            KEY `idx_aml_case_member_id` (`member_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 195943135 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户的aml全局状态'