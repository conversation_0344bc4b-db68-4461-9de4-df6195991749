CREATE TABLE `address_label`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `chain`       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '链',
    `address`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '查询的地址',
    `category`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签',
    `entity_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地址所属控制实体的标识',
    `detail_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地址详情列表',
    `editor`      varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '查询的地址',
    `source`      varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签来源',
    `remark`      varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签备注',
    `valid`       int                                                           NOT NULL DEFAULT '1' COMMENT '是否有效【0:标签无效，1:标签有效; 如果对标签删除，可以批量对地址进行valid字段置为0】',
    `created_at`  datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_chain_address` (`address`, `chain`),
    KEY           `idx_created_at` (`created_at`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'aml地址标签表'


CREATE TABLE `address_label_history`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `operation_log_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '操作记录id',
    `address_label_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '原表主键id',
    `chain`            varchar(32)  NOT NULL DEFAULT '' COMMENT '链',
    `address`          varchar(128) NOT NULL DEFAULT '' COMMENT '查询的地址',
    `operation_type`   varchar(32)  NOT NULL DEFAULT '' COMMENT '操作类型 INSET|UPDATE',
    `previous_state`   text COMMENT '变更前的状态',
    `current_state`    text COMMENT '变更后的状态',
    `created_at`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                `idx_operation_log_id` (`operation_log_id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'aml地址标签内容变更记录表'


CREATE TABLE `address_label_change_log`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `operation_user` varchar(64)  NOT NULL DEFAULT '' COMMENT '操作人',
    `operation_type` varchar(32)  NOT NULL DEFAULT '' COMMENT '操作类型 INSET|UPDATE',
    `created_at`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `affected_rows`  int          NOT NULL DEFAULT '0' COMMENT '影响行数',
    `status`         varchar(32)  NOT NULL DEFAULT '' COMMENT '状态 SUCCESS|FAILED',
    `msg`            varchar(256) NOT NULL DEFAULT '' COMMENT 'FAILED 时的错误信息',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_created_at` (`created_at`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'aml地址标签操作记录表'

CREATE TABLE `rn_log`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `request_id` varchar(128) NOT NULL DEFAULT '' COMMENT '唯一请求id',
    `method`     varchar(128) NOT NULL DEFAULT '' COMMENT '请求方法',
    `req`        text COMMENT '请求信息',
    `resp`       text COMMENT '返回信息',
    `status`     int(32) NOT NULL DEFAULT '0' COMMENT '状态 200 是成功的，其他为失败',
    `duration`   bigint(20) NOT NULL DEFAULT '0' COMMENT 'duration ms',
    `created_at` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY          `idx_request_id` (`request_id`) USING BTREE,
    KEY          `idx_created_at` (`created_at`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = 'rn request address log';

CREATE TABLE `fiat_aml_transaction`
(
    `id`                        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `request_id`                varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求唯一标识',
    `member_id`                 bigint unsigned NOT NULL DEFAULT '0' COMMENT '用户memberid',
    `at_time`                   datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发生时间',
    `transaction_type`          varchar(50)                             NOT NULL DEFAULT '' COMMENT '交易类型: withdraw, deposit, trade, card',
    `transaction_category`      varchar(50)                             NOT NULL DEFAULT '' COMMENT '交易类别：Onlychain, KZ, TR, FiatOther, CardEEA, CardKZ, CardAU, CardBR, CardAR, CardOther',
    `fiat_converted_usd_amount` decimal(65, 30)                         NOT NULL DEFAULT '0.0' COMMENT '对应法币USD的转换金额',
    `fiat_currency_symbol`      varchar(32) COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '交易法币币种',
    `fiat_currency_amount`      decimal(65, 30)                         NOT NULL DEFAULT '0.0' COMMENT '交易法币本币金额',
    `digital_currency_symbol`   varchar(32) COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '余额买币-交易数币币种',
    `digital_currency_amount`   decimal(65, 30)                         NOT NULL DEFAULT '0.0' COMMENT '余额买币-交易数币金额',
    `payment_type`              varchar(50) COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '支付方式',
    `channel_type`              varchar(50) COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '充值、提现渠道',
    `card_token`                varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡 Token',
    `order_no`                  varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
    `ext_info`                  text COMMENT '额外扩展字段',
    `decision`                  varchar(20) COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '决策结果=conclusion',
    `rn_resp`                   text COMMENT 'rn 风控返回结果',
    `status`                    varchar(20)                             NOT NULL DEFAULT '' COMMENT '状态 INIT, PENDING, SENT, REJECTED, APPROVED',
    `rn_callback`               varchar(50)                             NOT NULL DEFAULT '0' COMMENT 'rn callback status: ACCEPT, DECLINE, CLOSED',
    `created_at`                datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`                datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_request_id` (`request_id`),
    KEY                         `idx_member_id` (`member_id`),
    KEY                         `idx_order_no` (`order_no`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '法币 aml case Transaction';

CREATE TABLE `rn_user`
(
    `id`                  bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id`             bigint unsigned NOT NULL DEFAULT 0 COMMENT '原始用户id',
    `client_reference_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'RN 唯一标识',
    `ext_info`            text COMMENT '额外扩展字段',
    `created_at`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_user_id_pk` (`user_id`),
    KEY                   `idx_client_reference_id_index` (`client_reference_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='RN 用户映射表';


CREATE TABLE `aml_label_trigger`
(
    `id`                                       bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `trigger_label`                            varchar(200) NOT NULL DEFAULT '' COMMENT '命中label',
    `risk_level`                               varchar(20)  NOT NULL DEFAULT '' COMMENT 'risk level',
    `exposure_type`                            varchar(20)  NOT NULL DEFAULT '' COMMENT 'exposure type',
    `manually_auto`                            varchar(20)  NOT NULL DEFAULT '' COMMENT 'manually_auto',
    `asset_is_bf`                              varchar(20)  NOT NULL DEFAULT '' COMMENT 'block fund',
    `need_edd`                                 varchar(10)  NOT NULL DEFAULT '' COMMENT 'need_edd',
    `suggested_ac`                             varchar(10)  NOT NULL DEFAULT '' COMMENT 'suggested_ac',
    `suggested_qoa`                            varchar(10)  NOT NULL DEFAULT '' COMMENT 'suggested_qoa',
    `suggested_noa`                            varchar(10)  NOT NULL DEFAULT '' COMMENT 'suggested_noa',
    `suggested_atb`                            varchar(10)  NOT NULL DEFAULT '' COMMENT 'suggested_atb',
    `suggested_atb2`                           varchar(10)  NOT NULL DEFAULT '' COMMENT 'suggested_atb2',
    `suggested_awb`                            varchar(10)  NOT NULL DEFAULT '' COMMENT 'suggested_awb',
    `suggested_str`                            varchar(10)  NOT NULL DEFAULT '' COMMENT 'suggested_str',
    `vendor_source`                            varchar(50)  NOT NULL DEFAULT '' COMMENT 'vendor_source',
    `trigger_risk_value`                       varchar(50)  NOT NULL DEFAULT '' COMMENT 'trigger_risk_value',
    `trigger_risk_usd_value`                   varchar(50)  NOT NULL DEFAULT '' COMMENT 'trigger_risk_usd_value',
    `trigger_risk_ratio`                       varchar(50)  NOT NULL DEFAULT '' COMMENT 'trigger_risk_ratio',
    `trigger_risk_entity_list`                 varchar(50)  NOT NULL DEFAULT '' COMMENT 'trigger_risk_entity_list',
    `trigger_this_category_usd_one_month`      varchar(50)  NOT NULL DEFAULT '' COMMENT 'trigger_this_category_usd_one_month',
    `trigger_this_category_usd_lifetime`       varchar(50)  NOT NULL DEFAULT '' COMMENT 'trigger_this_category_usd_lifetime',
    `status`                                   int          NOT NULL DEFAULT '1' COMMENT 'column status',
    `trigger_this_category_withdraw_one_month` varchar(50)  NOT NULL DEFAULT '' COMMENT 'trigger_this_category_withdraw_one_month',
    `editor`                                   varchar(128) NOT NULL DEFAULT '' COMMENT '最后编辑人',
    `remark`                                   varchar(500) NOT NULL DEFAULT '' COMMENT '备注',
    `created_at`                               datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`                               datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                                        `idx_aml_label_trigger_trigger_label` (`trigger_label`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='决策标签命中与对应的触发内容'

CREATE TABLE `aml_email_trigger`
(
    `id`                    bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `trigger_label`         varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'trigger_label',
    `is_broker_sub_account` varchar(10) COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT 'is_broker_sub_account ',
    `first_email_template`  varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'first_email_template',
    `status`                int                                     NOT NULL DEFAULT '1' COMMENT 'status',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'aml_email_trigger';


CREATE TABLE `aml_hit_category`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `provider`         varchar(100) NOT NULL DEFAULT '' COMMENT '供应商',
    `category`         varchar(255) NOT NULL DEFAULT '' COMMENT '命中分类',
    `entity`           varchar(255) NOT NULL DEFAULT '' COMMENT '命中实体类型',
    `action_type`      int          NOT NULL DEFAULT '0' COMMENT '操作类型 1、deposit  2、withdraw',
    `exposure_type`    varchar(20)  NOT NULL DEFAULT '' COMMENT '涉及关系 direct indirect cp(counter_party)',
    `risk_level`       varchar(20)  NOT NULL DEFAULT '' COMMENT '风险等级 prohibited  high  medium',
    `risk_level_score` int          NOT NULL DEFAULT '0' COMMENT '等级分数',
    `status`           int          NOT NULL DEFAULT '1' COMMENT '记录状态 1 启用',
    `created_at`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `category_id`      varchar(20)  NOT NULL DEFAULT '' COMMENT 'category_id',
    `editor`           varchar(128) NOT NULL DEFAULT '' COMMENT '最后编辑人',
    `remark`           varchar(500) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY                `idx_aml_hit_category_hit` (`category`,`exposure_type`,`action_type`,`provider`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='aml命中的category配置'

create table vendor_category_mapping
(
    `id`           bigint unsigned                        NOT NULL AUTO_INCREMENT COMMENT 'id',
    `vendor`       varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci default ''                not null comment '供应商',
    `category_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci default ''                not null comment '映射的 key',
    `category_val` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci default ''                not null comment '映射的 val',
    `valid`        int                                                           default 1                 not null comment '是否有效【0:无效，1:有效',
    `editor`       varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci default ''                not null comment '最后一次修改的用户',
    `remark`       varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci default ''                not null comment '备注',
    `created_at`   datetime                                                      default CURRENT_TIMESTAMP not null comment '创建时间',
    `updated_at`   datetime                                                      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    PRIMARY KEY (`id`)
) comment '供应商 category 映射信息' charset = utf8mb4;

create table entity_name_mapping
(
    `id`              bigint unsigned                                                                         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `entity_name_key` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT ''                NOT NULL COMMENT '映射的 key[entity name | source name]',
    `entity_name_val` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT ''                NOT NULL COMMENT '映射的 value',
    `category_val`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT ''                NOT NULL COMMENT 'category 归类',
    `valid`           int                                                           DEFAULT 0                 NOT NULL COMMENT '是否有效[0: 无效, 1: 有效]',
    `mapping_type`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT ''                NOT NULL COMMENT '映射类型[entity|source]',
    `has_mapping`     int                                                           DEFAULT 0                 NOT NULL COMMENT '是否已映射: [0: 未映射, 1: 已映射]',
    `editor`          varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT ''                NOT NULL COMMENT '最后一次修改的用户',
    `remark`          varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT ''                NOT NULL COMMENT '备注',
    `created_at`      datetime                                                      DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    `updated_at`      datetime                                                      DEFAULT CURRENT_TIMESTAMP NOT NULL on update CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_entity_name_key` (`entity_name_key`),
    INDEX `idx_created_at` (`created_at` DESC),
    INDEX `idx_mapping_type` (`mapping_type`),
    INDEX `idx_has_mapping` (`has_mapping`),
    INDEX `idx_entity_name_val` (`entity_name_val`)
) comment '供应商 entity name 映射信息' charset = utf8mb4;

CREATE TABLE `aml_async_task_status`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `key`           varchar(64) NOT NULL DEFAULT '' COMMENT 'key',
    `extended_info` text        NOT NULL COMMENT '扩展信息',
    `status`        int         NOT NULL DEFAULT '0' COMMENT '处理状态',
    `valid`         int         NOT NULL DEFAULT '1' COMMENT '是否有效',
    `created_at`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_key` (`key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
    COMMENT ='aml 异步任务状态表';


CREATE TABLE `pre_kya_history`
(
    `id`         bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `request_id` varchar(128)          default '' not null comment '请求唯一id',
    `member_id`  bigint unsigned          default '0' not null comment '用户ID',
    `chain`      varchar(20)  NOT NULL DEFAULT '' COMMENT '链',
    `address`    varchar(255) NOT NULL DEFAULT '' COMMENT '查询的地址',
    `status`     int          NOT NULL DEFAULT '0' COMMENT '状态 , 1 pending，2 end',
    `vip_type`   int          NOT NULL DEFAULT '0' COMMENT 'KYA 预检查时的 VIP 类型',
    `vip_level`  int          NOT NULL DEFAULT '0' COMMENT 'KYA 预检查时的 VIP 等级',
    `result`     varchar(50)  NOT NULL DEFAULT '' COMMENT 'KYA 预检查的结果信息',
    `created_at` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY          `idx_member_check_time` (`member_id`,`created_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='KYA 预扫描记录'


CREATE TABLE `all_address_label`
(
    `id`          BIGINT UNSIGNED COMMENT '兼容字段',
    `chain`       VARCHAR(20)  NOT NULL COMMENT '链',
    `address`     VARCHAR(128) NOT NULL COMMENT '查询的地址',
    `category`    VARCHAR(128) NOT NULL DEFAULT '' COMMENT '标签',
    `entity_name` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '地址所属控制实体的标识',
    `detail_name` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '地址详情列表',
    `editor`      VARCHAR(128) NOT NULL DEFAULT '' COMMENT '查询的地址',
    `source`      VARCHAR(128) NOT NULL DEFAULT '' COMMENT '标签来源',
    `remark`      VARCHAR(500) NOT NULL DEFAULT '' COMMENT '标签备注',
    `valid`       TINYINT      NOT NULL DEFAULT 1 COMMENT '是否有效【0:标签无效，1:标签有效】',
    `created_at`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`address`, `chain`) /*T![clustered_index] CLUSTERED */,
    KEY           `idx_created_at` (`created_at`)
) COMMENT = '所有aml地址标签表';

CREATE TABLE `additional_kya_scan`
(
    `id`               bigint unsigned                                    NOT NULL AUTO_INCREMENT COMMENT '主键',
    `operator`         varchar(128) default '' COLLATE utf8mb4_general_ci not null comment '操作者',
    `case_count`       int          default 0                             not null comment '请求重新扫描的 case 数量',
    `valid_case_count` int          default 0                             not null comment '有效的 case 数量',
    `created_at`       datetime     default CURRENT_TIMESTAMP             not null comment '创建时间',
    `updated_at`       datetime     default CURRENT_TIMESTAMP             not null on update CURRENT_TIMESTAMP comment '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'KYA 补充扫描记录';
