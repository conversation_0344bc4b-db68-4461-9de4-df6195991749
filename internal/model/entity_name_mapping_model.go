package model

import (
	"context"
	"fmt"
	"strings"
	"time"

	"aml-insight/pkg/cache"

	"code.bydev.io/frameworks/byone/core/collection"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
)

var _ EntityNameMappingModel = (*customEntityNameMappingModel)(nil)

const (
	MappingTypeEntity = "entity"
	MappingTypeSource = "source"

	HasMappingYes = 1
	HasMappingNo  = 0

	cache_EntityNameMappingEntityNameValPrefix = "cache::entityNameMapping:entityNameVal:"
)

type (
	// EntityNameMappingModel is an interface to be customized, add more methods here,
	// and implement the added methods in customEntityNameMappingModel.
	EntityNameMappingModel interface {
		entityNameMappingModel
		QueryByEntityName(ctx context.Context, id uint64, entityName string, limit, page int32) ([]*EntityNameMapping, error)
		ListWithFilters(ctx context.Context, startTime, endTime *time.Time, mappingType, entityNameKey string, hasMapping *int32, limit, page int32) ([]*EntityNameMapping, int32, error)
		ExistsByValue(ctx context.Context, entityNameVal string) (bool, error)
	}

	customEntityNameMappingModel struct {
		*defaultEntityNameMappingModel
	}

	CacheConfig struct {
		ExpireTime time.Duration `json:",default=5m"`
		Name       string        `json:",default=entity"`
		Capacity   int           `json:",default=16384"` // 16k
	}
)

// MustNewEntityNameMappingModel returns a model for the database table.
func MustNewEntityNameMappingModel(conf sqlx.Config, cacheCfg CacheConfig, opts ...sqlx.SqlOption) EntityNameMappingModel {
	lc, _ := cache.NewLocalCache(cacheCfg.ExpireTime, collection.WithName(cacheCfg.Name),
		collection.WithLimit(cacheCfg.Capacity))
	return &customEntityNameMappingModel{
		defaultEntityNameMappingModel: mustNewEntityNameMappingModel(sqlc.NewConnWithCache(conf.MustNewMysql(opts...), lc)),
	}
}

func (m *customEntityNameMappingModel) QueryByEntityName(ctx context.Context, id uint64, entityName string, limit, page int32) ([]*EntityNameMapping, error) {

	var (
		resp         []*EntityNameMapping
		whereClauses []string
		args         []any
	)

	if id != 0 {
		whereClauses = append(whereClauses, "id = ?")
		args = append(args, id)
	}
	if len(entityName) != 0 {
		whereClauses = append(whereClauses, "entity_name_key = ?")
		args = append(args, entityName)
	}

	// 构建查询语句
	query := fmt.Sprintf("SELECT %s FROM %s", entityNameMappingRows, m.table)
	if len(whereClauses) != 0 {
		query += " WHERE " + strings.Join(whereClauses, " AND ")
	}
	if limit > 0 {
		query += " LIMIT ? OFFSET ?"
		args = append(args, limit, (page-1)*limit)
	}

	// 执行查询
	if err := m.QueryRowsNoCacheCtx(ctx, &resp, query, args...); err != nil {
		return nil, err
	}

	return resp, nil
}

func (m *customEntityNameMappingModel) ListWithFilters(ctx context.Context, startTime, endTime *time.Time, mappingType, entityNameKey string, hasMapping *int32, limit, page int32) ([]*EntityNameMapping, int32, error) {
	var (
		resp         []*EntityNameMapping
		whereClauses []string
		args         []any
	)

	// Build WHERE clauses
	if startTime != nil {
		whereClauses = append(whereClauses, "created_at >= ?")
		args = append(args, *startTime)
	}
	if endTime != nil {
		whereClauses = append(whereClauses, "created_at <= ?")
		args = append(args, *endTime)
	}
	if len(mappingType) != 0 {
		whereClauses = append(whereClauses, "mapping_type = ?")
		args = append(args, mappingType)
	}
	if len(entityNameKey) != 0 {
		whereClauses = append(whereClauses, "entity_name_key LIKE ?")
		args = append(args, "%"+entityNameKey+"%")
	}
	if hasMapping != nil {
		if *hasMapping == HasMappingYes {
			whereClauses = append(whereClauses, "has_mapping = ?")
			args = append(args, HasMappingYes)
		} else {
			whereClauses = append(whereClauses, "has_mapping = ?")
			args = append(args, HasMappingNo)
		}
	}

	// Build main query
	query := fmt.Sprintf("SELECT %s FROM %s", entityNameMappingRows, m.table)
	if len(whereClauses) != 0 {
		query += " WHERE " + strings.Join(whereClauses, " AND ")
	}
	query += " ORDER BY created_at DESC"

	// Add pagination
	if limit > 0 {
		query += " LIMIT ? OFFSET ?"
		args = append(args, limit, (page-1)*limit)
	}

	// Execute main query
	if err := m.QueryRowsNoCacheCtx(ctx, &resp, query, args...); err != nil {
		return nil, 0, err
	}

	// Get total count
	countQuery := "SELECT COUNT(*) FROM " + m.table
	if len(whereClauses) != 0 {
		countQuery += " WHERE " + strings.Join(whereClauses, " AND ")
	}

	var total int32
	// Remove pagination args for count query
	countArgs := args
	if limit > 0 {
		countArgs = args[:len(args)-2]
	}

	if err := m.QueryRowNoCacheCtx(ctx, &total, countQuery, countArgs...); err != nil {
		return nil, 0, err
	}

	return resp, total, nil
}

func (m *customEntityNameMappingModel) ExistsByValue(ctx context.Context, entityNameVal string) (bool, error) {
	entityNameMappingEntityNameValKey := fmt.Sprintf("%s%v", cache_EntityNameMappingEntityNameValPrefix, entityNameVal)

	var count int32
	err := m.QueryRowCtx(ctx, &count, entityNameMappingEntityNameValKey, func(ctx context.Context, conn sqlx.SqlConn, v interface{}) error {
		query := fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE entity_name_val = ?", m.table)
		return conn.QueryRowCtx(ctx, v, query, entityNameVal)
	})

	if err != nil {
		return false, err
	}

	return count > 0, nil
}
