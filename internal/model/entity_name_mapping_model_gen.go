// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	entityNameMappingFieldNames          = builder.RawFieldNames(&EntityNameMapping{})
	entityNameMappingRows                = strings.Join(entityNameMappingFieldNames, ",")
	entityNameMappingRowsExpectAutoSet   = strings.Join(stringx.Remove(entityNameMappingFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	entityNameMappingRowsWithPlaceHolder = strings.Join(stringx.Remove(entityNameMappingFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cache_EntityNameMappingIdPrefix            = "cache::entityNameMapping:id:"
	cache_EntityNameMappingEntityNameKeyPrefix = "cache::entityNameMapping:entityNameKey:"
)

type (
	entityNameMappingModel interface {
		Insert(ctx context.Context, data *EntityNameMapping) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*EntityNameMapping, error)
		FindOneByEntityNameKey(ctx context.Context, entityNameKey string) (*EntityNameMapping, error)
		Update(ctx context.Context, newData *EntityNameMapping) error
	}

	defaultEntityNameMappingModel struct {
		sqlc.CachedConn
		table string
	}

	EntityNameMapping struct {
		Id            uint64    `db:"id"`              // id
		EntityNameKey string    `db:"entity_name_key"` // 映射的 key[entity name | source name]
		EntityNameVal string    `db:"entity_name_val"` // 映射的 value
		CategoryVal   string    `db:"category_val"`    // category 归类
		Valid         int32     `db:"valid"`           // 是否有效[0: 无效, 1: 有效]
		MappingType   string    `db:"mapping_type"`    // 映射类型[entity|source]
		HasMapping    int32     `db:"has_mapping"`     // 是否已映射: [0: 未映射, 1: 已映射]
		Editor        string    `db:"editor"`          // 最后一次修改的用户
		Remark        string    `db:"remark"`          // 备注
		CreatedAt     time.Time `db:"created_at"`      // 创建时间
		UpdatedAt     time.Time `db:"updated_at"`      // 更新时间
	}
)

func mustNewEntityNameMappingModel(conn sqlc.CachedConn) *defaultEntityNameMappingModel {
	return &defaultEntityNameMappingModel{
		CachedConn: conn,
		table:      "`entity_name_mapping`",
	}
}

func (m *defaultEntityNameMappingModel) FindOne(ctx context.Context, id uint64) (*EntityNameMapping, error) {
	entityNameMappingIdKey := fmt.Sprintf("%s%v", cache_EntityNameMappingIdPrefix, id)
	var resp EntityNameMapping
	err := m.QueryRowCtx(ctx, &resp, entityNameMappingIdKey, func(ctx context.Context, conn sqlx.SqlConn, v interface{}) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", entityNameMappingRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultEntityNameMappingModel) FindOneByEntityNameKey(ctx context.Context, entityNameKey string) (*EntityNameMapping, error) {
	entityNameMappingEntityNameKeyKey := fmt.Sprintf("%s%v", cache_EntityNameMappingEntityNameKeyPrefix, entityNameKey)
	var resp EntityNameMapping
	err := m.QueryRowIndexCtx(ctx, &resp, entityNameMappingEntityNameKeyKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v interface{}) (i interface{}, e error) {
		query := fmt.Sprintf("select %s from %s where `entity_name_key` = ? limit 1", entityNameMappingRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, entityNameKey); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultEntityNameMappingModel) Insert(ctx context.Context, data *EntityNameMapping) (sql.Result, error) {
	entityNameMappingEntityNameKeyKey := fmt.Sprintf("%s%v", cache_EntityNameMappingEntityNameKeyPrefix, data.EntityNameKey)
	entityNameMappingIdKey := fmt.Sprintf("%s%v", cache_EntityNameMappingIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, entityNameMappingRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.EntityNameKey, data.EntityNameVal, data.CategoryVal, data.Valid, data.MappingType, data.HasMapping, data.Editor, data.Remark)
	}, entityNameMappingEntityNameKeyKey, entityNameMappingIdKey)
	return ret, err
}

func (m *defaultEntityNameMappingModel) Update(ctx context.Context, newData *EntityNameMapping) error {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return err
	}

	entityNameMappingEntityNameKeyKey := fmt.Sprintf("%s%v", cache_EntityNameMappingEntityNameKeyPrefix, data.EntityNameKey)
	entityNameMappingIdKey := fmt.Sprintf("%s%v", cache_EntityNameMappingIdPrefix, data.Id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, entityNameMappingRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, newData.EntityNameKey, newData.EntityNameVal, newData.CategoryVal, newData.Valid, newData.MappingType, newData.HasMapping, newData.Editor, newData.Remark, newData.Id)
	}, entityNameMappingEntityNameKeyKey, entityNameMappingIdKey)
	return err
}

func (m *defaultEntityNameMappingModel) formatPrimary(primary interface{}) string {
	return fmt.Sprintf("%s%v", cache_EntityNameMappingIdPrefix, primary)
}

func (m *defaultEntityNameMappingModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary interface{}) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", entityNameMappingRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultEntityNameMappingModel) tableName() string {
	return m.table
}
