// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"

	"github.com/shopspring/decimal"
)

var (
	fiatAmlTransactionFieldNames          = builder.RawFieldNames(&FiatAmlTransaction{})
	fiatAmlTransactionRows                = strings.Join(fiatAmlTransactionFieldNames, ",")
	fiatAmlTransactionRowsExpectAutoSet   = strings.Join(stringx.Remove(fiatAmlTransactionFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	fiatAmlTransactionRowsWithPlaceHolder = strings.Join(stringx.Remove(fiatAmlTransactionFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	fiatAmlTransactionModel interface {
		Insert(ctx context.Context, data *FiatAmlTransaction) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*FiatAmlTransaction, error)
		FindOneByRequestId(ctx context.Context, requestId string) (*FiatAmlTransaction, error)
		Update(ctx context.Context, newData *FiatAmlTransaction) error
	}

	defaultFiatAmlTransactionModel struct {
		conn  sqlx.SqlConn
		table string
	}

	FiatAmlTransaction struct {
		Id                     uint64          `db:"id"`                        // 主键
		RequestId              string          `db:"request_id"`                // 请求唯一标识
		MemberId               uint64          `db:"member_id"`                 // 用户memberid
		AtTime                 time.Time       `db:"at_time"`                   // 发生时间
		TransactionType        string          `db:"transaction_type"`          // 交易类型: withdraw, deposit, trade, card
		TransactionCategory    string          `db:"transaction_category"`      // 交易类别：Onlychain, KZ, TR, FiatOther, CardEEA, CardKZ, CardAU, CardBR, CardAR, CardOther
		FiatConvertedUsdAmount decimal.Decimal `db:"fiat_converted_usd_amount"` // 对应法币USD的转换金额
		FiatCurrencySymbol     string          `db:"fiat_currency_symbol"`      // 交易法币币种
		FiatCurrencyAmount     decimal.Decimal `db:"fiat_currency_amount"`      // 交易法币本币金额
		DigitalCurrencySymbol  string          `db:"digital_currency_symbol"`   // 余额买币-交易数币币种
		DigitalCurrencyAmount  decimal.Decimal `db:"digital_currency_amount"`   // 余额买币-交易数币金额
		PaymentType            string          `db:"payment_type"`              // 支付方式
		ChannelType            string          `db:"channel_type"`              // 充值、提现渠道
		CardToken              string          `db:"card_token"`                // 卡 Token
		OrderNo                string          `db:"order_no"`                  // 订单号
		ExtInfo                sql.NullString  `db:"ext_info"`                  // 额外扩展字段
		Decision               string          `db:"decision"`                  // 决策结果=conclusion
		RnResp                 sql.NullString  `db:"rn_resp"`                   // rn 风控返回结果
		Status                 string          `db:"status"`                    // 状态 INIT, PENDING, SENT, REJECTED, APPROVED
		RnCallback             string          `db:"rn_callback"`               // rn callback status: ACCEPT, DECLINE, CLOSED
		CreatedAt              time.Time       `db:"created_at"`                // 创建时间
		UpdatedAt              time.Time       `db:"updated_at"`                // 更新时间
	}
)

func mustNewFiatAmlTransactionModel(conn sqlx.SqlConn) *defaultFiatAmlTransactionModel {
	return &defaultFiatAmlTransactionModel{
		conn:  conn,
		table: "`fiat_aml_transaction`",
	}
}

func (m *defaultFiatAmlTransactionModel) FindOne(ctx context.Context, id uint64) (*FiatAmlTransaction, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", fiatAmlTransactionRows, m.table)
	var resp FiatAmlTransaction
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFiatAmlTransactionModel) FindOneByRequestId(ctx context.Context, requestId string) (*FiatAmlTransaction, error) {
	var resp FiatAmlTransaction
	query := fmt.Sprintf("select %s from %s where `request_id` = ? limit 1", fiatAmlTransactionRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, requestId)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFiatAmlTransactionModel) Insert(ctx context.Context, data *FiatAmlTransaction) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, fiatAmlTransactionRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.RequestId, data.MemberId, data.AtTime, data.TransactionType, data.TransactionCategory, data.FiatConvertedUsdAmount, data.FiatCurrencySymbol, data.FiatCurrencyAmount, data.DigitalCurrencySymbol, data.DigitalCurrencyAmount, data.PaymentType, data.ChannelType, data.CardToken, data.OrderNo, data.ExtInfo, data.Decision, data.RnResp, data.Status, data.RnCallback)
	return ret, err
}

func (m *defaultFiatAmlTransactionModel) Update(ctx context.Context, newData *FiatAmlTransaction) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, fiatAmlTransactionRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.RequestId, newData.MemberId, newData.AtTime, newData.TransactionType, newData.TransactionCategory, newData.FiatConvertedUsdAmount, newData.FiatCurrencySymbol, newData.FiatCurrencyAmount, newData.DigitalCurrencySymbol, newData.DigitalCurrencyAmount, newData.PaymentType, newData.ChannelType, newData.CardToken, newData.OrderNo, newData.ExtInfo, newData.Decision, newData.RnResp, newData.Status, newData.RnCallback, newData.Id)
	return err
}

func (m *defaultFiatAmlTransactionModel) tableName() string {
	return m.table
}
