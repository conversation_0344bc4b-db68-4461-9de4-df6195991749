// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	addressLabelChangeLogFieldNames          = builder.RawFieldNames(&AddressLabelChangeLog{})
	addressLabelChangeLogRows                = strings.Join(addressLabelChangeLogFieldNames, ",")
	addressLabelChangeLogRowsExpectAutoSet   = strings.Join(stringx.Remove(addressLabelChangeLogFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	addressLabelChangeLogRowsWithPlaceHolder = strings.Join(stringx.Remove(addressLabelChangeLogFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	addressLabelChangeLogModel interface {
		Insert(ctx context.Context, data *AddressLabelChangeLog) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*AddressLabelChangeLog, error)
		Update(ctx context.Context, newData *AddressLabelChangeLog) error
	}

	defaultAddressLabelChangeLogModel struct {
		conn  sqlx.SqlConn
		table string
	}

	AddressLabelChangeLog struct {
		Id            uint64    `db:"id"`             // 主键id
		OperationUser string    `db:"operation_user"` // 操作人
		OperationType string    `db:"operation_type"` // 操作类型 INSET|UPDATE
		CreatedAt     time.Time `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time `db:"updated_at"`     // 更新时间
		AffectedRows  int32     `db:"affected_rows"`  // 影响行数
		Status        string    `db:"status"`         // 状态 SUCCESS|FAILED
		Msg           string    `db:"msg"`            // FAILED 时的错误信息
	}
)

func mustNewAddressLabelChangeLogModel(conn sqlx.SqlConn) *defaultAddressLabelChangeLogModel {
	return &defaultAddressLabelChangeLogModel{
		conn:  conn,
		table: "`address_label_change_log`",
	}
}

func (m *defaultAddressLabelChangeLogModel) FindOne(ctx context.Context, id uint64) (*AddressLabelChangeLog, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", addressLabelChangeLogRows, m.table)
	var resp AddressLabelChangeLog
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAddressLabelChangeLogModel) Insert(ctx context.Context, data *AddressLabelChangeLog) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, addressLabelChangeLogRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.OperationUser, data.OperationType, data.AffectedRows, data.Status, data.Msg)
	return ret, err
}

func (m *defaultAddressLabelChangeLogModel) Update(ctx context.Context, data *AddressLabelChangeLog) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, addressLabelChangeLogRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.OperationUser, data.OperationType, data.AffectedRows, data.Status, data.Msg, data.Id)
	return err
}

func (m *defaultAddressLabelChangeLogModel) tableName() string {
	return m.table
}
