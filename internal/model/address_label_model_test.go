package model

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestBatchUpdate(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAddressLabelModel{
			defaultAddressLabelModel: mustNewAddressLabelModel(conn),
		}

		mock.ExpectExec("UPDATE (.+)").WillReturnResult(sqlmock.NewResult(0, 1))

		labels := []*AddressLabel{
			{
				Id: 1,
				//Chain:      "",
				//Address:    "",
				Category:   "c",
				EntityName: "e",
				DetailName: "d",
				Editor:     "e",
				Source:     "s",
				Remark:     "r",
				Valid:      1,
				CreatedAt:  time.Time{},
				UpdatedAt:  time.Time{},
			},
		}

		err := m.<PERSON>ch<PERSON>(context.Background(), labels)
		assert.NoError(t, err)
	})
}

func TestQueryAll(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAddressLabelModel{
			defaultAddressLabelModel: mustNewAddressLabelModel(conn),
		}

		mock.ExpectQuery("SELECT (.+)").WillReturnRows(sqlmock.NewRows([]string{
			"id", "chain", "address", "category", "entity_name", "detail_name",
			"editor", "source", "remark", "valid", "created_at", "updated_at",
		}).AddRow(
			1, "Ethereum", "0x123456...", "DeFi", "Uniswap", "v3",
			"Jack", "manual", "Important", 1, time.Now(), time.Now(),
		))

		rows, err := m.QueryAll(context.Background(), 10, 1)
		assert.NoError(t, err)
		assert.Len(t, rows, 1)
	})
}

func TestQueryIdIn(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAddressLabelModel{
			defaultAddressLabelModel: mustNewAddressLabelModel(conn),
		}

		mock.ExpectQuery("SELECT (.+)").WillReturnRows(sqlmock.NewRows([]string{
			"id", "chain", "address", "category", "entity_name", "detail_name",
			"editor", "source", "remark", "valid", "created_at", "updated_at",
		}).AddRow(
			1, "Ethereum", "0x123456...", "DeFi", "Uniswap", "v3",
			"Jack", "manual", "Important", 1, time.Now(), time.Now(),
		))

		rows, err := m.QueryIdIn(context.Background(), []uint64{1, 2}, "", []string{}, 1, 1)
		assert.NoError(t, err)
		assert.Len(t, rows, 1)
	})

}
