package model

import (
	"database/sql"
	"testing"
)

func TestNullStringToString(t *testing.T) {
	tests := []struct {
		input  sql.NullString
		output string
	}{
		{sql.NullString{String: "test", Valid: true}, "test"},
		{sql.NullString{String: "", Valid: true}, ""},
		{sql.NullString{String: "", Valid: false}, ""},
	}

	for _, test := range tests {
		result := NullStringToString(test.input)
		if result != test.output {
			t.<PERSON><PERSON><PERSON>("NullStringToString(%v) = %v; want %v", test.input, result, test.output)
		}
	}
}

func TestStringToNullString(t *testing.T) {
	tests := []struct {
		input  string
		output sql.NullString
	}{
		{"test", sql.NullString{String: "test", Valid: true}},
		{"", sql.NullString{}},
	}

	for _, test := range tests {
		result := StringToNullString(test.input)
		if result != test.output {
			t.<PERSON>("StringToNullString(%q) = %v; want %v", test.input, result, test.output)
		}
	}
}
