package model

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestCustomAmlCaseModel_InsertWithTime(t *testing.T) {
	tests := []struct {
		name    string
		data    *AmlCase
		setupDB func(mock sqlmock.Sqlmock)
		wantErr bool
		errMsg  string
	}{
		{
			name: "success_with_both_times_set",
			data: &AmlCase{
				RequestId:       "req123",
				Token:           "token123",
				MemberId:        12345,
				Chain:           "BTC",
				Coin:            "BTC",
				FromAddress:     "from_addr",
				ToAddress:       "to_addr",
				TxHash:          "tx_hash",
				Amount:          decimal.NewFromFloat(1.5),
				ActionType:      1,
				Decision:        "pass",
				SuggestedAction: "approve",
				Status:          1,
				Remark:          "test",
				AmountUsd:       decimal.NewFromFloat(50000),
				Disposal:        "auto",
				ExternalStatus:  1,
				ExternalProcess: 1,
				TxIndex:         0,
				BlockHash:       "block_hash",
				GroupId:         "group123",
				UtxoAddresses:   "addr1,addr2",
				Label:           "test_label",
				CreatedAt:       time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
				UpdatedAt:       time.Date(2023, 1, 1, 11, 0, 0, 0, time.UTC),
			},
			setupDB: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("INSERT INTO `aml_case`").
					WithArgs(
						"req123", "token123", uint64(12345), "BTC", "BTC", "from_addr", "to_addr",
						"tx_hash", decimal.NewFromFloat(1.5), int32(1), "pass", "approve", int32(1),
						"test", decimal.NewFromFloat(50000), "auto", int32(1), int32(1), int64(0),
						"block_hash", "group123", "addr1,addr2", "test_label",
						time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
						time.Date(2023, 1, 1, 11, 0, 0, 0, time.UTC),
					).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: false,
		},
		{
			name: "success_with_only_created_at",
			data: &AmlCase{
				RequestId:       "req456",
				Token:           "token456",
				MemberId:        67890,
				Chain:           "ETH",
				Coin:            "ETH",
				FromAddress:     "from_addr2",
				ToAddress:       "to_addr2",
				TxHash:          "tx_hash2",
				Amount:          decimal.NewFromFloat(2.5),
				ActionType:      2,
				Decision:        "reject",
				SuggestedAction: "deny",
				Status:          2,
				Remark:          "test2",
				AmountUsd:       decimal.NewFromFloat(75000),
				Disposal:        "manual",
				ExternalStatus:  2,
				ExternalProcess: 2,
				TxIndex:         1,
				BlockHash:       "block_hash2",
				GroupId:         "group456",
				UtxoAddresses:   "addr3,addr4",
				Label:           "test_label2",
				CreatedAt:       time.Date(2023, 2, 1, 12, 0, 0, 0, time.UTC),
				UpdatedAt:       time.Time{}, // Zero time
			},
			setupDB: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("INSERT INTO `aml_case`").
					WithArgs(
						"req456", "token456", uint64(67890), "ETH", "ETH", "from_addr2", "to_addr2",
						"tx_hash2", decimal.NewFromFloat(2.5), int32(2), "reject", "deny", int32(2),
						"test2", decimal.NewFromFloat(75000), "manual", int32(2), int32(2), int64(1),
						"block_hash2", "group456", "addr3,addr4", "test_label2",
						time.Date(2023, 2, 1, 12, 0, 0, 0, time.UTC),
						time.Date(2023, 2, 1, 12, 0, 0, 0, time.UTC), // CreatedAt used for UpdatedAt
					).
					WillReturnResult(sqlmock.NewResult(2, 1))
			},
			wantErr: false,
		},
		{
			name: "success_with_no_times_set",
			data: &AmlCase{
				RequestId:       "req789",
				Token:           "token789",
				MemberId:        11111,
				Chain:           "LTC",
				Coin:            "LTC",
				FromAddress:     "from_addr3",
				ToAddress:       "to_addr3",
				TxHash:          "tx_hash3",
				Amount:          decimal.NewFromFloat(0.5),
				ActionType:      1,
				Decision:        "pending",
				SuggestedAction: "review",
				Status:          3,
				Remark:          "test3",
				AmountUsd:       decimal.NewFromFloat(25000),
				Disposal:        "pending",
				ExternalStatus:  3,
				ExternalProcess: 3,
				TxIndex:         2,
				BlockHash:       "block_hash3",
				GroupId:         "group789",
				UtxoAddresses:   "addr5,addr6",
				Label:           "test_label3",
				CreatedAt:       time.Time{}, // Zero time
				UpdatedAt:       time.Time{}, // Zero time
			},
			setupDB: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("INSERT INTO `aml_case`").
					WithArgs(
						"req789", "token789", uint64(11111), "LTC", "LTC", "from_addr3", "to_addr3",
						"tx_hash3", decimal.NewFromFloat(0.5), int32(1), "pending", "review", int32(3),
						"test3", decimal.NewFromFloat(25000), "pending", int32(3), int32(3), int64(2),
						"block_hash3", "group789", "addr5,addr6", "test_label3",
					).
					WillReturnResult(sqlmock.NewResult(3, 1))
			},
			wantErr: false,
		},
		{
			name: "success_with_only_updated_at",
			data: &AmlCase{
				RequestId:       "req999",
				Token:           "token999",
				MemberId:        22222,
				Chain:           "DOGE",
				Coin:            "DOGE",
				FromAddress:     "from_addr4",
				ToAddress:       "to_addr4",
				TxHash:          "tx_hash4",
				Amount:          decimal.NewFromFloat(100.0),
				ActionType:      2,
				Decision:        "manual",
				SuggestedAction: "investigate",
				Status:          4,
				Remark:          "test4",
				AmountUsd:       decimal.NewFromFloat(10000),
				Disposal:        "investigate",
				ExternalStatus:  4,
				ExternalProcess: 4,
				TxIndex:         3,
				BlockHash:       "block_hash4",
				GroupId:         "group999",
				UtxoAddresses:   "addr7,addr8",
				Label:           "test_label4",
				CreatedAt:       time.Time{}, // Zero time
				UpdatedAt:       time.Date(2023, 3, 1, 14, 0, 0, 0, time.UTC),
			},
			setupDB: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("INSERT INTO `aml_case`").
					WithArgs(
						"req999", "token999", uint64(22222), "DOGE", "DOGE", "from_addr4", "to_addr4",
						"tx_hash4", decimal.NewFromFloat(100.0), int32(2), "manual", "investigate", int32(4),
						"test4", decimal.NewFromFloat(10000), "investigate", int32(4), int32(4), int64(3),
						"block_hash4", "group999", "addr7,addr8", "test_label4",
						time.Date(2023, 3, 1, 14, 0, 0, 0, time.UTC),
					).
					WillReturnResult(sqlmock.NewResult(4, 1))
			},
			wantErr: false,
		},
		{
			name: "database_error",
			data: &AmlCase{
				RequestId: "req_error",
				Token:     "token_error",
				MemberId:  99999,
				CreatedAt: time.Date(2023, 4, 1, 15, 0, 0, 0, time.UTC),
			},
			setupDB: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("INSERT INTO `aml_case`").
					WillReturnError(sql.ErrConnDone)
			},
			wantErr: true,
			errMsg:  "sql: connection is already closed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
				tt.setupDB(mock)

				conn := sqlx.NewSqlConnFromDB(db)
				m := &customAmlCaseModel{
					defaultAmlCaseModel: mustNewAmlCaseModel(conn),
				}

				result, err := m.InsertWithTime(context.Background(), tt.data)

				if tt.wantErr {
					assert.Error(t, err)
					assert.Contains(t, err.Error(), tt.errMsg)
					assert.Nil(t, result)
				} else {
					assert.NoError(t, err)
					assert.NotNil(t, result)

					lastInsertId, err := result.LastInsertId()
					assert.NoError(t, err)
					assert.Greater(t, lastInsertId, int64(0))

					rowsAffected, err := result.RowsAffected()
					assert.NoError(t, err)
					assert.Equal(t, int64(1), rowsAffected)
				}
			})
		})
	}
}
