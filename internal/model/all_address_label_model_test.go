package model

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

// RunAllAddressLabelTest runs a test function with a mock database.
func RunAllAddressLabelTest(t *testing.T, fn func(db *sql.DB, mock sqlmock.Sqlmock)) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}

	// 执行测试函数
	fn(db, mock)

	if err = mock.ExpectationsWereMet(); err != nil {
		t.<PERSON><PERSON>rf("there were unfulfilled expectations: %s", err)
	}

	// 添加期望数据库关闭操作
	mock.ExpectClose()

	// 测试函数执行完成后关闭连接
	if err = db.Close(); err != nil {
		t.<PERSON><PERSON><PERSON>("error closing db connection: %s", err)
	}
}

func TestAllAddressLabelUpsert(t *testing.T) {
	RunAllAddressLabelTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAllAddressLabelModel{
			defaultAllAddressLabelModel: mustNewAllAddressLabelModel(conn),
		}

		// 期望SQL为insert...on DUPLICATE KEY update...格式
		mock.ExpectExec("insert into all_address_label").
			WithArgs(
				uint64(1), "ETH", "0x123456",
				"Exchange", "Binance", "Hot Wallet", "Admin", "Manual", "VIP", int32(1),
				uint64(1), "Exchange", "Binance", "Hot Wallet", "Admin", "Manual", "VIP", int32(1),
			).
			WillReturnResult(sqlmock.NewResult(0, 1))

		data := &AllAddressLabel{
			Id:         1,
			Chain:      "ETH",
			Address:    "0x123456",
			Category:   "Exchange",
			EntityName: "Binance",
			DetailName: "Hot Wallet",
			Editor:     "Admin",
			Source:     "Manual",
			Remark:     "VIP",
			Valid:      1,
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		}

		_, err := m.Upsert(context.Background(), data)
		assert.NoError(t, err)
	})
}

func TestAllAddressLabelBatchUpdate(t *testing.T) {
	RunAllAddressLabelTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAllAddressLabelModel{
			defaultAllAddressLabelModel: mustNewAllAddressLabelModel(conn),
		}

		// 对于每个标签都会执行一次UPDATE查询
		mock.ExpectExec("UPDATE `all_address_label` SET").
			WithArgs(
				"Exchange", "Binance", "Hot Wallet", "Admin", "Manual", "VIP", int32(1),
				"0x123456", "ETH",
			).
			WillReturnResult(sqlmock.NewResult(0, 1))

		mock.ExpectExec("UPDATE `all_address_label` SET").
			WithArgs(
				"DeFi", "Uniswap", "Router", "System", "Auto", "Important", int32(1),
				"0x789012", "ETH",
			).
			WillReturnResult(sqlmock.NewResult(0, 1))

		labels := []*AllAddressLabel{
			{
				Chain:      "ETH",
				Address:    "0x123456",
				Category:   "Exchange",
				EntityName: "Binance",
				DetailName: "Hot Wallet",
				Editor:     "Admin",
				Source:     "Manual",
				Remark:     "VIP",
				Valid:      1,
			},
			{
				Chain:      "ETH",
				Address:    "0x789012",
				Category:   "DeFi",
				EntityName: "Uniswap",
				DetailName: "Router",
				Editor:     "System",
				Source:     "Auto",
				Remark:     "Important",
				Valid:      1,
			},
		}

		err := m.BatchUpdate(context.Background(), labels)
		assert.NoError(t, err)
	})
}

func TestAllAddressLabelQueryAll(t *testing.T) {
	RunAllAddressLabelTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAllAddressLabelModel{
			defaultAllAddressLabelModel: mustNewAllAddressLabelModel(conn),
		}

		// 准备返回行
		rows := sqlmock.NewRows([]string{
			"id", "chain", "address", "category", "entity_name", "detail_name",
			"editor", "source", "remark", "valid", "created_at", "updated_at",
		}).
			AddRow(
				1, "ETH", "0x123456", "Exchange", "Binance", "Hot Wallet",
				"Admin", "Manual", "VIP", 1, time.Now(), time.Now(),
			).
			AddRow(
				2, "ETH", "0x789012", "DeFi", "Uniswap", "Router",
				"System", "Auto", "Important", 1, time.Now(), time.Now(),
			)

		// 期望查询及其结果 (注意：即使limit=0, page=0，实现也会添加默认的分页)
		mock.ExpectQuery("SELECT \\* FROM `all_address_label` ORDER BY created_at DESC LIMIT \\? OFFSET \\?").
			WithArgs(100, 0).
			WillReturnRows(rows)

		result, err := m.QueryAll(context.Background(), 0, 0)
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, "ETH", result[0].Chain)
		assert.Equal(t, "0x123456", result[0].Address)
		assert.Equal(t, "ETH", result[1].Chain)
		assert.Equal(t, "0x789012", result[1].Address)
	})
}

func TestAllAddressLabelQueryAll_WithPagination(t *testing.T) {
	RunAllAddressLabelTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAllAddressLabelModel{
			defaultAllAddressLabelModel: mustNewAllAddressLabelModel(conn),
		}

		// 准备返回行
		rows := sqlmock.NewRows([]string{
			"id", "chain", "address", "category", "entity_name", "detail_name",
			"editor", "source", "remark", "valid", "created_at", "updated_at",
		}).
			AddRow(
				1, "ETH", "0x123456", "Exchange", "Binance", "Hot Wallet",
				"Admin", "Manual", "VIP", 1, time.Now(), time.Now(),
			)

		// 期望带分页的查询及其结果
		mock.ExpectQuery("SELECT \\* FROM `all_address_label` ORDER BY created_at DESC LIMIT \\? OFFSET \\?").
			WithArgs(10, 0).
			WillReturnRows(rows)

		result, err := m.QueryAll(context.Background(), 10, 1)
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, "ETH", result[0].Chain)
		assert.Equal(t, "0x123456", result[0].Address)
	})
}

func TestAllAddressLabelQueryByAddressAndChain(t *testing.T) {
	RunAllAddressLabelTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAllAddressLabelModel{
			defaultAllAddressLabelModel: mustNewAllAddressLabelModel(conn),
		}

		// 准备返回行
		rows := sqlmock.NewRows([]string{
			"id", "chain", "address", "category", "entity_name", "detail_name",
			"editor", "source", "remark", "valid", "created_at", "updated_at",
		}).
			AddRow(
				1, "ETH", "0x123456", "Exchange", "Binance", "Hot Wallet",
				"Admin", "Manual", "VIP", 1, time.Now(), time.Now(),
			)

		// 期望带条件的查询及其结果 (注意：即使limit=0, page=0，实现也会添加默认的分页)
		mock.ExpectQuery("SELECT \\* FROM `all_address_label` WHERE `chain` = \\? AND `address` IN \\(\\?\\) ORDER BY created_at DESC LIMIT \\? OFFSET \\?").
			WithArgs("ETH", "0x123456", 100, 0).
			WillReturnRows(rows)

		result, err := m.QueryByAddressAndChain(context.Background(), "ETH", []string{"0x123456"}, 0, 0)
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, "ETH", result[0].Chain)
		assert.Equal(t, "0x123456", result[0].Address)
	})
}

func TestAllAddressLabelQueryByAddressAndChain_WithPagination(t *testing.T) {
	RunAllAddressLabelTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAllAddressLabelModel{
			defaultAllAddressLabelModel: mustNewAllAddressLabelModel(conn),
		}

		// 准备返回行
		rows := sqlmock.NewRows([]string{
			"id", "chain", "address", "category", "entity_name", "detail_name",
			"editor", "source", "remark", "valid", "created_at", "updated_at",
		}).
			AddRow(
				1, "ETH", "0x123456", "Exchange", "Binance", "Hot Wallet",
				"Admin", "Manual", "VIP", 1, time.Now(), time.Now(),
			)

		// 期望带条件和分页的查询及其结果
		mock.ExpectQuery("SELECT \\* FROM `all_address_label` WHERE `chain` = \\? AND `address` IN \\(\\?\\) ORDER BY created_at DESC LIMIT \\? OFFSET \\?").
			WithArgs("ETH", "0x123456", 10, 0).
			WillReturnRows(rows)

		result, err := m.QueryByAddressAndChain(context.Background(), "ETH", []string{"0x123456"}, 10, 1)
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, "ETH", result[0].Chain)
		assert.Equal(t, "0x123456", result[0].Address)
	})
}

func TestAllAddressLabelQueryByAddressAndChain_EmptyParams(t *testing.T) {
	RunAllAddressLabelTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)

		m := &customAllAddressLabelModel{
			defaultAllAddressLabelModel: mustNewAllAddressLabelModel(conn),
		}

		// 测试空参数情况
		result, err := m.QueryByAddressAndChain(context.Background(), "", []string{}, 0, 0)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, "both addressList and chain are empty", err.Error())
	})
}
