// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	sfCaseFieldNames          = builder.RawFieldNames(&SfCase{})
	sfCaseRows                = strings.Join(sfCaseFieldNames, ",")
	sfCaseRowsExpectAutoSet   = strings.Join(stringx.Remove(sfCaseFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	sfCaseRowsWithPlaceHolder = strings.Join(stringx.Remove(sfCaseFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	sfCaseModel interface {
		Insert(ctx context.Context, data *SfCase) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*SfCase, error)
		Update(ctx context.Context, newData *SfCase) error
	}

	defaultSfCaseModel struct {
		conn  sqlx.SqlConn
		table string
	}

	SfCase struct {
		Id                                  uint64         `db:"id"`                                       // 主键
		RequestId                           string         `db:"request_id"`                               // 请求id
		CaseId                              string         `db:"case_id"`                                  // case唯一标识
		CaseType                            int32          `db:"case_type"`                                // case类型 1 数币 2法币
		CaseNumber                          string         `db:"case_number"`                              // case number
		MemberId                            uint64         `db:"member_id"`                                // 用户ID
		FiatUserId                          uint64         `db:"fiat_user_id"`                             // 法币用户id
		SfStatus                            string         `db:"sf_status"`                                // sf 状态
		AmlStatus                           sql.NullString `db:"aml_status"`                               // aml处理结果
		CreatedAt                           time.Time      `db:"created_at"`                               // 创建时间
		UpdatedAt                           time.Time      `db:"updated_at"`                               // 更新时间
		SiteId                              string         `db:"site_id"`                                  // 等同与broker_id
		AmlFlow                             int32          `db:"aml_flow"`                                 // 0 状态未知 1 无需EDD：触发归集 2 冻结：触发冻结 3 EDD流程，提交了材料：EDD申诉中 4 EDD审核结果：审核成功 5 EDD审核结果：审核失败 6 可提 7 不可提
		EncryptUid                          string         `db:"encrypt_uid"`                              // 用户加密后的UID
		MainAccountUid                      string         `db:"main_account_uid"`                         // 用户的母账号UID
		IsSubAccount                        string         `db:"is_sub_account"`                           // 用户是否为子账号
		MultiKycUid                         string         `db:"multi_kyc_uid"`                            // 用户单人多证件的其他账号,逗号分割
		Country                             string         `db:"country"`                                  // 用户的国家
		CurrentBanStatus                    string         `db:"current_ban_status"`                       // 用户当前的封禁状态
		GroupId                             string         `db:"group_id"`                                 // AML的group id
		LiftBanAuto                         string         `db:"lift_ban_auto"`                            // 用户是否可以自动解封
		Time                                string         `db:"time"`                                     // 交易的时间（列表）
		DepositWithdrawal                   string         `db:"deposit_withdrawal"`                       // 出金/入金
		Txhash                              string         `db:"txhash"`                                   // 出金/入金的txhash（列表）
		DepositWithdrawalAddress            string         `db:"deposit_withdrawal_address"`               // 出金/入金关联的地址 对于BTC有多个地址，用逗号分隔
		DepositWithdrawalToken              string         `db:"deposit_withdrawal_token"`                 // 出金/入金的币种（列表）
		Chain                               string         `db:"chain"`                                    // chain（列表）
		DepositWithdrawalValue              string         `db:"deposit_withdrawal_value"`                 // 出金/入金的数量（列表）
		DepositWithdrawalUsdValue           string         `db:"deposit_withdrawal_usd_value"`             // 出金/入金的U金额（列表）
		TriggerLabel                        string         `db:"trigger_label"`                            // AML的策略命中Label
		ManuallyAuto                        string         `db:"manually_auto"`                            // AML工单是否自动处理
		AssetIsBf                           string         `db:"asset_is_bf"`                              // AML是否拒绝上账
		NeedEdd                             string         `db:"need_edd"`                                 // AML是否需要EDD
		SuggestedAc                         string         `db:"suggested_ac"`                             // AML是否关闭账号
		SuggestedQoa                        string         `db:"suggested_qoa"`                            // AML最初的QOA结论
		SuggestedNoa                        string         `db:"suggested_noa"`                            // AML最初的NOA结论
		SuggestedAtb                        string         `db:"suggested_atb"`                            // AML最初的ATB结论
		SuggestedAtb2                       string         `db:"suggested_atb2"`                           // AML最初的ATB2结论
		SuggestedAwb                        string         `db:"suggested_awb"`                            // AML最初的AWB结论
		SuggestedStr                        string         `db:"suggested_str"`                            // AML最初的STR结论
		FirstEmailTemplate                  string         `db:"first_email_template"`                     // AML第一封触达邮件的CODE
		VendorSource                        string         `db:"vendor_source"`                            // 供应商
		IncomingRiskLevel                   string         `db:"incoming_risk_level"`                      // incoming_risk_level
		DirectIndirect                      string         `db:"direct_indirect"`                          // direct indirect
		TriggerRiskValue                    string         `db:"trigger_risk_value"`                       // AML告警的KYA数量（列表）
		TriggerRiskUsdValue                 string         `db:"trigger_risk_usd_value"`                   // usd
		TriggerRiskRatio                    string         `db:"trigger_risk_ratio"`                       // ratio
		TriggerRiskEntityList               string         `db:"trigger_risk_entity_list"`                 // trigger_risk_entity_list（列表）
		TriggerThisCategoryUsdOneMonth      string         `db:"trigger_this_category_usd_one_month"`      // trigger_this_category_usd_one_month
		TriggerThisCategoryUsdLifetime      string         `db:"trigger_this_category_usd_lifetime"`       // AML告警的KYT历史总金额
		TriggerThisCategoryWithdrawLifetime string         `db:"trigger_this_category_withdraw_lifetime"`  // AML告警的用户历史出金次数
		CautionNoticeSentCnt                string         `db:"caution_notice_sent_cnt"`                  // AML历史上风险提示邮件的次数
		WarningNoticeSentCnt                string         `db:"warning_notice_sent_cnt"`                  // AML历史上告警邮件的次数
		IsBroker                            string         `db:"is_broker"`                                // 是否为代理商用户
		TriggerThisCategoryWithdrawOneMonth string         `db:"trigger_this_category_withdraw_one_month"` // trigger_this_category_withdraw_one_month
		IsOnlyChain                         string         `db:"is_only_chain"`                            // is_only_chain
		IsHighRiskCountry                   string         `db:"is_high_risk_country"`                     // 是否高风险国家
		IsHighRiskCustomer                  string         `db:"is_high_risk_customer"`                    // is_high_risk_customer
		HitRuleName                         string         `db:"hit_rule_name"`                            // hit_rule_name
		IncomingValue                       string         `db:"incoming_value"`                           // incoming_value
		CumulativeIncomingValue             string         `db:"cumulative_incoming_value"`                // cumulative_incoming_value
		IncomingTransactionCount            string         `db:"incoming_transaction_count"`               // incoming_transaction_count
		OutgoingValue                       string         `db:"outgoing_value"`                           // outgoing_value
		CumulativeOutgoingValue             string         `db:"cumulative_outgoing_value"`                // cumulative_outgoing_value
		OutgoingTransactionCount            string         `db:"outgoing_transaction_count"`               // outgoing_transaction_count
		CraScore                            string         `db:"cra_score"`                                // cra_score
		UserType                            string         `db:"user_type"`                                // user_type
		WorkOrderChannel                    string         `db:"work_order_channel"`                       // 工单的渠道类型: 空：salesforce 工单； appealAPI: 新版本工单
		WebEddLink                          string         `db:"web_edd_link"`                             // appeal 工单的 web 链接
		AppEddLink                          string         `db:"app_edd_link"`                             // appeal 工单的 app 链接
	}
)

func mustNewSfCaseModel(conn sqlx.SqlConn) *defaultSfCaseModel {
	return &defaultSfCaseModel{
		conn:  conn,
		table: "`sf_case`",
	}
}

func (m *defaultSfCaseModel) FindOne(ctx context.Context, id uint64) (*SfCase, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", sfCaseRows, m.table)
	var resp SfCase
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultSfCaseModel) Insert(ctx context.Context, data *SfCase) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, sfCaseRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.RequestId, data.CaseId, data.CaseType, data.CaseNumber, data.MemberId, data.FiatUserId, data.SfStatus, data.AmlStatus, data.SiteId, data.AmlFlow, data.EncryptUid, data.MainAccountUid, data.IsSubAccount, data.MultiKycUid, data.Country, data.CurrentBanStatus, data.GroupId, data.LiftBanAuto, data.Time, data.DepositWithdrawal, data.Txhash, data.DepositWithdrawalAddress, data.DepositWithdrawalToken, data.Chain, data.DepositWithdrawalValue, data.DepositWithdrawalUsdValue, data.TriggerLabel, data.ManuallyAuto, data.AssetIsBf, data.NeedEdd, data.SuggestedAc, data.SuggestedQoa, data.SuggestedNoa, data.SuggestedAtb, data.SuggestedAtb2, data.SuggestedAwb, data.SuggestedStr, data.FirstEmailTemplate, data.VendorSource, data.IncomingRiskLevel, data.DirectIndirect, data.TriggerRiskValue, data.TriggerRiskUsdValue, data.TriggerRiskRatio, data.TriggerRiskEntityList, data.TriggerThisCategoryUsdOneMonth, data.TriggerThisCategoryUsdLifetime, data.TriggerThisCategoryWithdrawLifetime, data.CautionNoticeSentCnt, data.WarningNoticeSentCnt, data.IsBroker, data.TriggerThisCategoryWithdrawOneMonth, data.IsOnlyChain, data.IsHighRiskCountry, data.IsHighRiskCustomer, data.HitRuleName, data.IncomingValue, data.CumulativeIncomingValue, data.IncomingTransactionCount, data.OutgoingValue, data.CumulativeOutgoingValue, data.OutgoingTransactionCount, data.CraScore, data.UserType, data.WorkOrderChannel, data.WebEddLink, data.AppEddLink)
	return ret, err
}

func (m *defaultSfCaseModel) Update(ctx context.Context, data *SfCase) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, sfCaseRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.RequestId, data.CaseId, data.CaseType, data.CaseNumber, data.MemberId, data.FiatUserId, data.SfStatus, data.AmlStatus, data.SiteId, data.AmlFlow, data.EncryptUid, data.MainAccountUid, data.IsSubAccount, data.MultiKycUid, data.Country, data.CurrentBanStatus, data.GroupId, data.LiftBanAuto, data.Time, data.DepositWithdrawal, data.Txhash, data.DepositWithdrawalAddress, data.DepositWithdrawalToken, data.Chain, data.DepositWithdrawalValue, data.DepositWithdrawalUsdValue, data.TriggerLabel, data.ManuallyAuto, data.AssetIsBf, data.NeedEdd, data.SuggestedAc, data.SuggestedQoa, data.SuggestedNoa, data.SuggestedAtb, data.SuggestedAtb2, data.SuggestedAwb, data.SuggestedStr, data.FirstEmailTemplate, data.VendorSource, data.IncomingRiskLevel, data.DirectIndirect, data.TriggerRiskValue, data.TriggerRiskUsdValue, data.TriggerRiskRatio, data.TriggerRiskEntityList, data.TriggerThisCategoryUsdOneMonth, data.TriggerThisCategoryUsdLifetime, data.TriggerThisCategoryWithdrawLifetime, data.CautionNoticeSentCnt, data.WarningNoticeSentCnt, data.IsBroker, data.TriggerThisCategoryWithdrawOneMonth, data.IsOnlyChain, data.IsHighRiskCountry, data.IsHighRiskCustomer, data.HitRuleName, data.IncomingValue, data.CumulativeIncomingValue, data.IncomingTransactionCount, data.OutgoingValue, data.CumulativeOutgoingValue, data.OutgoingTransactionCount, data.CraScore, data.UserType, data.WorkOrderChannel, data.WebEddLink, data.AppEddLink, data.Id)
	return err
}

func (m *defaultSfCaseModel) tableName() string {
	return m.table
}
