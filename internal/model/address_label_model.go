package model

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
)

var _ AddressLabelModel = (*customAddressLabelModel)(nil)

type (
	// AddressLabelModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAddressLabelModel.
	AddressLabelModel interface {
		addressLabelModel

		InsertOrUpdate(ctx context.Context, data *AddressLabel) error
		QueryAll(ctx context.Context, limit, page int32) ([]*AddressLabel, error)
		QueryIdIn(ctx context.Context, ids []uint64, chain string, addressList []string, limit, page int32) ([]*AddressLabel, error)
		BatchUpdate(ctx context.Context, labels []*AddressLabel) error
	}

	customAddressLabelModel struct {
		*defaultAddressLabelModel
	}
)

// MustNewAddressLabelModel returns a model for the database table.
func MustNewAddressLabelModel(conf sqlx.Config, opts ...sqlx.SqlOption) AddressLabelModel {
	return &customAddressLabelModel{
		defaultAddressLabelModel: mustNewAddressLabelModel(conf.MustNewMysql(opts...)),
	}
}

func (m *customAddressLabelModel) InsertOrUpdate(ctx context.Context, data *AddressLabel) error {
	args := []any{data.Chain, data.Address,
		data.Category, data.EntityName, data.DetailName, data.Editor, data.Source, data.Remark, data.Valid,
		data.Category, data.EntityName, data.DetailName, data.Editor, data.Source, data.Remark, data.Valid}

	stmt := fmt.Sprintf(`insert into address_label (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?) 
on DUPLICATE KEY update category = ?, entity_name = ?, detail_name = ?, editor = ?, source = ?, remark = ?, valid = ?`, addressLabelRowsExpectAutoSet)
	_, err := m.conn.ExecCtx(ctx, stmt, args...)
	return err
}

func (m *customAddressLabelModel) BatchUpdate(ctx context.Context, labels []*AddressLabel) error {
	if len(labels) == 0 {
		return nil
	}

	for _, label := range labels {
		// 构建更新的字段和参数
		var setClauses []string
		var args []any

		//setClauses = append(setClauses, "`chain` = ?")
		//args = append(args, label.Chain)
		//setClauses = append(setClauses, "`address` = ?")
		//args = append(args, label.Address)
		setClauses = append(setClauses, "`category` = ?")
		args = append(args, label.Category)
		setClauses = append(setClauses, "`entity_name` = ?")
		args = append(args, label.EntityName)
		setClauses = append(setClauses, "`detail_name` = ?")
		args = append(args, label.DetailName)
		setClauses = append(setClauses, "`editor` = ?")
		args = append(args, label.Editor)
		setClauses = append(setClauses, "`source` = ?")
		args = append(args, label.Source)
		setClauses = append(setClauses, "`remark` = ?")
		args = append(args, label.Remark)
		setClauses = append(setClauses, "`valid` = ?")
		args = append(args, label.Valid)

		// 执行更新
		query := fmt.Sprintf("UPDATE %s SET %s WHERE `id` = ?", m.table, strings.Join(setClauses, ","))
		args = append(args, label.Id)
		if _, err := m.conn.ExecCtx(ctx, query, args...); err != nil {
			return err
		}
	}
	return nil
}

func (m *customAddressLabelModel) QueryAll(ctx context.Context, limit, page int32) ([]*AddressLabel, error) {
	var labels []*AddressLabel
	var args []any

	query := fmt.Sprintf("SELECT * FROM %s", m.table)
	query += " ORDER BY id DESC"
	if limit > 0 {
		query += fmt.Sprintf(" LIMIT ? OFFSET ?")
		args = append(args, limit, (page-1)*limit)
	}

	if err := m.conn.QueryRowsCtx(ctx, &labels, query, args...); err != nil {
		return nil, err
	}

	return labels, nil
}

func (m *customAddressLabelModel) QueryIdIn(ctx context.Context, ids []uint64, chain string, addressList []string, limit, page int32) ([]*AddressLabel, error) {
	var labels []*AddressLabel

	if len(ids) == 0 && len(addressList) == 0 {
		return nil, errors.New("ids or addressList is empty")
	}

	// 构建查询条件
	var whereClauses []string
	var args []any

	if len(ids) > 0 {
		// 展开 ids 参数
		placeholders := make([]string, len(ids))
		for i := range ids {
			placeholders[i] = "?"
			args = append(args, ids[i])
		}
		whereClauses = append(whereClauses, fmt.Sprintf("`id` IN (%s)", strings.Join(placeholders, ",")))
	}
	if chain != "" {
		whereClauses = append(whereClauses, "`chain` = ?")
		args = append(args, chain)
	}
	if len(addressList) > 0 {
		// 展开 addressList 参数
		placeholders := make([]string, len(addressList))
		for i := range addressList {
			placeholders[i] = "?"
			args = append(args, addressList[i])
		}
		whereClauses = append(whereClauses, fmt.Sprintf("`address` IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(whereClauses) == 0 {
		return labels, nil
	}

	// 构建查询语句
	query := fmt.Sprintf("SELECT * FROM %s", m.table)
	query += " WHERE " + strings.Join(whereClauses, " AND ")
	if limit > 0 {
		query += fmt.Sprintf(" LIMIT ? OFFSET ?")
		args = append(args, limit, (page-1)*limit)
	}

	// 执行查询
	if err := m.conn.QueryRowsCtx(ctx, &labels, query, args...); err != nil {
		return nil, err
	}

	return labels, nil
}
