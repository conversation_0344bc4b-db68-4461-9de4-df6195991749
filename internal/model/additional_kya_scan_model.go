package model

import "code.bydev.io/frameworks/byone/core/stores/sqlx"

var _ AdditionalKyaScanModel = (*customAdditionalKyaScanModel)(nil)

type (
	// AdditionalKyaScanModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAdditionalKyaScanModel.
	AdditionalKyaScanModel interface {
		additionalKyaScanModel
	}

	customAdditionalKyaScanModel struct {
		*defaultAdditionalKyaScanModel
	}
)

// MustNewAdditionalKyaScanModel returns a model for the database table.
func MustNewAdditionalKyaScanModel(conf sqlx.Config, opts ...sqlx.SqlOption) AdditionalKyaScanModel {
	return &customAdditionalKyaScanModel{
		defaultAdditionalKyaScanModel: mustNewAdditionalKyaScanModel(conf.MustNewMysql(opts...)),
	}
}
