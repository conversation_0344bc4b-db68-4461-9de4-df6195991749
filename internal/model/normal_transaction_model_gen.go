// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	normalTransactionFieldNames          = builder.RawFieldNames(&NormalTransaction{})
	normalTransactionRows                = strings.Join(normalTransactionFieldNames, ",")
	normalTransactionRowsExpectAutoSet   = strings.Join(stringx.Remove(normalTransactionFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	normalTransactionRowsWithPlaceHolder = strings.Join(stringx.Remove(normalTransactionFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	normalTransactionModel interface {
		Insert(ctx context.Context, data *NormalTransaction) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*NormalTransaction, error)
		FindOneByTransactionHash(ctx context.Context, transactionHash string) (*NormalTransaction, error)
		Update(ctx context.Context, newData *NormalTransaction) error
	}

	defaultNormalTransactionModel struct {
		conn  sqlx.SqlConn
		table string
	}

	NormalTransaction struct {
		Id              uint64    `db:"id"`               // 主键id
		TransactionHash string    `db:"transaction_hash"` // 交易哈希
		BlockHash       string    `db:"block_hash"`       // 区块哈希
		ChainType       string    `db:"chain_type"`       // 区块链网络标识
		Address         string    `db:"address"`          // 查询的地址
		MethodId        string    `db:"method_id"`        // 方法标识
		Nonce           string    `db:"nonce"`            // 交易nonce值
		GasPrice        string    `db:"gas_price"`        // Gas价格
		GasLimit        string    `db:"gas_limit"`        // Gas限制
		GasUsed         string    `db:"gas_used"`         // 实际使用Gas
		Height          int64     `db:"height"`           // 区块高度
		TransactionTime int64     `db:"transaction_time"` // 交易时间戳(毫秒)
		FromAddress     string    `db:"from_address"`     // 发送方地址
		ToAddress       string    `db:"to_address"`       // 接收方地址
		IsFromContract  int16     `db:"is_from_contract"` // 发送方是否合约地址
		IsToContract    int16     `db:"is_to_contract"`   // 接收方是否合约地址
		Amount          string    `db:"amount"`           // 转账金额(支持大数字和小数)
		CoinSymbol      string    `db:"coin_symbol"`      // 代币符号
		TxFee           string    `db:"tx_fee"`           // 交易费用
		State           string    `db:"state"`            // 交易状态:success/fail/pending
		TransactionType string    `db:"transaction_type"` // 交易类型:0(原生),1(EIP2930),2(EIP1559)
		CreatedAt       time.Time `db:"created_at"`       // 创建时间
		UpdatedAt       time.Time `db:"updated_at"`       // 更新时间
	}
)

func mustNewNormalTransactionModel(conn sqlx.SqlConn) *defaultNormalTransactionModel {
	return &defaultNormalTransactionModel{
		conn:  conn,
		table: "`normal_transaction`",
	}
}

func (m *defaultNormalTransactionModel) FindOne(ctx context.Context, id uint64) (*NormalTransaction, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", normalTransactionRows, m.table)
	var resp NormalTransaction
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultNormalTransactionModel) FindOneByTransactionHash(ctx context.Context, transactionHash string) (*NormalTransaction, error) {
	var resp NormalTransaction
	query := fmt.Sprintf("select %s from %s where `transaction_hash` = ? limit 1", normalTransactionRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, transactionHash)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultNormalTransactionModel) Insert(ctx context.Context, data *NormalTransaction) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, normalTransactionRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.TransactionHash, data.BlockHash, data.ChainType, data.Address, data.MethodId, data.Nonce, data.GasPrice, data.GasLimit, data.GasUsed, data.Height, data.TransactionTime, data.FromAddress, data.ToAddress, data.IsFromContract, data.IsToContract, data.Amount, data.CoinSymbol, data.TxFee, data.State, data.TransactionType)
	return ret, err
}

func (m *defaultNormalTransactionModel) Update(ctx context.Context, newData *NormalTransaction) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, normalTransactionRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.TransactionHash, newData.BlockHash, newData.ChainType, newData.Address, newData.MethodId, newData.Nonce, newData.GasPrice, newData.GasLimit, newData.GasUsed, newData.Height, newData.TransactionTime, newData.FromAddress, newData.ToAddress, newData.IsFromContract, newData.IsToContract, newData.Amount, newData.CoinSymbol, newData.TxFee, newData.State, newData.TransactionType, newData.Id)
	return err
}

func (m *defaultNormalTransactionModel) tableName() string {
	return m.table
}
