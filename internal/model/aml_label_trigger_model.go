package model

import (
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"context"
	"fmt"
	"strings"
)

var _ AmlLabelTriggerModel = (*customAmlLabelTriggerModel)(nil)

type (
	// AmlLabelTriggerModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAmlLabelTriggerModel.
	AmlLabelTriggerModel interface {
		amlLabelTriggerModel
		QueryByIdOrLabel(ctx context.Context, id uint64, label string, limit, page int32) ([]*AmlLabelTrigger, error)
	}

	customAmlLabelTriggerModel struct {
		*defaultAmlLabelTriggerModel
	}
)

// MustNewAmlLabelTriggerModel returns a model for the database table.
func MustNewAmlLabelTriggerModel(conf sqlx.Config, opts ...sqlx.SqlOption) AmlLabelTriggerModel {
	return &customAmlLabelTriggerModel{
		defaultAmlLabelTriggerModel: mustNewAmlLabelTriggerModel(conf.MustNewMysql(opts...)),
	}
}

func (m *customAmlLabelTriggerModel) QueryByIdOrLabel(ctx context.Context, id uint64, triggerLabel string, limit, page int32) ([]*AmlLabelTrigger, error) {
	var (
		resp         []*AmlLabelTrigger
		whereClauses []string
		args         []any
	)

	if id != 0 {
		whereClauses = append(whereClauses, "id = ?")
		args = append(args, id)
	}
	if len(triggerLabel) != 0 {
		whereClauses = append(whereClauses, "trigger_label = ?")
		args = append(args, triggerLabel)
	}

	// 构建查询语句
	query := fmt.Sprintf("SELECT * FROM %s", m.table)
	if len(whereClauses) != 0 {
		query += " WHERE " + strings.Join(whereClauses, " AND ")
	}
	query += fmt.Sprintf(" ORDER BY id DESC")
	if limit > 0 {
		query += fmt.Sprintf(" LIMIT ? OFFSET ?")
		args = append(args, limit, (page-1)*limit)
	}

	// 执行查询
	if err := m.conn.QueryRowsCtx(ctx, &resp, query, args...); err != nil {
		return nil, err
	}

	return resp, nil
}
