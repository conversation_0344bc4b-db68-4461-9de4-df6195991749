package model

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"testing"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

const maxBatchSize = 1000

func TestNormalTransaction_Insert(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		model := &customNormalTransactionModel{
			defaultNormalTransactionModel: mustNewNormalTransactionModel(conn),
		}

		t.Run("success", func(t *testing.T) {
			mock.ExpectExec("insert into `normal_transaction`").
				WithArgs(
					"hash1", "block1", "chain1", "addr1", "method1",
					"1", "1000", "2000", "1500", int64(100), int64(1740063284739),
					"from1", "to1", int16(0), int16(1),
					"100", "ETH", "0.1", "success", "0",
				).
				WillReturnResult(sqlmock.NewResult(1, 1))

			tx := &NormalTransaction{
				TransactionHash: "hash1",
				BlockHash:       "block1",
				ChainType:       "chain1",
				Address:         "addr1",
				MethodId:        "method1",
				Nonce:           "1",
				GasPrice:        "1000",
				GasLimit:        "2000",
				GasUsed:         "1500",
				Height:          100,
				TransactionTime: 1740063284739,
				FromAddress:     "from1",
				ToAddress:       "to1",
				IsFromContract:  0,
				IsToContract:    1,
				Amount:          "100",
				CoinSymbol:      "ETH",
				TxFee:           "0.1",
				State:           "success",
				TransactionType: "0",
			}

			result, err := model.Insert(context.Background(), tx)
			assert.NoError(t, err)
			lastId, err := result.LastInsertId()
			assert.NoError(t, err)
			assert.Equal(t, int64(1), lastId)
		})

		t.Run("insert_error", func(t *testing.T) {
			tx := &NormalTransaction{
				TransactionHash: "hash1",
				ChainType:       "chain1",
				Address:         "addr1",
			}

			mock.ExpectExec("insert into `normal_transaction`").
				WillReturnError(fmt.Errorf("insert error"))

			result, err := model.Insert(context.Background(), tx)
			assert.Error(t, err)
			assert.Nil(t, result)
			assert.Contains(t, err.Error(), "insert error")
		})
	})
}

func TestNormalTransaction_InsertBatch(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		t.Run("empty_data", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customNormalTransactionModel{
				defaultNormalTransactionModel: mustNewNormalTransactionModel(conn),
			}

			_, err := model.InsertBatch(context.Background(), nil, nil)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "empty data")
		})

		t.Run("exceed_max_size", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customNormalTransactionModel{
				defaultNormalTransactionModel: mustNewNormalTransactionModel(conn),
			}
			data := make([]*NormalTransaction, maxBatchSize+1)
			_, err := model.InsertBatch(context.Background(), data, nil)
			assert.Error(t, err)
		})

		t.Run("success_ignore_error", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customNormalTransactionModel{
				defaultNormalTransactionModel: mustNewNormalTransactionModel(conn),
			}
			data := []*NormalTransaction{
				{
					TransactionHash: "hash1",
					BlockHash:       "block1",
					ChainType:       "chain1",
					Address:         "addr1",
					MethodId:        "method1",
					Nonce:           "1",
					GasPrice:        "1000",
					GasLimit:        "2000",
					GasUsed:         "1500",
					Height:          100,
					TransactionTime: 1740063284739,
					FromAddress:     "from1",
					ToAddress:       "to1",
					IsFromContract:  0,
					IsToContract:    1,
					Amount:          "100",
					CoinSymbol:      "ETH",
					TxFee:           "0.1",
					State:           "success",
					TransactionType: "0",
				},
			}

			mock.ExpectExec("insert ignore into `normal_transaction`").
				WithArgs(
					"hash1", "block1", "chain1", "addr1", "method1",
					"1", "1000", "2000", "1500", 100,
					int64(1740063284739), "from1", "to1", int16(0), int16(1),
					"100", "ETH", "0.1", "success", "0",
				).
				WillReturnResult(sqlmock.NewResult(1, 1))

			_, err := model.InsertBatch(context.Background(), data, &InsertBatchOption{
				IgnoreErrors: true,
			})
			assert.NoError(t, err)
		})

		t.Run("success_update_on_duplicate", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customNormalTransactionModel{
				defaultNormalTransactionModel: mustNewNormalTransactionModel(conn),
			}
			data := []*NormalTransaction{
				{
					TransactionHash: "hash1",
					BlockHash:       "block1",
					ChainType:       "chain1",
					Address:         "addr1",
					MethodId:        "method1",
					Nonce:           "1",
					GasPrice:        "1000",
					GasLimit:        "2000",
					GasUsed:         "1500",
					Height:          100,
					TransactionTime: 1740063284739,
					FromAddress:     "from1",
					ToAddress:       "to1",
					IsFromContract:  0,
					IsToContract:    1,
					Amount:          "100",
					CoinSymbol:      "ETH",
					TxFee:           "0.1",
					State:           "success",
					TransactionType: "0",
				},
			}

			mock.ExpectExec("insert into `normal_transaction`.*ON DUPLICATE KEY UPDATE").
				WithArgs(
					"hash1", "block1", "chain1", "addr1", "method1",
					"1", "1000", "2000", "1500", 100,
					int64(1740063284739), "from1", "to1", int16(0), int16(1),
					"100", "ETH", "0.1", "success", "0",
				).
				WillReturnResult(sqlmock.NewResult(1, 1))

			_, err := model.InsertBatch(context.Background(), data, &InsertBatchOption{
				OnDuplicateKeyUpdate: true,
			})
			assert.NoError(t, err)
		})
	})
}

func TestNormalTransaction_FindLastHeight(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		t.Run("success", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customNormalTransactionModel{
				defaultNormalTransactionModel: mustNewNormalTransactionModel(conn),
			}

			mock.ExpectQuery("SELECT MAX\\(height\\) as max_height FROM `normal_transaction` WHERE chain_type = \\? AND address = \\?").
				WithArgs("chain1", "addr1").
				WillReturnRows(sqlmock.NewRows([]string{"max_height"}).
					AddRow(100))

			height, err := model.FindLastHeight(context.Background(), "chain1", "addr1")
			assert.NoError(t, err)
			assert.Equal(t, int64(100), height)
		})

		t.Run("empty_chain", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customNormalTransactionModel{
				defaultNormalTransactionModel: mustNewNormalTransactionModel(conn),
			}

			height, err := model.FindLastHeight(context.Background(), "", "addr1")
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "chain and address cannot be empty")
			assert.Equal(t, int64(0), height)
		})

		t.Run("empty_address", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customNormalTransactionModel{
				defaultNormalTransactionModel: mustNewNormalTransactionModel(conn),
			}

			height, err := model.FindLastHeight(context.Background(), "chain1", "")
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "chain and address cannot be empty")
			assert.Equal(t, int64(0), height)
		})

		t.Run("both_empty", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customNormalTransactionModel{
				defaultNormalTransactionModel: mustNewNormalTransactionModel(conn),
			}

			height, err := model.FindLastHeight(context.Background(), "", "")
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "chain and address cannot be empty")
			assert.Equal(t, int64(0), height)
		})

		t.Run("no_data", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customNormalTransactionModel{
				defaultNormalTransactionModel: mustNewNormalTransactionModel(conn),
			}

			mock.ExpectQuery("SELECT MAX\\(height\\) as max_height FROM `normal_transaction` WHERE chain_type = \\? AND address = \\?").
				WithArgs("chain1", "addr1").
				WillReturnRows(sqlmock.NewRows([]string{"max_height"}).
					AddRow(nil))

			height, err := model.FindLastHeight(context.Background(), "chain1", "addr1")
			assert.NoError(t, err)
			assert.Equal(t, int64(0), height)
		})

		t.Run("query_error", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customNormalTransactionModel{
				defaultNormalTransactionModel: mustNewNormalTransactionModel(conn),
			}

			mock.ExpectQuery("SELECT MAX\\(height\\) as max_height FROM `normal_transaction` WHERE chain_type = \\? AND address = \\?").
				WithArgs("chain1", "addr1").
				WillReturnError(errors.New("query error"))

			_, err := model.FindLastHeight(context.Background(), "chain1", "addr1")
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "query error")
		})
	})
}
