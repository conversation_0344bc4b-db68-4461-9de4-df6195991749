package model

import (
	"context"
	"fmt"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
)

var _ SyncAddressLabelModel = (*customSyncAddressLabelModel)(nil)

const (
	syncAddressLabelLimit   = 1000
	syncAddressLabelMinPage = 1
)

type TxType = int16

const (
	TxTypeNormal TxType = 1
	TxTypeToken  TxType = 2
	TxTypeBoth   TxType = 3
)

type (
	// SyncAddressLabelModel is an interface to be customized, add more methods here,
	// and implement the added methods in customSyncAddressLabelModel.
	SyncAddressLabelModel interface {
		syncAddressLabelModel
		FindByChain(ctx context.Context, chain string, limit, page int32) ([]*SyncAddressLabel, error)
		CountByChain(ctx context.Context, chain string) (int64, error)
		FindAllByChain(ctx context.Context, chain string) ([]*SyncAddressLabel, error)
		FindAllChain(ctx context.Context) ([]string, error)
	}

	customSyncAddressLabelModel struct {
		*defaultSyncAddressLabelModel
	}

	// InsertBatchOption defines options for batch insert
	InsertBatchOption struct {
		// IgnoreErrors if true, uses INSERT IGNORE to skip rows that would cause errors
		IgnoreErrors bool
		// OnDuplicateKeyUpdate if true, updates existing rows on unique key conflict
		OnDuplicateKeyUpdate bool
	}
)

// MustNewSyncAddressLabelModel returns a model for the database table.
func MustNewSyncAddressLabelModel(conf sqlx.Config, opts ...sqlx.SqlOption) SyncAddressLabelModel {
	return &customSyncAddressLabelModel{
		defaultSyncAddressLabelModel: mustNewSyncAddressLabelModel(conf.MustNewMysql(opts...)),
	}
}

// FindByChain returns paginated records for a given chain
func (m *customSyncAddressLabelModel) FindByChain(ctx context.Context, chain string, limit, page int32) ([]*SyncAddressLabel, error) {
	if chain == "" {
		return nil, fmt.Errorf("chain parameter cannot be empty")
	}

	var labels []*SyncAddressLabel
	var args []interface{}

	query := fmt.Sprintf("SELECT * FROM %s WHERE `chain_type` = ?", m.table)
	args = append(args, chain)

	if page <= 0 {
		page = syncAddressLabelMinPage
	}

	// Add pagination
	if limit > 0 {
		query += " ORDER BY id DESC LIMIT ? OFFSET ?"
		args = append(args, limit, (page-1)*limit)
	}

	if err := m.conn.QueryRowsCtx(ctx, &labels, query, args...); err != nil {
		return nil, fmt.Errorf("FindByChain error: %w", err)
	}

	return labels, nil
}

// CountByChain returns the total number of records for a given chain
func (m *customSyncAddressLabelModel) CountByChain(ctx context.Context, chain string) (int64, error) {
	var count int64
	query := fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE `chain_type` = ?", m.table)

	err := m.conn.QueryRowCtx(ctx, &count, query, chain)
	if err != nil {
		return 0, fmt.Errorf("CountByChain error: %w", err)
	}

	return count, nil
}

// FindAllByChain returns all records for a given chain
func (m *customSyncAddressLabelModel) FindAllByChain(ctx context.Context, chain string) ([]*SyncAddressLabel, error) {
	count, err := m.CountByChain(ctx, chain)
	if err != nil {
		return nil, fmt.Errorf("CountByChain error: %w", err)
	}
	if count == 0 {
		return nil, fmt.Errorf("no data found for chain: %s", chain)
	}

	// 如果数量小于等于1000，则直接返回
	if count <= syncAddressLabelLimit {
		return m.FindByChain(ctx, chain, 0, 0)
	}

	// 如果数量大于1000，则分页查询
	page := count / syncAddressLabelLimit
	if count%syncAddressLabelLimit != 0 {
		page++
	}

	addressLabels := make([]*SyncAddressLabel, 0, count)
	for i := 1; i <= int(page); i++ {
		labels, err := m.FindByChain(ctx, chain, syncAddressLabelLimit, int32(i))
		if err != nil {
			return nil, fmt.Errorf("FindByChain error: %w", err)
		}
		addressLabels = append(addressLabels, labels...)
	}

	return addressLabels, nil
}

func (m *customSyncAddressLabelModel) FindAllChain(ctx context.Context) ([]string, error) {
	query := fmt.Sprintf("SELECT DISTINCT `chain_type` FROM %s", m.table)

	var chains []string
	err := m.conn.QueryRowsCtx(ctx, &chains, query)
	if err != nil {
		return nil, fmt.Errorf("FindAllChain error: %w", err)
	}

	return chains, nil
}
