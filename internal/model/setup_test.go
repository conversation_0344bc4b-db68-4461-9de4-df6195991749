package model

import (
	"database/sql"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
)

// RunTest runs a test function with a mock database.
func RunTest(t *testing.T, fn func(db *sql.DB, mock sqlmock.Sqlmock)) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}

	// 执行测试函数
	fn(db, mock)

	if err = mock.ExpectationsWereMet(); err != nil {
		t.<PERSON>rf("there were unfulfilled expectations: %s", err)
	}

	// 添加期望数据库关闭操作
	mock.ExpectClose()

	// 测试函数执行完成后关闭连接
	if err = db.Close(); err != nil {
		t.Errorf("error closing db connection: %s", err)
	}
}
