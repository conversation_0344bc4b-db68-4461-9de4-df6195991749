package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	allAddressLabelFieldNames          = builder.RawFieldNames(&AllAddressLabel{})
	allAddressLabelRows                = strings.Join(allAddressLabelFieldNames, ",")
	allAddressLabelRowsExpectAutoSet   = strings.Join(stringx.Remove(allAddressLabelFieldNames, "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	allAddressLabelRowsWithPlaceHolder = strings.Join(stringx.Remove(allAddressLabelFieldNames, "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	allAddressLabelModel interface {
		Insert(ctx context.Context, data *AllAddressLabel) (sql.Result, error)
		FindOneByAddressChain(ctx context.Context, address string, chain string) (*AllAddressLabel, error)
		Update(ctx context.Context, data *AllAddressLabel) error
		Delete(ctx context.Context, address string, chain string) error
	}

	defaultAllAddressLabelModel struct {
		conn  sqlx.SqlConn
		table string
	}

	AllAddressLabel struct {
		Id         uint64    `db:"id" json:"id"`                   // 兼容字段
		Chain      string    `db:"chain" json:"chain"`             // 链
		Address    string    `db:"address" json:"address"`         // 查询的地址
		Category   string    `db:"category" json:"category"`       // 标签
		EntityName string    `db:"entity_name" json:"entity_name"` // 地址所属控制实体的标识
		DetailName string    `db:"detail_name" json:"detail_name"` // 地址详情列表
		Editor     string    `db:"editor" json:"editor"`           // 查询的地址
		Source     string    `db:"source" json:"source"`           // 标签来源
		Remark     string    `db:"remark" json:"remark"`           // 标签备注
		Valid      int32     `db:"valid" json:"valid"`             // 是否有效【0:标签无效，1:标签有效】
		CreatedAt  time.Time `db:"created_at" json:"created_at"`   // 创建时间
		UpdatedAt  time.Time `db:"updated_at" json:"updated_at"`   // 更新时间
	}
)

func mustNewAllAddressLabelModel(conn sqlx.SqlConn) *defaultAllAddressLabelModel {
	return &defaultAllAddressLabelModel{
		conn:  conn,
		table: "`all_address_label`",
	}
}

func (m *defaultAllAddressLabelModel) FindOneByAddressChain(ctx context.Context, address string, chain string) (*AllAddressLabel, error) {
	var resp AllAddressLabel
	query := fmt.Sprintf("select %s from %s where `address` = ? and `chain` = ? limit 1", allAddressLabelRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, address, chain)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAllAddressLabelModel) Insert(ctx context.Context, data *AllAddressLabel) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, allAddressLabelRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Chain, data.Address, data.Category, data.EntityName, data.DetailName, data.Editor, data.Source, data.Remark, data.Valid, data.Id)
	return ret, err
}

func (m *defaultAllAddressLabelModel) Update(ctx context.Context, data *AllAddressLabel) error {
	query := fmt.Sprintf("update %s set %s where `address` = ? and `chain` = ?", m.table, allAddressLabelRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Id, data.Category, data.EntityName, data.DetailName, data.Editor, data.Source, data.Remark, data.Valid, data.Address, data.Chain)
	return err
}

func (m *defaultAllAddressLabelModel) Delete(ctx context.Context, address string, chain string) error {
	query := fmt.Sprintf("delete from %s where `address` = ? and `chain` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, address, chain)
	return err
}

func (m *defaultAllAddressLabelModel) tableName() string {
	return m.table
}
