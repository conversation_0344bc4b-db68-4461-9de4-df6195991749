create table entity_name_mapping
(
    `id`              bigint unsigned                                                                         NOT NULL AUTO_INCREMENT COMMENT 'id',
    `entity_name_key` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT ''                NOT NULL COMMENT '映射的 key[entity name | source name]',
    `entity_name_val` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT ''                NOT NULL COMMENT '映射的 value',
    `category_val`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT ''                NOT NULL COMMENT 'category 归类',
    `valid`           int                                                           DEFAULT 0                 NOT NULL COMMENT '是否有效[0: 无效, 1: 有效]',
    `mapping_type`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT ''                NOT NULL COMMENT '映射类型[entity|source]',
    `has_mapping`     int                                                           DEFAULT 0                 NOT NULL COMMENT '是否已映射: [0: 未映射, 1: 已映射]',
    `editor`          varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT ''                NOT NULL COMMENT '最后一次修改的用户',
    `remark`          varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT ''                NOT NULL COMMENT '备注',
    `created_at`      datetime                                                      DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    `updated_at`      datetime                                                      DEFAULT CURRENT_TIMESTAMP NOT NULL on update CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_entity_name_key` (`entity_name_key`),
    INDEX `idx_created_at` (`created_at` DESC),
    INDEX `idx_mapping_type` (`mapping_type`),
    INDEX `idx_entity_name_key` (`entity_name_key`),
    INDEX `idx_has_mapping` (`has_mapping`),
    INDEX `idx_entity_name_val` (`entity_name_val`)
) comment '供应商 entity name 映射信息' charset = utf8mb4;
