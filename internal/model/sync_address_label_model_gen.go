// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	syncAddressLabelFieldNames          = builder.RawFieldNames(&SyncAddressLabel{})
	syncAddressLabelRows                = strings.Join(syncAddressLabelFieldNames, ",")
	syncAddressLabelRowsExpectAutoSet   = strings.Join(stringx.Remove(syncAddressLabelFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	syncAddressLabelRowsWithPlaceHolder = strings.Join(stringx.Remove(syncAddressLabelFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	syncAddressLabelModel interface {
		Insert(ctx context.Context, data *SyncAddressLabel) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*SyncAddressLabel, error)
		FindOneByAddressChainType(ctx context.Context, address string, chainType string) (*SyncAddressLabel, error)
		Update(ctx context.Context, newData *SyncAddressLabel) error
	}

	defaultSyncAddressLabelModel struct {
		conn  sqlx.SqlConn
		table string
	}

	SyncAddressLabel struct {
		Id        uint64    `db:"id"`         // 主键id
		ChainType string    `db:"chain_type"` // 区块链网络标识
		Address   string    `db:"address"`    // 地址
		IsBlack   int16     `db:"is_black"`   // 是否黑名单地址
		TxType    int16     `db:"tx_type"`    // 交易类型: 1为本币, 2为代币, 3为两个都同步
		CreatedAt time.Time `db:"created_at"` // 创建时间
		UpdatedAt time.Time `db:"updated_at"` // 更新时间
	}
)

func mustNewSyncAddressLabelModel(conn sqlx.SqlConn) *defaultSyncAddressLabelModel {
	return &defaultSyncAddressLabelModel{
		conn:  conn,
		table: "`sync_address_label`",
	}
}

func (m *defaultSyncAddressLabelModel) FindOne(ctx context.Context, id uint64) (*SyncAddressLabel, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", syncAddressLabelRows, m.table)
	var resp SyncAddressLabel
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultSyncAddressLabelModel) FindOneByAddressChainType(ctx context.Context, address string, chainType string) (*SyncAddressLabel, error) {
	var resp SyncAddressLabel
	query := fmt.Sprintf("select %s from %s where `address` = ? and `chain_type` = ? limit 1", syncAddressLabelRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, address, chainType)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultSyncAddressLabelModel) Insert(ctx context.Context, data *SyncAddressLabel) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, syncAddressLabelRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.ChainType, data.Address, data.IsBlack, data.TxType)
	return ret, err
}

func (m *defaultSyncAddressLabelModel) Update(ctx context.Context, newData *SyncAddressLabel) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, syncAddressLabelRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.ChainType, newData.Address, newData.IsBlack, newData.TxType, newData.Id)
	return err
}

func (m *defaultSyncAddressLabelModel) tableName() string {
	return m.table
}
