// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	amlLabelTriggerFieldNames          = builder.RawFieldNames(&AmlLabelTrigger{})
	amlLabelTriggerRows                = strings.Join(amlLabelTriggerFieldNames, ",")
	amlLabelTriggerRowsExpectAutoSet   = strings.Join(stringx.Remove(amlLabelTriggerFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	amlLabelTriggerRowsWithPlaceHolder = strings.Join(stringx.Remove(amlLabelTriggerFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	amlLabelTriggerModel interface {
		Insert(ctx context.Context, data *AmlLabelTrigger) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*AmlLabelTrigger, error)
		Update(ctx context.Context, newData *AmlLabelTrigger) error
	}

	defaultAmlLabelTriggerModel struct {
		conn  sqlx.SqlConn
		table string
	}

	AmlLabelTrigger struct {
		Id                                  uint64    `db:"id"`                                       // id
		TriggerLabel                        string    `db:"trigger_label"`                            // 命中label
		RiskLevel                           string    `db:"risk_level"`                               // risk level
		ExposureType                        string    `db:"exposure_type"`                            // exposure type
		ManuallyAuto                        string    `db:"manually_auto"`                            // manually_auto
		AssetIsBf                           string    `db:"asset_is_bf"`                              // block fund
		NeedEdd                             string    `db:"need_edd"`                                 // need_edd
		SuggestedAc                         string    `db:"suggested_ac"`                             // suggested_ac
		SuggestedQoa                        string    `db:"suggested_qoa"`                            // suggested_qoa
		SuggestedNoa                        string    `db:"suggested_noa"`                            // suggested_noa
		SuggestedAtb                        string    `db:"suggested_atb"`                            // suggested_atb
		SuggestedAtb2                       string    `db:"suggested_atb2"`                           // suggested_atb2
		SuggestedAwb                        string    `db:"suggested_awb"`                            // suggested_awb
		SuggestedStr                        string    `db:"suggested_str"`                            // suggested_str
		VendorSource                        string    `db:"vendor_source"`                            // vendor_source
		TriggerRiskValue                    string    `db:"trigger_risk_value"`                       // trigger_risk_value
		TriggerRiskUsdValue                 string    `db:"trigger_risk_usd_value"`                   // trigger_risk_usd_value
		TriggerRiskRatio                    string    `db:"trigger_risk_ratio"`                       // trigger_risk_ratio
		TriggerRiskEntityList               string    `db:"trigger_risk_entity_list"`                 // trigger_risk_entity_list
		TriggerThisCategoryUsdOneMonth      string    `db:"trigger_this_category_usd_one_month"`      // trigger_this_category_usd_one_month
		TriggerThisCategoryUsdLifetime      string    `db:"trigger_this_category_usd_lifetime"`       // trigger_this_category_usd_lifetime
		Status                              int32     `db:"status"`                                   // column status
		TriggerThisCategoryWithdrawOneMonth string    `db:"trigger_this_category_withdraw_one_month"` // trigger_this_category_withdraw_one_month
		Editor                              string    `db:"editor"`                                   // 最后编辑人
		Remark                              string    `db:"remark"`                                   // 备注
		CreatedAt                           time.Time `db:"created_at"`                               // 创建时间
		UpdatedAt                           time.Time `db:"updated_at"`                               // 更新时间
	}
)

func mustNewAmlLabelTriggerModel(conn sqlx.SqlConn) *defaultAmlLabelTriggerModel {
	return &defaultAmlLabelTriggerModel{
		conn:  conn,
		table: "`aml_label_trigger`",
	}
}

func (m *defaultAmlLabelTriggerModel) FindOne(ctx context.Context, id uint64) (*AmlLabelTrigger, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", amlLabelTriggerRows, m.table)
	var resp AmlLabelTrigger
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAmlLabelTriggerModel) Insert(ctx context.Context, data *AmlLabelTrigger) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, amlLabelTriggerRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.TriggerLabel, data.RiskLevel, data.ExposureType, data.ManuallyAuto, data.AssetIsBf, data.NeedEdd, data.SuggestedAc, data.SuggestedQoa, data.SuggestedNoa, data.SuggestedAtb, data.SuggestedAtb2, data.SuggestedAwb, data.SuggestedStr, data.VendorSource, data.TriggerRiskValue, data.TriggerRiskUsdValue, data.TriggerRiskRatio, data.TriggerRiskEntityList, data.TriggerThisCategoryUsdOneMonth, data.TriggerThisCategoryUsdLifetime, data.Status, data.TriggerThisCategoryWithdrawOneMonth, data.Editor, data.Remark)
	return ret, err
}

func (m *defaultAmlLabelTriggerModel) Update(ctx context.Context, data *AmlLabelTrigger) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, amlLabelTriggerRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.TriggerLabel, data.RiskLevel, data.ExposureType, data.ManuallyAuto, data.AssetIsBf, data.NeedEdd, data.SuggestedAc, data.SuggestedQoa, data.SuggestedNoa, data.SuggestedAtb, data.SuggestedAtb2, data.SuggestedAwb, data.SuggestedStr, data.VendorSource, data.TriggerRiskValue, data.TriggerRiskUsdValue, data.TriggerRiskRatio, data.TriggerRiskEntityList, data.TriggerThisCategoryUsdOneMonth, data.TriggerThisCategoryUsdLifetime, data.Status, data.TriggerThisCategoryWithdrawOneMonth, data.Editor, data.Remark, data.Id)
	return err
}

func (m *defaultAmlLabelTriggerModel) tableName() string {
	return m.table
}
