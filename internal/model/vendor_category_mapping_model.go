package model

import (
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"context"
	"fmt"
	"strings"
)

var _ VendorCategoryMappingModel = (*customVendorCategoryMappingModel)(nil)

type (
	// VendorCategoryMappingModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVendorCategoryMappingModel.
	VendorCategoryMappingModel interface {
		vendorCategoryMappingModel
		QueryByVendorCategoryName(ctx context.Context, id uint64, categoryName string, limit, page int32) ([]*VendorCategoryMapping, error)
	}

	customVendorCategoryMappingModel struct {
		*defaultVendorCategoryMappingModel
	}
)

// MustNewVendorCategoryMappingModel returns a model for the database table.
func MustNewVendorCategoryMappingModel(conf sqlx.Config, opts ...sqlx.SqlOption) VendorCategoryMappingModel {
	return &customVendorCategoryMappingModel{
		defaultVendorCategoryMappingModel: mustNewVendorCategoryMappingModel(conf.MustNewMysql(opts...)),
	}
}

func (m *customVendorCategoryMappingModel) QueryByVendorCategoryName(ctx context.Context, id uint64, categoryName string, limit, page int32) ([]*VendorCategoryMapping, error) {
	var (
		resp         []*VendorCategoryMapping
		whereClauses []string
		args         []any
	)

	if id != 0 {
		whereClauses = append(whereClauses, "id = ?")
		args = append(args, id)
	}
	if len(categoryName) != 0 {
		whereClauses = append(whereClauses, "category_key = ?")
		args = append(args, categoryName)
	}

	// 构建查询语句
	query := fmt.Sprintf("SELECT * FROM %s", m.table)
	if len(whereClauses) != 0 {
		query += " WHERE " + strings.Join(whereClauses, " AND ")
	}
	if limit > 0 {
		query += fmt.Sprintf(" LIMIT ? OFFSET ?")
		args = append(args, limit, (page-1)*limit)
	}

	// 执行查询
	if err := m.conn.QueryRowsCtx(ctx, &resp, query, args...); err != nil {
		return nil, err
	}

	return resp, nil
}
