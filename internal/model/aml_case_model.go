package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"github.com/go-xorm/builder"
	"github.com/shopspring/decimal"
)

var _ AmlCaseModel = (*customAmlCaseModel)(nil)

type (
	// AmlCaseModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAmlCaseModel.
	AmlCaseModel interface {
		amlCaseModel
		FindListByMemberIDAndActionType(ctx context.Context,
			memberId int64, actionType int, limit, page int) ([]*AmlCase, error)
		FindOneByMemberIDAndRequestID(ctx context.Context, memberID int64, requestID string) (*AmlCase, error)
		FindListByMemberID(ctx context.Context, memberId int64, page, limit int) ([]*AmlTransaction, int64, error)
		FindFirstStrByMemberID(ctx context.Context, memberId int64) (*AmlTransaction, error)
		FindAllByMemberID(ctx context.Context, memberId int64) ([]*AmlTransaction, error)
		FindDepositAddressByMemberID(ctx context.Context, memberId int64) ([]*MemberDepositAddress, error)
		UpdateTxHash(ctx context.Context, requestID string, txHash string) error
		InsertWithTime(ctx context.Context, data *AmlCase) (sql.Result, error)
	}

	customAmlCaseModel struct {
		*defaultAmlCaseModel
	}
)

const (
	TYPE_DEPOSIT          = 1
	TYPE_WITHDRAWAL       = 2
	TYPE_PREWITHDRAWAL    = 3
	TYPE_CONTRACT_DEPOSIT = 4
	TYPE_FIAT_WITHDRAW    = 12
	TYPE_FIAT_DEPOSIT     = 10
	TYPE_FIAT_CARD_BUY    = 11
	TYPE_FIAT_BALANCE_BUY = 13
)

// MustNewAmlCaseModel returns a model for the database table.
func MustNewAmlCaseModel(conf sqlx.Config, opts ...sqlx.SqlOption) AmlCaseModel {
	return &customAmlCaseModel{
		defaultAmlCaseModel: mustNewAmlCaseModel(conf.MustNewMysql(opts...)),
	}
}

func (m *customAmlCaseModel) FindListByMemberIDAndActionType(ctx context.Context,
	memberId int64, actionType int, limit, page int) ([]*AmlCase, error) {
	cond := builder.NewCond()
	if memberId != 0 {
		cond = cond.And(builder.Eq{"member_id": memberId})
	}
	if actionType != 0 {
		cond = cond.And(builder.Eq{"action_type": actionType})
	}
	if limit <= 0 {
		limit = 10
	}
	if page <= 0 {
		page = 1
	}

	query, args, err := builder.MySQL().
		Select(amlCaseRows).
		From(m.table).Where(cond).
		Limit(limit, (page-1)*limit).
		OrderBy("id desc").ToSQL()
	if err != nil {
		return nil, err
	}
	var resp []*AmlCase
	if err := m.conn.QueryRowsCtx(ctx, &resp, query, args...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *customAmlCaseModel) FindOneByMemberIDAndRequestID(ctx context.Context, memberID int64, requestID string) (*AmlCase, error) {
	cond := builder.NewCond()
	if memberID != 0 {
		cond = cond.And(builder.Eq{"member_id": memberID})
	}
	if requestID != "" {
		cond = cond.And(builder.Eq{"request_id": requestID})
	}
	query, args, err := builder.MySQL().
		Select(amlCaseRows).
		From(m.table).Where(cond).
		Limit(1).
		ToSQL()
	if err != nil {
		return nil, err
	}
	var resp AmlCase
	if err := m.conn.QueryRowCtx(ctx, &resp, query, args...); err != nil {
		return nil, err
	}
	return &resp, nil
}

func (m *customAmlCaseModel) InsertWithTime(ctx context.Context, data *AmlCase) (sql.Result, error) {
	columns := amlCaseRowsExpectAutoSet
	values := []interface{}{
		data.RequestId, data.Token, data.MemberId, data.Chain, data.Coin, data.FromAddress,
		data.ToAddress, data.TxHash, data.Amount, data.ActionType, data.Decision,
		data.SuggestedAction, data.Status, data.Remark, data.AmountUsd, data.Disposal,
		data.ExternalStatus, data.ExternalProcess, data.TxIndex, data.BlockHash,
		data.GroupId, data.UtxoAddresses, data.Label,
	}
	placeholders := strings.Repeat("?,", len(values)-1) + "?"

	// Handle CreatedAt
	if !data.CreatedAt.IsZero() {
		columns += ",`created_at`"
		values = append(values, data.CreatedAt)
		placeholders += ",?"
	}

	// Handle UpdatedAt
	if !data.UpdatedAt.IsZero() {
		columns += ",`updated_at`"
		values = append(values, data.UpdatedAt)
		placeholders += ",?"
	} else if !data.CreatedAt.IsZero() {
		columns += ",`updated_at`"
		values = append(values, data.CreatedAt)
		placeholders += ",?"
	}

	query := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)", m.table, columns, placeholders)

	ret, err := m.conn.ExecCtx(ctx, query, values...)
	return ret, err
}

func (m *customAmlCaseModel) UpdateTxHash(ctx context.Context, requestID string, txHash string) error {
	query, args, err := builder.MySQL().
		Update(builder.Eq{"tx_hash": txHash}).
		From(m.table).
		Where(builder.Eq{"request_id": requestID}).
		ToSQL()
	if err != nil {
		return err
	}

	_, err = m.conn.ExecCtx(ctx, query, args...)
	return err
}

type AmlTransaction struct {
	SuggestedStr sql.NullString  `db:"suggested_str"` // 是否str ：yes/no
	CreatedAt    time.Time       `db:"created_at"`    // 创建时间
	ActionType   int             `db:"action_type"`   // 交易类型
	FromAddress  sql.NullString  `db:"from_address"`  // 来源地址
	ToAddress    sql.NullString  `db:"to_address"`    // 目标地址
	Chain        sql.NullString  `db:"chain"`         // 链
	Coin         sql.NullString  `db:"coin"`          // 币种
	Amount       decimal.Decimal `db:"amount"`        // 数量
	TxHash       sql.NullString  `db:"tx_hash"`       // 交易hash
}

func (m *customAmlCaseModel) FindAllByMemberID(ctx context.Context, memberId int64) ([]*AmlTransaction, error) {
	rawQuery := `SELECT
IFNULL(l.suggested_str, 'no') AS suggested_str,  c.created_at, c.action_type , c.from_address, c.to_address, c.chain, c.coin, c.amount,c.tx_hash  from (
        select created_at,action_type,
    from_address COLLATE utf8mb4_general_ci as  from_address,
    to_address COLLATE utf8mb4_general_ci as to_address ,
    chain COLLATE utf8mb4_general_ci as chain,
    coin COLLATE utf8mb4_general_ci as coin,
    amount,
    member_id,
    id,
    label COLLATE utf8mb4_general_ci as label,
    tx_hash COLLATE utf8mb4_general_ci as tx_hash
from aml_case_v2 where member_id = ?
UNION
select created_at,action_type,
    from_address COLLATE utf8mb4_general_ci as  from_address,
    to_address COLLATE utf8mb4_general_ci as to_address ,
    chain COLLATE utf8mb4_general_ci as chain,
    coin COLLATE utf8mb4_general_ci as coin,
    amount,
    member_id,
    id,
    label COLLATE utf8mb4_general_ci as label,
    tx_hash COLLATE utf8mb4_general_ci as tx_hash
from aml_case where member_id = ?
    ) AS c
LEFT JOIN aml_label_trigger AS l on l.trigger_label = c.label
where member_id = ?
order by c.id desc
	`
	args := []interface{}{memberId, memberId, memberId}
	var resp []*AmlTransaction
	if err := m.conn.QueryRowsCtx(ctx, &resp, rawQuery, args...); err != nil {
		return nil, err
	}
	handleSuggestedStr(resp)
	return resp, nil
}

// suggested_str 这个字段review视为no。仅针对自动化生成STR项目
func handleSuggestedStr(txes []*AmlTransaction) {
	for _, tx := range txes {
		if tx.SuggestedStr.Valid && strings.ToLower(tx.SuggestedStr.String) == "review" {
			tx.SuggestedStr.String = "no"
		}
	}
}

func (m *customAmlCaseModel) FindListByMemberID(ctx context.Context,
	memberId int64, page, limit int) ([]*AmlTransaction, int64, error) {
	rawQuery := `SELECT
IFNULL(l.suggested_str, 'no') AS suggested_str,  c.created_at, c.action_type , c.from_address, c.to_address, c.chain, c.coin, c.amount,c.tx_hash  from (
        select created_at,action_type,
    from_address COLLATE utf8mb4_general_ci as  from_address,
    to_address COLLATE utf8mb4_general_ci as to_address ,
    chain COLLATE utf8mb4_general_ci as chain,
    coin COLLATE utf8mb4_general_ci as coin,
    amount,
    member_id,
    id,
    label COLLATE utf8mb4_general_ci as label,
    tx_hash COLLATE utf8mb4_general_ci as tx_hash
from aml_case_v2 where member_id = ?
UNION
select created_at,action_type,
    from_address COLLATE utf8mb4_general_ci as  from_address,
    to_address COLLATE utf8mb4_general_ci as to_address ,
    chain COLLATE utf8mb4_general_ci as chain,
    coin COLLATE utf8mb4_general_ci as coin,
    amount,
    member_id,
    id,
    label COLLATE utf8mb4_general_ci as label,
    tx_hash COLLATE utf8mb4_general_ci as tx_hash
from aml_case where member_id = ?
    ) AS c
LEFT JOIN aml_label_trigger AS l on l.trigger_label = c.label
where member_id = ?
order by c.id desc LIMIT ?, ?
	`
	args := []interface{}{memberId, memberId, memberId, (page - 1) * limit, limit}
	var resp []*AmlTransaction
	if err := m.conn.QueryRowsCtx(ctx, &resp, rawQuery, args...); err != nil {
		return nil, 0, err
	}
	handleSuggestedStr(resp)
	totalQuery := `SELECT COUNT(*) as c FROM (
        select created_at,action_type,
    from_address COLLATE utf8mb4_general_ci as  from_address,
    to_address COLLATE utf8mb4_general_ci as to_address ,
    chain COLLATE utf8mb4_general_ci as chain,
    coin COLLATE utf8mb4_general_ci as coin,
    amount,
    member_id,
    id,
    label COLLATE utf8mb4_general_ci as label,
    tx_hash COLLATE utf8mb4_general_ci as tx_hash
from aml_case_v2 where member_id = ?
UNION
select created_at,action_type,
    from_address COLLATE utf8mb4_general_ci as  from_address,
    to_address COLLATE utf8mb4_general_ci as to_address ,
    chain COLLATE utf8mb4_general_ci as chain,
    coin COLLATE utf8mb4_general_ci as coin,
    amount,
    member_id,
    id,
    label COLLATE utf8mb4_general_ci as label,
    tx_hash COLLATE utf8mb4_general_ci as tx_hash
from aml_case as c where member_id = ?
    ) as c WHERE member_id = ?`
	var total = struct {
		C int64 `db:"c"`
	}{}
	if err := m.conn.QueryRowCtx(ctx, &total, totalQuery, memberId, memberId, memberId); err != nil {
		return nil, 0, err
	}
	return resp, total.C, nil
}

type MemberDepositAddress struct {
	Chain     string `db:"chain"`      // 链
	ToAddress string `db:"to_address"` // 目标地址
}

func (m *customAmlCaseModel) FindDepositAddressByMemberID(ctx context.Context, memberId int64) ([]*MemberDepositAddress, error) {
	var query = `SELECT DISTINCT chain,to_address from (
        select created_at,action_type,
    from_address COLLATE utf8mb4_general_ci as  from_address,
    to_address COLLATE utf8mb4_general_ci as to_address ,
    chain COLLATE utf8mb4_general_ci as chain,
    coin COLLATE utf8mb4_general_ci as coin,
    amount,
    member_id,
    id,
    label COLLATE utf8mb4_general_ci as label,
    tx_hash COLLATE utf8mb4_general_ci as tx_hash
from aml_case_v2 where member_id = ?
UNION
select created_at,action_type,
    from_address COLLATE utf8mb4_general_ci as  from_address,
    to_address COLLATE utf8mb4_general_ci as to_address ,
    chain COLLATE utf8mb4_general_ci as chain,
    coin COLLATE utf8mb4_general_ci as coin,
    amount,
    member_id,
    id,
    label COLLATE utf8mb4_general_ci as label,
    tx_hash COLLATE utf8mb4_general_ci as tx_hash
from aml_case where member_id = ?
    ) as c where action_type = 1 and member_id = ? and to_address != ''`
	args := []interface{}{memberId, memberId, memberId}
	var resp []*MemberDepositAddress
	if err := m.conn.QueryRowsCtx(ctx, &resp, query, args...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *customAmlCaseModel) FindFirstStrByMemberID(ctx context.Context, memberId int64) (*AmlTransaction, error) {
	rawQuery := `SELECT
	IFNULL(l.suggested_str, 'no') AS suggested_str,  c.created_at, c.action_type , c.from_address, c.to_address, c.chain, c.coin, c.amount,c.tx_hash  from (
        select created_at,action_type,
    from_address COLLATE utf8mb4_general_ci as  from_address,
    to_address COLLATE utf8mb4_general_ci as to_address ,
    chain COLLATE utf8mb4_general_ci as chain,
    coin COLLATE utf8mb4_general_ci as coin,
    amount,
    member_id,
    id,
    label COLLATE utf8mb4_general_ci as label,
    tx_hash COLLATE utf8mb4_general_ci as tx_hash
from aml_case_v2 where member_id = 1000854781
UNION
select created_at,action_type,
    from_address COLLATE utf8mb4_general_ci as  from_address,
    to_address COLLATE utf8mb4_general_ci as to_address ,
    chain COLLATE utf8mb4_general_ci as chain,
    coin COLLATE utf8mb4_general_ci as coin,
    amount,
    member_id,
    id,
    label COLLATE utf8mb4_general_ci as label,
    tx_hash COLLATE utf8mb4_general_ci as tx_hash
from aml_case where member_id = 1000854781
    ) AS c
	LEFT JOIN aml_label_trigger AS l on l.trigger_label = c.label
	where member_id = ? AND l.suggested_str = 'yes'
	order by c.id ASC LIMIT 1
		`
	var resp = &AmlTransaction{}
	if err := m.conn.QueryRowCtx(ctx, resp, rawQuery, memberId, memberId, memberId); err != nil {
		return nil, err
	}
	return resp, nil
}
