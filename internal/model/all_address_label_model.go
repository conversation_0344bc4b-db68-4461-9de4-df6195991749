package model

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
)

const (
	defaultLimit = 100
	defaultPage  = 1
)

var _ AllAddressLabelModel = (*customAllAddressLabelModel)(nil)

type (
	// AllAddressLabelModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAllAddressLabelModel.
	AllAddressLabelModel interface {
		allAddressLabelModel

		Upsert(ctx context.Context, data *AllAddressLabel) (int64, error)
		QueryAll(ctx context.Context, limit, page int32) ([]*AllAddressLabel, error)
		QueryByAddressAndChain(ctx context.Context, chain string, addressList []string, limit, page int32) ([]*AllAddressLabel, error)
		BatchUpdate(ctx context.Context, labels []*AllAddressLabel) error
		BatchQuery(ctx context.Context, addresses []string) ([]*AllAddressLabel, error)
	}

	customAllAddressLabelModel struct {
		*defaultAllAddressLabelModel
	}
)

// MustNewAllAddressLabelModel returns a model for the database table.
func MustNewAllAddressLabelModel(conf sqlx.Config, opts ...sqlx.SqlOption) AllAddressLabelModel {
	return &customAllAddressLabelModel{
		defaultAllAddressLabelModel: mustNewAllAddressLabelModel(conf.MustNewMysql(opts...)),
	}
}

func (m *customAllAddressLabelModel) Upsert(ctx context.Context, data *AllAddressLabel) (int64, error) {
	args := []any{data.Id, data.Chain, data.Address,
		data.Category, data.EntityName, data.DetailName, data.Editor, data.Source, data.Remark, data.Valid,
		data.Id, data.Category, data.EntityName, data.DetailName, data.Editor, data.Source, data.Remark, data.Valid}

	stmt := fmt.Sprintf(`insert into all_address_label (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
on DUPLICATE KEY update id = ?, category = ?, entity_name = ?, detail_name = ?, editor = ?, source = ?, remark = ?, valid = ?`, allAddressLabelRowsExpectAutoSet)
	result, err := m.conn.ExecCtx(ctx, stmt, args...)
	if err != nil {
		return 0, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, err
	}

	return rowsAffected, nil
}

func (m *customAllAddressLabelModel) BatchUpdate(ctx context.Context, labels []*AllAddressLabel) error {
	if len(labels) == 0 {
		return nil
	}

	for _, label := range labels {
		// 构建更新的字段和参数
		var setClauses []string
		var args []any

		// setClauses = append(setClauses, "`id` = ?")
		// args = append(args, label.Id)
		setClauses = append(setClauses, "`category` = ?")
		args = append(args, label.Category)
		setClauses = append(setClauses, "`entity_name` = ?")
		args = append(args, label.EntityName)
		setClauses = append(setClauses, "`detail_name` = ?")
		args = append(args, label.DetailName)
		setClauses = append(setClauses, "`editor` = ?")
		args = append(args, label.Editor)
		setClauses = append(setClauses, "`source` = ?")
		args = append(args, label.Source)
		setClauses = append(setClauses, "`remark` = ?")
		args = append(args, label.Remark)
		setClauses = append(setClauses, "`valid` = ?")
		args = append(args, label.Valid)

		// 执行更新
		query := fmt.Sprintf("UPDATE %s SET %s WHERE `address` = ? AND `chain` = ?", m.table, strings.Join(setClauses, ","))
		args = append(args, label.Address, label.Chain)
		if _, err := m.conn.ExecCtx(ctx, query, args...); err != nil {
			return err
		}
	}
	return nil
}

func (m *customAllAddressLabelModel) QueryAll(ctx context.Context, limit, page int32) ([]*AllAddressLabel, error) {
	var labels []*AllAddressLabel
	var args []any

	query := fmt.Sprintf("SELECT * FROM %s", m.table)
	query += " ORDER BY created_at DESC"

	if limit == 0 {
		limit = defaultLimit
	}

	if page == 0 {
		page = defaultPage
	}

	query += fmt.Sprintf(" LIMIT ? OFFSET ?")
	args = append(args, limit, (page-1)*limit)

	if err := m.conn.QueryRowsCtx(ctx, &labels, query, args...); err != nil {
		return nil, err
	}

	return labels, nil
}

func (m *customAllAddressLabelModel) QueryByAddressAndChain(ctx context.Context, chain string, addressList []string, limit, page int32) ([]*AllAddressLabel, error) {
	var labels []*AllAddressLabel

	if len(addressList) == 0 && chain == "" {
		return nil, errors.New("both addressList and chain are empty")
	}

	// 构建查询条件
	var whereClauses []string
	var args []any

	if chain != "" {
		whereClauses = append(whereClauses, "`chain` = ?")
		args = append(args, chain)
	}

	if len(addressList) > 0 {
		// 展开 addressList 参数
		placeholders := make([]string, len(addressList))
		for i := range addressList {
			placeholders[i] = "?"
			args = append(args, addressList[i])
		}
		whereClauses = append(whereClauses, fmt.Sprintf("`address` IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(whereClauses) == 0 {
		return labels, nil
	}

	if limit == 0 {
		limit = defaultLimit
	}

	if page == 0 {
		page = defaultPage
	}

	// 构建查询语句
	query := fmt.Sprintf("SELECT * FROM %s", m.table)
	query += " WHERE " + strings.Join(whereClauses, " AND ")
	query += " ORDER BY created_at DESC"
	query += fmt.Sprintf(" LIMIT ? OFFSET ?")
	args = append(args, limit, (page-1)*limit)

	// 执行查询
	if err := m.conn.QueryRowsCtx(ctx, &labels, query, args...); err != nil {
		return nil, err
	}

	return labels, nil
}

func (m *customAllAddressLabelModel) BatchQuery(ctx context.Context, addresses []string) ([]*AllAddressLabel, error) {
	if len(addresses) == 0 {
		return []*AllAddressLabel{}, nil
	}

	// 构建查询条件
	placeholders := make([]string, len(addresses))
	for i := range addresses {
		placeholders[i] = "?"
	}

	query := fmt.Sprintf(`SELECT * FROM %s WHERE address IN (%s)`, m.table, strings.Join(placeholders, ","))
	var labels []*AllAddressLabel
	var args []any
	for _, address := range addresses {
		args = append(args, address)
	}

	if err := m.conn.QueryRowsCtx(ctx, &labels, query, args...); err != nil {
		return nil, err
	}

	return labels, nil
}
