package model

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"testing"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestTokenTransaction_InsertBatch(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		t.Run("empty_data", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customTokenTransactionModel{
				defaultTokenTransactionModel: mustNewTokenTransactionModel(conn),
			}

			result, err := model.InsertBatch(context.Background(), nil, nil)
			assert.NoError(t, err)
			assert.Nil(t, result)
		})

		t.Run("exceed_max_size", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customTokenTransactionModel{
				defaultTokenTransactionModel: mustNewTokenTransactionModel(conn),
			}
			data := make([]*TokenTransaction, 1001)
			_, err := model.InsertBatch(context.Background(), data, nil)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "batch size 1001 exceeds maximum allowed size of 1000")
		})

		t.Run("success_ignore_error", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customTokenTransactionModel{
				defaultTokenTransactionModel: mustNewTokenTransactionModel(conn),
			}
			data := []*TokenTransaction{
				{
					TransactionHash:      "hash1",
					BlockHash:            "block1",
					ChainType:            "chain1",
					Address:              "addr1",
					Height:               100,
					TransactionTime:      1740063284739,
					FromAddress:          "from1",
					ToAddress:            "to1",
					IsFromContract:       0,
					IsToContract:         1,
					TokenContractAddress: "token1",
					Amount:               "100",
					TokenSymbol:          "ETH",
					TokenId:              "1",
				},
			}

			mock.ExpectExec("insert ignore into `token_transaction`").
				WithArgs(
					"hash1", "block1", "chain1", "addr1", int64(100),
					int64(1740063284739), "from1", "to1", int16(0), int16(1),
					"token1", "100", "ETH", "1",
				).
				WillReturnResult(sqlmock.NewResult(1, 1))

			_, err := model.InsertBatch(context.Background(), data, &InsertBatchOption{
				IgnoreErrors: true,
			})
			assert.NoError(t, err)
		})

		t.Run("success_update_on_duplicate", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customTokenTransactionModel{
				defaultTokenTransactionModel: mustNewTokenTransactionModel(conn),
			}
			data := []*TokenTransaction{
				{
					TransactionHash:      "hash1",
					BlockHash:            "block1",
					ChainType:            "chain1",
					Address:              "addr1",
					Height:               100,
					TransactionTime:      1740063284739,
					FromAddress:          "from1",
					ToAddress:            "to1",
					IsFromContract:       0,
					IsToContract:         1,
					TokenContractAddress: "token1",
					Amount:               "100",
					TokenSymbol:          "ETH",
					TokenId:              "1",
				},
			}

			mock.ExpectExec("insert into `token_transaction`.*ON DUPLICATE KEY UPDATE").
				WithArgs(
					"hash1", "block1", "chain1", "addr1", int64(100),
					int64(1740063284739), "from1", "to1", int16(0), int16(1),
					"token1", "100", "ETH", "1",
				).
				WillReturnResult(sqlmock.NewResult(1, 1))

			_, err := model.InsertBatch(context.Background(), data, &InsertBatchOption{
				OnDuplicateKeyUpdate: true,
			})
			assert.NoError(t, err)
		})

		t.Run("insert_error", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customTokenTransactionModel{
				defaultTokenTransactionModel: mustNewTokenTransactionModel(conn),
			}
			data := []*TokenTransaction{
				{
					TransactionHash: "hash1",
					ChainType:       "chain1",
					Address:         "addr1",
				},
			}

			mock.ExpectExec("insert into `token_transaction`").
				WillReturnError(fmt.Errorf("insert error"))

			_, err := model.InsertBatch(context.Background(), data, nil)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "insert error")
		})
	})
}

func TestTokenTransaction_FindLastHeight(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		t.Run("success", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customTokenTransactionModel{
				defaultTokenTransactionModel: mustNewTokenTransactionModel(conn),
			}

			mock.ExpectQuery("SELECT MAX\\(height\\) as max_height FROM `token_transaction` WHERE chain_type = \\? AND address = \\?").
				WithArgs("chain1", "addr1").
				WillReturnRows(sqlmock.NewRows([]string{"max_height"}).
					AddRow(100))

			height, err := model.FindLastHeight(context.Background(), "chain1", "addr1")
			assert.NoError(t, err)
			assert.Equal(t, int64(100), height)
		})

		t.Run("empty_chain", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customTokenTransactionModel{
				defaultTokenTransactionModel: mustNewTokenTransactionModel(conn),
			}

			height, err := model.FindLastHeight(context.Background(), "", "addr1")
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "chain and address cannot be empty")
			assert.Equal(t, int64(0), height)
		})

		t.Run("empty_address", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customTokenTransactionModel{
				defaultTokenTransactionModel: mustNewTokenTransactionModel(conn),
			}

			height, err := model.FindLastHeight(context.Background(), "chain1", "")
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "chain and address cannot be empty")
			assert.Equal(t, int64(0), height)
		})

		t.Run("both_empty", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customTokenTransactionModel{
				defaultTokenTransactionModel: mustNewTokenTransactionModel(conn),
			}

			height, err := model.FindLastHeight(context.Background(), "", "")
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "chain and address cannot be empty")
			assert.Equal(t, int64(0), height)
		})

		t.Run("no_data", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customTokenTransactionModel{
				defaultTokenTransactionModel: mustNewTokenTransactionModel(conn),
			}

			mock.ExpectQuery("SELECT MAX\\(height\\) as max_height FROM `token_transaction` WHERE chain_type = \\? AND address = \\?").
				WithArgs("chain1", "addr1").
				WillReturnRows(sqlmock.NewRows([]string{"max_height"}).
					AddRow(nil))

			height, err := model.FindLastHeight(context.Background(), "chain1", "addr1")
			assert.NoError(t, err)
			assert.Equal(t, int64(0), height)
		})

		t.Run("query_error", func(t *testing.T) {
			conn := sqlx.NewSqlConnFromDB(db)
			model := &customTokenTransactionModel{
				defaultTokenTransactionModel: mustNewTokenTransactionModel(conn),
			}

			mock.ExpectQuery("SELECT MAX\\(height\\) as max_height FROM `token_transaction` WHERE chain_type = \\? AND address = \\?").
				WithArgs("chain1", "addr1").
				WillReturnError(errors.New("query error"))

			_, err := model.FindLastHeight(context.Background(), "chain1", "addr1")
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "failed to query last height: query error")
		})
	})
}
