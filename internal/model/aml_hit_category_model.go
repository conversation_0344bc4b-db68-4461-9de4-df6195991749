package model

import (
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"context"
	"fmt"
	"strings"
)

var _ AmlHitCategoryModel = (*customAmlHitCategoryModel)(nil)

type (
	// AmlHitCategoryModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAmlHitCategoryModel.
	AmlHitCategoryModel interface {
		amlHitCategoryModel
		QueryByIdOrCategory(ctx context.Context, id int64, category string, limit, page int32) ([]*AmlHitCategory, error)
	}

	customAmlHitCategoryModel struct {
		*defaultAmlHitCategoryModel
	}
)

// MustNewAmlHitCategoryModel returns a model for the database table.
func MustNewAmlHitCategoryModel(conf sqlx.Config, opts ...sqlx.SqlOption) AmlHitCategoryModel {
	return &customAmlHitCategoryModel{
		defaultAmlHitCategoryModel: mustNewAmlHitCategoryModel(conf.MustNewMysql(opts...)),
	}
}

func (m *customAmlHitCategoryModel) QueryByIdOrCategory(ctx context.Context, id int64, category string, limit, page int32) ([]*AmlHitCategory, error) {

	var (
		resp         []*AmlHitCategory
		whereClauses []string
		args         []any
	)

	if id != 0 {
		whereClauses = append(whereClauses, "id = ?")
		args = append(args, id)
	}
	if len(category) != 0 {
		whereClauses = append(whereClauses, "category = ?")
		args = append(args, category)
	}

	// 构建查询语句
	query := fmt.Sprintf("SELECT * FROM %s", m.table)
	if len(whereClauses) != 0 {
		query += " WHERE " + strings.Join(whereClauses, " AND ")
	}
	query += fmt.Sprintf(" ORDER BY id DESC")
	if limit > 0 {
		query += fmt.Sprintf(" LIMIT ? OFFSET ?")
		args = append(args, limit, (page-1)*limit)
	}

	// 执行查询
	if err := m.conn.QueryRowsCtx(ctx, &resp, query, args...); err != nil {
		return nil, err
	}

	return resp, nil
}
