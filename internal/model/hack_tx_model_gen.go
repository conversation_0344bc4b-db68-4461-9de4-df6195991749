// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	hackTxFieldNames          = builder.RawFieldNames(&HackTx{})
	hackTxRows                = strings.Join(hackTxFieldNames, ",")
	hackTxRowsExpectAutoSet   = strings.Join(stringx.Remove(hackTxFieldNames, "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	hackTxRowsWithPlaceHolder = strings.Join(stringx.Remove(hackTxFieldNames, "`tx_id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	hackTxModel interface {
		Insert(ctx context.Context, data *HackTx) (sql.Result, error)
		FindOne(ctx context.Context, txId string) (*HackTx, error)
		Update(ctx context.Context, newData *HackTx) error
	}

	defaultHackTxModel struct {
		conn  sqlx.SqlConn
		table string
	}

	HackTx struct {
		TxId           string    `db:"tx_id"`            // 交易ID, 主键
		TxTime         time.Time `db:"tx_time"`          // 交易时间
		FromAddr       string    `db:"from_addr"`        // 发送地址
		ToAddr         string    `db:"to_addr"`          // 接收地址
		Amount         string    `db:"amount"`           // 交易金额
		TxSymbol       string    `db:"tx_symbol"`        // 交易币种
		State          string    `db:"state"`            // 交易状态
		TxType         string    `db:"tx_type"`          // 交易类型
		ToChainType    string    `db:"to_chain_type"`    // 目标链类型
		AssetChainCoin string    `db:"asset_chain_coin"` // 资产链币种
		ToChainAddr    string    `db:"to_chain_addr"`    // 目标链地址
		ToChainTxHash  string    `db:"to_chain_tx_hash"` // 目标链交易哈希
		ChainAmount    string    `db:"chain_amount"`     // 链上金额
		CreatedAt      time.Time `db:"created_at"`       // 创建时间
		UpdatedAt      time.Time `db:"updated_at"`       // 更新时间
	}
)

func mustNewHackTxModel(conn sqlx.SqlConn) *defaultHackTxModel {
	return &defaultHackTxModel{
		conn:  conn,
		table: "`hack_tx`",
	}
}

func (m *defaultHackTxModel) FindOne(ctx context.Context, txId string) (*HackTx, error) {
	query := fmt.Sprintf("select %s from %s where `tx_id` = ? limit 1", hackTxRows, m.table)
	var resp HackTx
	err := m.conn.QueryRowCtx(ctx, &resp, query, txId)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultHackTxModel) Insert(ctx context.Context, data *HackTx) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, hackTxRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.TxId, data.TxTime, data.FromAddr, data.ToAddr, data.Amount, data.TxSymbol, data.State, data.TxType, data.ToChainType, data.AssetChainCoin, data.ToChainAddr, data.ToChainTxHash, data.ChainAmount)
	return ret, err
}

func (m *defaultHackTxModel) Update(ctx context.Context, data *HackTx) error {
	query := fmt.Sprintf("update %s set %s where `tx_id` = ?", m.table, hackTxRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.TxTime, data.FromAddr, data.ToAddr, data.Amount, data.TxSymbol, data.State, data.TxType, data.ToChainType, data.AssetChainCoin, data.ToChainAddr, data.ToChainTxHash, data.ChainAmount, data.TxId)
	return err
}

func (m *defaultHackTxModel) tableName() string {
	return m.table
}
