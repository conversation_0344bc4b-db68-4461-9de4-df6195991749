package model

import (
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"context"
	"database/sql"
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestPreKyaHistory_Insert(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		model := &customPreKyaHistoryModel{
			defaultPreKyaHistoryModel: mustNewPreKyaHistoryModel(conn),
		}

		t.Run("success", func(t *testing.T) {
			// Expecting an insert into pre_kya_history with specific arguments
			mock.ExpectExec("insert into `pre_kya_history`").
				WithArgs(
					"request1", 1, "chain1", "address1", 1, 1, 2, "result1",
				).
				WillReturnResult(sqlmock.NewResult(1, 1))

			data := &PreKyaHistory{
				RequestId: "request1",
				MemberId:  1,
				Chain:     "chain1",
				Address:   "address1",
				Status:    1,
				VipType:   1,
				VipLevel:  2,
				Result:    "result1",
			}

			result, err := model.Insert(context.Background(), data)
			assert.NoError(t, err)
			lastId, err := result.LastInsertId()
			assert.NoError(t, err)
			assert.Equal(t, int64(1), lastId)
		})

		t.Run("insert_error", func(t *testing.T) {
			mock.ExpectExec("insert into `pre_kya_history`").
				WillReturnError(fmt.Errorf("insert error"))

			data := &PreKyaHistory{
				RequestId: "request1",
				MemberId:  1,
				Chain:     "chain1",
				Address:   "address1",
				Status:    1,
				VipType:   1,
				VipLevel:  2,
				Result:    "result1",
			}

			result, err := model.Insert(context.Background(), data)
			assert.Error(t, err)
			assert.Nil(t, result)
			assert.Contains(t, err.Error(), "insert error")
		})
	})
}

func TestPreKyaHistory_FindOne(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		model := &customPreKyaHistoryModel{
			defaultPreKyaHistoryModel: mustNewPreKyaHistoryModel(conn),
		}

		t.Run("success", func(t *testing.T) {
			// 修改正则表达式，确保可以匹配实际执行的查询
			mock.ExpectQuery("(?i)select (.+)").
				WithArgs(1).
				WillReturnRows(sqlmock.NewRows([]string{"id", "request_id", "member_id", "chain", "address", "status", "vip_type", "vip_level", "result", "created_at", "updated_at"}).
					AddRow(1, "request1", 1, "chain1", "address1", 1, 1, 2, "result1", time.Now(), time.Now()))

			resp, err := model.FindOne(context.Background(), 1)
			assert.NoError(t, err)
			assert.NotNil(t, resp) // 确保 resp 不为 nil
			assert.Equal(t, uint64(1), resp.Id)
			assert.Equal(t, "request1", resp.RequestId)
		})

		t.Run("not_found", func(t *testing.T) {
			mock.ExpectQuery("(?i)select (.+)").
				WithArgs(1).
				WillReturnRows(sqlmock.NewRows([]string{"id", "request_id", "member_id", "chain", "address", "status", "vip_type", "vip_level", "result", "created_at", "updated_at"}))

			resp, err := model.FindOne(context.Background(), 1)
			assert.Error(t, err)
			assert.Nil(t, resp) // 确保没有返回结果
			assert.ErrorIs(t, err, ErrNotFound)
		})

		t.Run("query_error", func(t *testing.T) {
			mock.ExpectQuery("(?i)select (.+)").
				WithArgs(1).
				WillReturnError(fmt.Errorf("query error"))

			resp, err := model.FindOne(context.Background(), 1)
			assert.Error(t, err)
			assert.Nil(t, resp) // 确保没有返回结果
			assert.Contains(t, err.Error(), "query error")
		})
	})
}

func TestPreKyaHistory_Update(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		model := &customPreKyaHistoryModel{
			defaultPreKyaHistoryModel: mustNewPreKyaHistoryModel(conn),
		}

		// 创建待更新的数据
		data := &PreKyaHistory{
			Id:        1,
			RequestId: "request1",
			MemberId:  1,
			Chain:     "chain1",
			Address:   "address1",
			Status:    1,
			VipType:   1,
			VipLevel:  2,
			Result:    "result1",
		}

		t.Run("success", func(t *testing.T) {
			// 模拟成功的 UPDATE 操作
			mock.ExpectExec("(?i)update (.+)").
				WithArgs(
					data.RequestId, data.MemberId, data.Chain, data.Address,
					data.Status, data.VipType, data.VipLevel, data.Result, data.Id,
				).
				WillReturnResult(sqlmock.NewResult(1, 1))

			err := model.Update(context.Background(), data)
			assert.NoError(t, err)
		})

		t.Run("update_error", func(t *testing.T) {
			// 模拟 UPDATE 操作失败
			mock.ExpectExec("(?i)update (.+)").
				WithArgs(
					data.RequestId, data.MemberId, data.Chain, data.Address,
					data.Status, data.VipType, data.VipLevel, data.Result, data.Id,
				).
				WillReturnError(fmt.Errorf("update error"))

			err := model.Update(context.Background(), data)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "update error")
		})
	})
}

func TestPreKyaHistory_QueryKyaHistoryByTime(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		model := &customPreKyaHistoryModel{
			defaultPreKyaHistoryModel: mustNewPreKyaHistoryModel(conn),
		}

		// 测试数据
		memberId := uint64(1)
		startTime := time.Now().Add(-time.Hour * 24 * 30) // 30天前
		endTime := time.Now()
		address := "address1"
		result := "result1"
		limit := int32(10)
		offset := int32(0)

		t.Run("success", func(t *testing.T) {
			// 模拟查询操作
			mock.ExpectQuery("(?i)select (.+)").
				WithArgs(memberId, startTime, endTime, PreKyaStatusSuccess, address, result, limit, offset).
				WillReturnRows(sqlmock.NewRows([]string{"id", "request_id", "member_id", "chain", "address", "status", "vip_type", "vip_level", "result", "created_at", "updated_at"}).
					AddRow(1, "request1", 1, "chain1", "address1", 1, 1, 2, "result1", time.Now(), time.Now()))

			resp, err := model.QueryKyaHistoryByTime(context.Background(), memberId, startTime, endTime, address, result, limit, offset)
			assert.NoError(t, err)
			assert.NotNil(t, resp)
			assert.Len(t, resp, 1)
			assert.Equal(t, uint64(1), resp[0].Id)
		})

		t.Run("no_records", func(t *testing.T) {
			// 模拟没有记录的情况
			mock.ExpectQuery("(?i)select (.+)").
				WithArgs(memberId, startTime, endTime, PreKyaStatusSuccess, address, result, limit, offset).
				WillReturnRows(sqlmock.NewRows([]string{"id", "request_id", "member_id", "chain", "address", "status", "vip_type", "vip_level", "result", "created_at", "updated_at"}))

			resp, err := model.QueryKyaHistoryByTime(context.Background(), memberId, startTime, endTime, address, result, limit, offset)
			assert.NoError(t, err)
			assert.Len(t, resp, 0)
		})

		t.Run("query_error", func(t *testing.T) {
			// 模拟查询时的错误
			mock.ExpectQuery("(?i)select (.+)").
				WithArgs(memberId, startTime, endTime, PreKyaStatusSuccess, address, result, limit, offset).
				WillReturnError(fmt.Errorf("query error"))

			resp, err := model.QueryKyaHistoryByTime(context.Background(), memberId, startTime, endTime, address, result, limit, offset)
			assert.Error(t, err)
			assert.Nil(t, resp)
			assert.Contains(t, err.Error(), "query error")
		})
	})
}

func TestPreKyaHistory_CountKyaHistoryResultIn(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		model := &customPreKyaHistoryModel{
			defaultPreKyaHistoryModel: mustNewPreKyaHistoryModel(conn),
		}

		// 测试数据
		memberId := uint64(1)
		startTime := time.Now().Add(-time.Hour * 24 * 30) // 30天前
		endTime := time.Now()
		address := "address1"
		results := []string{"result1", "result2"}

		t.Run("success", func(t *testing.T) {
			// 模拟 COUNT 查询
			mock.ExpectQuery("(?i)select (.+)").
				WithArgs(memberId, startTime, endTime, PreKyaStatusSuccess, address, results[0], results[1]).
				WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(5))

			count, err := model.CountKyaHistoryResultIn(context.Background(), memberId, startTime, endTime, address, results)
			assert.NoError(t, err)
			assert.Equal(t, int32(5), count)
		})

		t.Run("no_records", func(t *testing.T) {
			// 模拟没有记录的情况
			mock.ExpectQuery("(?i)select (.+)").
				WithArgs(memberId, startTime, endTime, PreKyaStatusSuccess, address, results[0], results[1]).
				WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

			count, err := model.CountKyaHistoryResultIn(context.Background(), memberId, startTime, endTime, address, results)
			assert.NoError(t, err)
			assert.Equal(t, int32(0), count)
		})

		t.Run("query_error", func(t *testing.T) {
			// 模拟查询时的错误
			mock.ExpectQuery("(?i)select (.+)").
				WithArgs(memberId, startTime, endTime, PreKyaStatusSuccess, address, results[0], results[1]).
				WillReturnError(fmt.Errorf("query error"))

			count, err := model.CountKyaHistoryResultIn(context.Background(), memberId, startTime, endTime, address, results)
			assert.Error(t, err)
			assert.Equal(t, int32(0), count)
			assert.Contains(t, err.Error(), "query error")
		})
	})
}
