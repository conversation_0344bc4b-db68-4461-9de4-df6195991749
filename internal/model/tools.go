package model

import (
	"database/sql"
)

func NullStringToString(value sql.NullString) string {
	if !value.Valid {
		return ""
	}
	return value.String
}

func StringToNullString(value string) sql.NullString {
	sqlStr := sql.NullString{}
	if value == "" {
		sqlStr.Valid = false
	} else {
		sqlStr.Valid = true
	}

	sqlStr.String = value
	return sqlStr
}

type mockedResult struct {
	lastInsertId int64
	rowsAffected int64
}

func NewMockedResult(lastInsertId, rowsAffected int64) sql.Result {
	return &mockedResult{
		lastInsertId: lastInsertId,
		rowsAffected: rowsAffected,
	}
}

func (m mockedResult) LastInsertId() (int64, error) {
	return m.lastInsertId, nil
}

func (m mockedResult) RowsAffected() (int64, error) {
	return m.rowsAffected, nil
}
