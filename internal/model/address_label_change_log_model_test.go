package model

import (
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"context"
	"database/sql"
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

// Test for Insert method
func TestAddressLabelChangeLog_Insert(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		model := mustNewAddressLabelChangeLogModel(conn)

		t.Run("success", func(t *testing.T) {
			// Prepare test data
			data := &AddressLabelChangeLog{
				OperationUser: "user123",
				OperationType: "INSERT",
				AffectedRows:  1,
				Status:        "SUCCESS",
				Msg:           "Operation successful",
			}

			// Expecting an insert into address_label_change_log with specific arguments
			mock.ExpectExec("insert into `address_label_change_log`").
				WithArgs(data.OperationUser, data.OperationType, data.AffectedRows, data.Status, data.Msg).
				WillReturnResult(sqlmock.NewResult(1, 1))

			result, err := model.Insert(context.Background(), data)
			assert.NoError(t, err)
			lastId, err := result.LastInsertId()
			assert.NoError(t, err)
			assert.Equal(t, int64(1), lastId)
		})

		t.Run("insert_error", func(t *testing.T) {
			// Simulating insert error
			mock.ExpectExec("insert into `address_label_change_log`").
				WillReturnError(fmt.Errorf("insert error"))

			data := &AddressLabelChangeLog{
				OperationUser: "user123",
				OperationType: "INSERT",
				AffectedRows:  1,
				Status:        "SUCCESS",
				Msg:           "Operation successful",
			}

			result, err := model.Insert(context.Background(), data)
			assert.Error(t, err)
			assert.Nil(t, result)
			assert.Contains(t, err.Error(), "insert error")
		})
	})
}

// Test for FindOne method
func TestAddressLabelChangeLog_FindOne(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		model := mustNewAddressLabelChangeLogModel(conn)

		t.Run("success", func(t *testing.T) {
			// Simulating data returned for FindOne
			mock.ExpectQuery("(?i)select (.+)").
				WithArgs(1).
				WillReturnRows(sqlmock.NewRows([]string{"id", "operation_user", "operation_type", "affected_rows", "status", "msg", "created_at", "updated_at"}).
					AddRow(1, "user123", "INSERT", 1, "SUCCESS", "Operation successful", time.Now(), time.Now()))

			resp, err := model.FindOne(context.Background(), 1)
			assert.NoError(t, err)
			assert.NotNil(t, resp)
			assert.Equal(t, uint64(1), resp.Id)
			assert.Equal(t, "user123", resp.OperationUser)
		})

		t.Run("not_found", func(t *testing.T) {
			mock.ExpectQuery("(?i)select (.+)").
				WithArgs(1).
				WillReturnRows(sqlmock.NewRows([]string{"id", "operation_user", "operation_type", "affected_rows", "status", "msg", "created_at", "updated_at"}))

			resp, err := model.FindOne(context.Background(), 1)
			assert.Error(t, err)
			assert.Nil(t, resp)
			assert.ErrorIs(t, err, ErrNotFound)
		})

		t.Run("query_error", func(t *testing.T) {
			mock.ExpectQuery("(?i)select (.+)").
				WithArgs(1).
				WillReturnError(fmt.Errorf("query error"))

			resp, err := model.FindOne(context.Background(), 1)
			assert.Error(t, err)
			assert.Nil(t, resp)
			assert.Contains(t, err.Error(), "query error")
		})
	})
}

// Test for Update method
func TestAddressLabelChangeLog_Update(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		model := mustNewAddressLabelChangeLogModel(conn)

		// Prepare test data
		data := &AddressLabelChangeLog{
			Id:            1,
			OperationUser: "user123",
			OperationType: "UPDATE",
			AffectedRows:  1,
			Status:        "SUCCESS",
			Msg:           "Update successful",
		}

		t.Run("success", func(t *testing.T) {
			// Simulating a successful update
			mock.ExpectExec("(?i)update `address_label_change_log`").
				WithArgs(data.OperationUser, data.OperationType, data.AffectedRows, data.Status, data.Msg, data.Id).
				WillReturnResult(sqlmock.NewResult(1, 1))

			err := model.Update(context.Background(), data)
			assert.NoError(t, err)
		})

		t.Run("update_error", func(t *testing.T) {
			// Simulating update error
			mock.ExpectExec("(?i)update `address_label_change_log`").
				WithArgs(data.OperationUser, data.OperationType, data.AffectedRows, data.Status, data.Msg, data.Id).
				WillReturnError(fmt.Errorf("update error"))

			err := model.Update(context.Background(), data)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "update error")
		})
	})
}

// Test for UpdateChangeLogStatus method
func TestAddressLabelChangeLog_UpdateChangeLogStatus(t *testing.T) {
	RunTest(t, func(db *sql.DB, mock sqlmock.Sqlmock) {
		conn := sqlx.NewSqlConnFromDB(db)
		model := mustNewAddressLabelChangeLogModel(conn)

		t.Run("success", func(t *testing.T) {
			// Test data
			id := uint64(1)
			status := StatusSuccess

			// Simulating a successful update of status
			mock.ExpectExec("(?i)update `address_label_change_log`").
				WithArgs(status, id).
				WillReturnResult(sqlmock.NewResult(1, 1))

			// Calling the method
			err := model.UpdateChangeLogStatus(context.Background(), id, status)
			assert.NoError(t, err)
		})

		t.Run("update_error", func(t *testing.T) {
			// Simulating an update error
			id := uint64(1)
			status := StatusFailed

			mock.ExpectExec("(?i)update `address_label_change_log`").
				WithArgs(status, id).
				WillReturnError(fmt.Errorf("update error"))

			// Calling the method and expecting an error
			err := model.UpdateChangeLogStatus(context.Background(), id, status)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "update error")
		})

		t.Run("no_rows_affected", func(t *testing.T) {
			// Simulating no rows affected, i.e., no change was made
			id := uint64(1)
			status := StatusFailed

			mock.ExpectExec("(?i)update `address_label_change_log`").
				WithArgs(status, id).
				WillReturnResult(sqlmock.NewResult(0, 0)) // No rows affected

			// Calling the method, expecting no error but also ensuring no change occurred
			err := model.UpdateChangeLogStatus(context.Background(), id, status)
			assert.NoError(t, err)
		})
	})
}
