// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	rnLogFieldNames          = builder.RawFieldNames(&RnLog{})
	rnLogRows                = strings.Join(rnLogFieldNames, ",")
	rnLogRowsExpectAutoSet   = strings.Join(stringx.Remove(rnLogFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	rnLogRowsWithPlaceHolder = strings.Join(stringx.Remove(rnLogFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	rnLogModel interface {
		Insert(ctx context.Context, data *RnLog) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*RnLog, error)
		Update(ctx context.Context, newData *RnLog) error
	}

	defaultRnLogModel struct {
		conn  sqlx.SqlConn
		table string
	}

	RnLog struct {
		Id        uint64         `db:"id"`         // 主键id
		RequestId string         `db:"request_id"` // 唯一请求id
		Method    string         `db:"method"`     // 请求方法
		Req       sql.NullString `db:"req"`        // 请求信息
		Resp      sql.NullString `db:"resp"`       // 返回信息
		Status    int32          `db:"status"`     // 状态 200 是成功的，其他为失败
		Duration  int64          `db:"duration"`   // duration ms
		CreatedAt time.Time      `db:"created_at"` // 创建时间
	}
)

func mustNewRnLogModel(conn sqlx.SqlConn) *defaultRnLogModel {
	return &defaultRnLogModel{
		conn:  conn,
		table: "`rn_log`",
	}
}

func (m *defaultRnLogModel) FindOne(ctx context.Context, id uint64) (*RnLog, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", rnLogRows, m.table)
	var resp RnLog
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultRnLogModel) Insert(ctx context.Context, data *RnLog) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?)", m.table, rnLogRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.RequestId, data.Method, data.Req, data.Resp, data.Status, data.Duration)
	return ret, err
}

func (m *defaultRnLogModel) Update(ctx context.Context, data *RnLog) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, rnLogRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.RequestId, data.Method, data.Req, data.Resp, data.Status, data.Duration, data.Id)
	return err
}

func (m *defaultRnLogModel) tableName() string {
	return m.table
}
