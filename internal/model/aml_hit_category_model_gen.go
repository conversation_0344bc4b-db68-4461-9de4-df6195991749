// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"
)

var (
	amlHitCategoryFieldNames          = builder.RawFieldNames(&AmlHitCategory{})
	amlHitCategoryRows                = strings.Join(amlHitCategoryFieldNames, ",")
	amlHitCategoryRowsExpectAutoSet   = strings.Join(stringx.Remove(amlHitCategoryFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	amlHitCategoryRowsWithPlaceHolder = strings.Join(stringx.Remove(amlHitCategoryFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	amlHitCategoryModel interface {
		Insert(ctx context.Context, data *AmlHitCategory) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*AmlHitCategory, error)
		Update(ctx context.Context, newData *AmlHitCategory) error
	}

	defaultAmlHitCategoryModel struct {
		conn  sqlx.SqlConn
		table string
	}

	AmlHitCategory struct {
		Id             uint64    `db:"id"`               // id
		Provider       string    `db:"provider"`         // 供应商
		Category       string    `db:"category"`         // 命中分类
		Entity         string    `db:"entity"`           // 命中实体类型
		ActionType     int32     `db:"action_type"`      // 操作类型 1、deposit  2、withdraw
		ExposureType   string    `db:"exposure_type"`    // 涉及关系 direct indirect cp(counter_party)
		RiskLevel      string    `db:"risk_level"`       // 风险等级 prohibited  high  medium
		RiskLevelScore int32     `db:"risk_level_score"` // 等级分数
		Status         int32     `db:"status"`           // 记录状态 1 启用
		CreatedAt      time.Time `db:"created_at"`       // 创建时间
		UpdatedAt      time.Time `db:"updated_at"`       // 更新时间
		CategoryId     string    `db:"category_id"`      // category_id
		Editor         string    `db:"editor"`           // 最后编辑人
		Remark         string    `db:"remark"`           // 备注
	}
)

func mustNewAmlHitCategoryModel(conn sqlx.SqlConn) *defaultAmlHitCategoryModel {
	return &defaultAmlHitCategoryModel{
		conn:  conn,
		table: "`aml_hit_category`",
	}
}

func (m *defaultAmlHitCategoryModel) FindOne(ctx context.Context, id uint64) (*AmlHitCategory, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", amlHitCategoryRows, m.table)
	var resp AmlHitCategory
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAmlHitCategoryModel) Insert(ctx context.Context, data *AmlHitCategory) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, amlHitCategoryRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Provider, data.Category, data.Entity, data.ActionType, data.ExposureType, data.RiskLevel, data.RiskLevelScore, data.Status, data.CategoryId, data.Editor, data.Remark)
	return ret, err
}

func (m *defaultAmlHitCategoryModel) Update(ctx context.Context, data *AmlHitCategory) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, amlHitCategoryRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Provider, data.Category, data.Entity, data.ActionType, data.ExposureType, data.RiskLevel, data.RiskLevelScore, data.Status, data.CategoryId, data.Editor, data.Remark, data.Id)
	return err
}

func (m *defaultAmlHitCategoryModel) tableName() string {
	return m.table
}
