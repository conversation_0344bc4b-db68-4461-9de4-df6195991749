package model

import "code.bydev.io/frameworks/byone/core/stores/sqlx"

var _ HackTxModel = (*customHackTxModel)(nil)

type (
	// HackTxModel is an interface to be customized, add more methods here,
	// and implement the added methods in customHackTxModel.
	HackTxModel interface {
		hackTxModel
	}

	customHackTxModel struct {
		*defaultHackTxModel
	}
)

// MustNewHackTxModel returns a model for the database table.
func MustNewHackTxModel(conf sqlx.Config, opts ...sqlx.SqlOption) HackTxModel {
	return &customHackTxModel{
		defaultHackTxModel: mustNewHackTxModel(conf.MustNewMysql(opts...)),
	}
}
