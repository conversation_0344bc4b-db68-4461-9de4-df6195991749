// Code generated by byctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/core/stores/builder"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/core/stringx"

	"github.com/shopspring/decimal"
)

var (
	amlCaseFieldNames          = builder.RawFieldNames(&AmlCase{})
	amlCaseRows                = strings.Join(amlCaseFieldNames, ",")
	amlCaseRowsExpectAutoSet   = strings.Join(stringx.Remove(amlCaseFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), ",")
	amlCaseRowsWithPlaceHolder = strings.Join(stringx.Remove(amlCaseFieldNames, "`id`", "`create_time`", "`created_at`", "`gmt_create`", "`gmt_modified`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	amlCaseModel interface {
		Insert(ctx context.Context, data *AmlCase) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*AmlCase, error)
		FindOneByRequestId(ctx context.Context, requestId string) (*AmlCase, error)
		Update(ctx context.Context, newData *AmlCase) error
	}

	defaultAmlCaseModel struct {
		conn  sqlx.SqlConn
		table string
	}

	AmlCase struct {
		Id              uint64          `db:"id" json:"id"`                             // 主键
		RequestId       string          `db:"request_id" json:"request_id"`             // 请求唯一id
		Token           string          `db:"token" json:"token"`                       // 幂等标识
		MemberId        uint64          `db:"member_id" json:"member_id"`               // 原始用户id
		Chain           string          `db:"chain" json:"chain"`                       // 链
		Coin            string          `db:"coin" json:"coin"`                         // 币
		FromAddress     string          `db:"from_address" json:"from_address"`         // 来源地址
		ToAddress       string          `db:"to_address" json:"to_address"`             // 目标地址
		TxHash          string          `db:"tx_hash" json:"tx_hash"`                   // 交易hash
		Amount          decimal.Decimal `db:"amount" json:"amount"`                     // 数量
		ActionType      int32           `db:"action_type" json:"action_type"`           // deposit/withdrawal
		Decision        string          `db:"decision" json:"decision"`                 // pass manual edd reject pending
		SuggestedAction string          `db:"suggested_action" json:"suggested_action"` // 建议操作
		Status          int32           `db:"status" json:"status"`                     // 1 创建 2 等待 7 完成关闭
		Remark          string          `db:"remark" json:"remark"`                     // 备注
		CreatedAt       time.Time       `db:"created_at" json:"created_at"`             // 创建时间
		UpdatedAt       time.Time       `db:"updated_at" json:"updated_at"`             // 更新时间
		AmountUsd       decimal.Decimal `db:"amount_usd" json:"amount_usd"`             // 换算usd金额
		Disposal        string          `db:"disposal" json:"disposal"`                 // 处置
		ExternalStatus  int32           `db:"external_status" json:"external_status"`   // 对外一致性状态 1处理中 2命中 3未命中
		ExternalProcess int32           `db:"external_process" json:"external_process"` // 对外处理状态 1允许上账 2直接可退 3edd 4ac
		TxIndex         int64           `db:"tx_index" json:"tx_index"`                 // 交易index
		BlockHash       string          `db:"block_hash" json:"block_hash"`             // 块hash，只用于查询交易详情
		GroupId         string          `db:"group_id" json:"group_id"`                 // group_id
		UtxoAddresses   string          `db:"utxo_addresses" json:"utxo_addresses"`     // 逗号分割
		Label           string          `db:"label" json:"label"`                       // 命中主标签
	}
)

func mustNewAmlCaseModel(conn sqlx.SqlConn) *defaultAmlCaseModel {
	return &defaultAmlCaseModel{
		conn:  conn,
		table: "`aml_case`",
	}
}

func (m *defaultAmlCaseModel) FindOne(ctx context.Context, id uint64) (*AmlCase, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", amlCaseRows, m.table)
	var resp AmlCase
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAmlCaseModel) FindOneByRequestId(ctx context.Context, requestId string) (*AmlCase, error) {
	var resp AmlCase
	query := fmt.Sprintf("select %s from %s where `request_id` = ? limit 1", amlCaseRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, requestId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAmlCaseModel) Insert(ctx context.Context, data *AmlCase) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, amlCaseRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.RequestId, data.Token, data.MemberId, data.Chain, data.Coin, data.FromAddress, data.ToAddress, data.TxHash, data.Amount, data.ActionType, data.Decision, data.SuggestedAction, data.Status, data.Remark, data.AmountUsd, data.Disposal, data.ExternalStatus, data.ExternalProcess, data.TxIndex, data.BlockHash, data.GroupId, data.UtxoAddresses, data.Label)
	return ret, err
}

func (m *defaultAmlCaseModel) Update(ctx context.Context, newData *AmlCase) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, amlCaseRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.RequestId, newData.Token, newData.MemberId, newData.Chain, newData.Coin, newData.FromAddress, newData.ToAddress, newData.TxHash, newData.Amount, newData.ActionType, newData.Decision, newData.SuggestedAction, newData.Status, newData.Remark, newData.AmountUsd, newData.Disposal, newData.ExternalStatus, newData.ExternalProcess, newData.TxIndex, newData.BlockHash, newData.GroupId, newData.UtxoAddresses, newData.Label, newData.Id)
	return err
}

func (m *defaultAmlCaseModel) tableName() string {
	return m.table
}
