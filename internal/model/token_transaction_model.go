package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"code.bydev.io/frameworks/byone/core/stores/sqlx"
)

var _ TokenTransactionModel = (*customTokenTransactionModel)(nil)

type (
	// TokenTransactionModel is an interface to be customized, add more methods here,
	// and implement the added methods in customTokenTransactionModel.
	TokenTransactionModel interface {
		tokenTransactionModel
		InsertBatch(ctx context.Context, data []*TokenTransaction, opt *InsertBatchOption) (sql.Result, error)
		FindLastHeight(ctx context.Context, chain, address string) (int64, error)
	}

	customTokenTransactionModel struct {
		*defaultTokenTransactionModel
	}
)

// MustNewTokenTransactionModel returns a model for the database table.
func MustNewTokenTransactionModel(conf sqlx.Config, opts ...sqlx.SqlOption) TokenTransactionModel {
	return &customTokenTransactionModel{
		defaultTokenTransactionModel: mustNewTokenTransactionModel(conf.MustNewMysql(opts...)),
	}
}

func (m *customTokenTransactionModel) InsertBatch(ctx context.Context, data []*TokenTransaction, opt *InsertBatchOption) (sql.Result, error) {
	if len(data) == 0 {
		return nil, nil
	}

	// 限制批量插入的数量
	if len(data) > 1000 {
		return nil, fmt.Errorf("batch size %d exceeds maximum allowed size of 1000", len(data))
	}

	if opt == nil {
		opt = &InsertBatchOption{}
	}

	var query string
	if opt.IgnoreErrors {
		query = fmt.Sprintf("insert ignore into %s (%s) values ", m.table, tokenTransactionRowsExpectAutoSet)
	} else {
		query = fmt.Sprintf("insert into %s (%s) values ", m.table, tokenTransactionRowsExpectAutoSet)
	}

	var values []interface{}
	valueStrings := make([]string, 0, len(data))
	for _, tx := range data {
		valueStrings = append(valueStrings, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
		values = append(values,
			tx.TransactionHash, tx.BlockHash, tx.ChainType, tx.Address, tx.Height,
			tx.TransactionTime, tx.FromAddress, tx.ToAddress, tx.IsFromContract,
			tx.IsToContract, tx.TokenContractAddress, tx.Amount, tx.TokenSymbol, tx.TokenId,
		)
	}

	query += strings.Join(valueStrings, ",")

	// 如果请求了ON DUPLICATE KEY UPDATE，则添加ON DUPLICATE KEY UPDATE子句
	if opt.OnDuplicateKeyUpdate {
		updates := make([]string, 0)
		for _, field := range strings.Split(tokenTransactionRowsExpectAutoSet, ",") {
			if field != "`transaction_hash`" { // Skip primary key
				updates = append(updates, fmt.Sprintf("%s=VALUES(%s)", field, field))
			}
		}
		query += " ON DUPLICATE KEY UPDATE " + strings.Join(updates, ",")
	}

	return m.conn.ExecCtx(ctx, query, values...)
}

// FindLastHeight 获取指定链的最新区块高度
func (m *customTokenTransactionModel) FindLastHeight(ctx context.Context, chain, address string) (int64, error) {
	if chain == "" || address == "" {
		return 0, fmt.Errorf("chain and address cannot be empty")
	}

	var result struct {
		MaxHeight sql.NullInt64 `db:"max_height"`
	}

	query := fmt.Sprintf("SELECT MAX(height) as max_height FROM %s WHERE chain_type = ? AND address = ?", m.table)
	err := m.conn.QueryRowCtx(ctx, &result, query, chain, address)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, nil
		}
		return 0, fmt.Errorf("failed to query last height: %w", err)
	}

	if !result.MaxHeight.Valid {
		return 0, nil
	}

	return result.MaxHeight.Int64, nil
}
