package kafka

import (
	"errors"

	"code.bydev.io/frameworks/sarama"
)

type PErr struct {
	e sarama.ProducerErrors
}

func (p *PErr) Error() string {
	var errs []error
	for _, e := range p.e {
		errs = append(errs, e)
	}
	return errors.Join(errs...).Error()
}

func ExtractPErr(err error) error {
	if err == nil {
		return nil
	}
	var ers sarama.ProducerErrors
	if errors.As(err, &ers) {
		return &PErr{e: ers}
	}
	return err
}
