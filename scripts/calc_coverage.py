#!/usr/bin/env python3
import os
import time
import sys
import subprocess
from collections import defaultdict
import fnmatch

# 定义临时目录和文件路径
TMP_DIR = 'tmp'
COVERAGE_FILE = os.path.join(TMP_DIR, 'coverage.out')
MASTER_COVERAGE_FILE = os.path.join(TMP_DIR, 'master_coverage.out')

def get_excluded_dirs():
    """从环境变量获取排除目录列表"""
    excluded_dirs = os.environ.get('EXCLUDED_DIRS', '')
    if not excluded_dirs:
        return []
    return [dir.strip() for dir in excluded_dirs.split(',')]


# 从环境变量获取排除目录配置
EXCLUDED_DIRS = get_excluded_dirs()

def should_update_master_coverage():
    """检查是否需要更新master分支的覆盖率文件"""
    force_master = os.environ.get('FORCE_MASTER', 'false').lower() == 'true'
    if force_master:
        return True
        
    if not os.path.exists(MASTER_COVERAGE_FILE):
        return True
        
    file_mtime = os.path.getmtime(MASTER_COVERAGE_FILE)
    current_time = time.time()
    hours_since_update = (current_time - file_mtime) / 3600
    
    # 如果超过24小时,需要更新
    return hours_since_update >= 24

def should_exclude_file(filename):
    """检查文件是否应该被排除"""
    normalized_path = os.path.normpath(filename)
    if normalized_path.startswith('./'):
        normalized_path = normalized_path[2:]
    
    for pattern in EXCLUDED_DIRS:
        if fnmatch.fnmatch(normalized_path, pattern):
            return True
    return False

def run_command(cmd):
    """执行命令并返回输出"""
    try:
        return subprocess.check_output(cmd, stderr=subprocess.STDOUT).decode()
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {' '.join(cmd)}")
        # print(f"错误输出: {e.output.decode()}")
        sys.exit(1)

def save_current_changes():
    """保存当前未提交的修改"""
    return run_command(['git', 'stash', 'save', 'temporary stash for coverage'])

def restore_changes(stash_output):
    """恢复之前保存的修改"""
    if "No local changes to save" not in stash_output:
        run_command(['git', 'stash', 'pop'])

def get_current_branch():
    """获取当前分支名"""
    return run_command(['git', 'rev-parse', '--abbrev-ref', 'HEAD']).strip()

def switch_branch(branch):
    """切换到指定分支"""
    run_command(['git', 'checkout', branch])

def generate_coverage(output_file):
    """生成覆盖率文件"""
    run_command(['go', 'test', './...', f'-coverprofile={output_file}'])

def normalize_path(path):
    """规范化文件路径以便于比较"""
    normalized = os.path.normpath(path)
    
    if normalized.startswith('./'):
        normalized = normalized[2:]
    
    if normalized.startswith('aml-insight/'):
        normalized = normalized[len('aml-insight/'):]
    
    # 确保文件路径是标准的，去除可能的相对路径部分
    normalized = os.path.normpath(normalized)
    
    return normalized

def parse_coverage_file(coverage_file):
    """解析覆盖率文件,返回每个文件的行覆盖信息"""
    if not os.path.exists(coverage_file):
        print(f"\n❌ 错误: 覆盖率文件不存在: {coverage_file}")
        sys.exit(1)
        
    if os.path.getsize(coverage_file) == 0:
        print(f"\n❌ 错误: 覆盖率文件为空: {coverage_file}")
        sys.exit(1)
        
    coverage_data = defaultdict(dict)
    
    try:
        with open(coverage_file, 'r') as f:
            first_line = f.readline().strip()
            if not first_line.startswith('mode:'):
                print(f"\n❌ 错误: 覆盖率文件格式无效: {coverage_file}")
                sys.exit(1)
                
            for line in f:
                try:
                    file_info, stats = line.strip().split(' ', maxsplit=1)
                    filename, line_range = file_info.split(':')

                    if should_exclude_file(filename):
                        continue
                        
                    start_range, end_range = line_range.split(',')
                    start_line = int(start_range.split('.')[0])
                    end_line = int(end_range.split('.')[0])
                    
                    block_info = stats.strip().split()
                    num_statements = int(block_info[0])
                    execution_count = int(block_info[-1])
                    
                    if num_statements > 0:
                        normalized_filename = normalize_path(filename)
                        for line_num in range(start_line, end_line + 1):
                            if line_num not in coverage_data[normalized_filename]:
                                coverage_data[normalized_filename][line_num] = execution_count > 0
                        
                except Exception as e:
                    print(f"\n❌ 错误: 解析覆盖率文件行时失败: {line.strip()}")
                    print(f"错误信息: {str(e)}")
                    sys.exit(1)
                    
    except Exception as e:
        print(f"\n❌ 错误: 读取覆盖率文件失败: {coverage_file}")
        print(f"错误信息: {str(e)}")
        sys.exit(1)
        
    if not coverage_data:
        print(f"\n❌ 错误: 覆盖率文件中没有有效数据: {coverage_file}")
        sys.exit(1)
        
    return coverage_data

def get_changed_files():
    """获取与 master 相比的变更文件列表"""
    diff_cmd = ['git', 'diff', '--name-only', 'origin/master...']
    
    try:
        output = run_command(diff_cmd).strip()
        if not output:
            print("No changed files detected from git diff")
            return []
            
        changed_files = []
        original_files = output.split('\n')
        
        for f in original_files:
            if f.endswith('.go') and not f.endswith('_test.go') and not should_exclude_file(f):
                normalized_path = normalize_path(f)
                changed_files.append(normalized_path)
        
        # print(f"Original files from git: {original_files[:5]}")
        # print(f"Normalized changed files: {changed_files[:5]}")
        
        return changed_files
    except Exception as e:
        print(f"Error getting changed files: {str(e)}")
        return []

def calculate_total_coverage(coverage_data):
    """计算全量覆盖率统计"""
    total_stats = {'total': 0, 'covered': 0}
    
    for filename, line_coverage in coverage_data.items():
        if should_exclude_file(filename):
            continue
        for line_num, is_covered in line_coverage.items():
            total_stats['total'] += 1
            if is_covered:
                total_stats['covered'] += 1
                
    return total_stats

def calculate_delta_coverage(current_coverage, base_coverage, changed_files):
    """计算增量覆盖率统计"""
    delta_stats = {'total': 0, 'covered': 0}
    file_stats = defaultdict(lambda: {'total': 0, 'covered': 0})

    # print(f"Changed files from git: {changed_files}")
    # normalized_coverage_files = list(current_coverage.keys())
    # print(f"Files in current coverage: {normalized_coverage_files[:5]}")
    
    file_base_to_full = {}
    for path in changed_files:
        base_name = os.path.basename(path)
        if base_name not in file_base_to_full:
            file_base_to_full[base_name] = []
        file_base_to_full[base_name].append(path)
    
    for filename in current_coverage:
        base_name = os.path.basename(filename)
        file_match = filename in changed_files

        if not file_match and base_name in file_base_to_full:
            for changed_file in file_base_to_full[base_name]:
                if os.path.basename(changed_file) == base_name:
                    # print(f"Found base name match: {filename} -> {changed_file}")
                    file_match = True
                    break
                    
        if not file_match:
            continue
            
        # print(f"Processing changed file: {filename}")
        current_file_coverage = current_coverage[filename]
        base_file_coverage = base_coverage.get(filename, {})
        
        for line_num, is_covered in current_file_coverage.items():
            if line_num not in base_file_coverage:
                delta_stats['total'] += 1
                file_stats[filename]['total'] += 1
                if is_covered:
                    delta_stats['covered'] += 1
                    file_stats[filename]['covered'] += 1
    
    return delta_stats, file_stats

def format_coverage(stats):
    """格式化覆盖率数据"""
    if stats['total'] == 0:
        return "0.00%"
    return f"{(stats['covered'] / stats['total'] * 100):.2f}%"

def generate_master_coverage():
    """生成 master 分支的覆盖率文件,返回是否成功"""
    current_branch = get_current_branch()
    stash_output = save_current_changes()
    success = True
    
    try:
        try:
            run_command(['git', 'rev-parse', '--verify', 'origin/master'])
            switch_branch('master')
        except:
            print("\n⚠️ Warning: 无法找到 master 分支，尝试使用 origin/master")
            switch_branch('origin/master')
            
        try:
            run_command(['go', 'test', './...', f'-coverprofile={MASTER_COVERAGE_FILE}'])
        except Exception as e:
            print(f"\n⚠️ Warning: master 分支单元测试失败,将只统计全量覆盖率")
            print(f"错误信息: {str(e)}")
            success = False
        finally:
            switch_branch(current_branch)
            restore_changes(stash_output)
    except Exception as e:
        print(f"\n❌ 错误: 分支切换失败: {str(e)}")
        switch_branch(current_branch)
        restore_changes(stash_output)
        sys.exit(1)
        
    return success

def main():
    try:
        try:
            current_coverage = parse_coverage_file(COVERAGE_FILE)
            total_stats = calculate_total_coverage(current_coverage)
            
            print("\n全量覆盖率统计 (排除指定目录):")
            print(f"总行数: {total_stats['total']}")
            print(f"覆盖行数: {total_stats['covered']}")
            print(f"覆盖率: \033[1m\033[32m{format_coverage(total_stats)}\033[0m")
        except Exception as e:
            print(f"\n❌ 错误: 计算全量覆盖率失败")
            print(f"错误信息: {str(e)}")
            sys.exit(1)

        try:
            # 检查是否需要更新master分支覆盖率
            need_update = should_update_master_coverage()
            has_master_coverage = True
            
            if need_update:
                print("\n更新master分支覆盖率统计...")
                has_master_coverage = generate_master_coverage()
            else:
                print("\n使用缓存的master分支覆盖率统计...")
            
            base_coverage = {}
            if os.path.exists(MASTER_COVERAGE_FILE):
                try:
                    base_coverage = parse_coverage_file(MASTER_COVERAGE_FILE)
                except Exception as e:
                    print(f"\n⚠️ Warning: 解析 master 分支覆盖率文件失败,跳过增量覆盖率统计")
                    print(f"错误信息: {str(e)}")
                    return
                    
                changed_files = get_changed_files()
                
                if not changed_files:
                    print("\n没有检测到代码变更")
                    return
                
                delta_stats, file_stats = calculate_delta_coverage(
                    current_coverage, base_coverage, changed_files
                )
                
                if delta_stats['total'] > 0:
                    print("\n增量覆盖率统计 (所有修改和新增代码):")
                    print(f"新增/修改行数: {delta_stats['total']}")
                    print(f"覆盖行数: {delta_stats['covered']}")
                    coverage_percentage = (delta_stats['covered'] / delta_stats['total'] * 100)
                    color_code = "\033[31m" if coverage_percentage < 50 else "\033[32m"
                    print(f"覆盖率: \033[1m{color_code}{format_coverage(delta_stats)}\033[0m")
                    if coverage_percentage < 50:
                        print(f"\033[31m⚠️ Warning: 增量覆盖率低于50%,建议增加测试用例\033[0m")
                    
                    changed_file_count = len([f for f in file_stats if file_stats[f]['total'] > 0])
                    print(f"\n共有 {changed_file_count} 个文件被修改或新增")
                else:
                    print("\n没有检测到代码变更")
            else:
                print("\n⚠️ 由于 master 分支覆盖率获取失败,跳过增量覆盖率统计")
                
        except Exception as e:
            print(f"\n⚠️ Warning: 增量覆盖率统计失败")
            print(f"错误信息: {str(e)}")
            
    finally:
        # 不再删除master分支的覆盖率文件,以便缓存使用
        pass

if __name__ == '__main__':
    main()