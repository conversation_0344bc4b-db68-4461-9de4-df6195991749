# BackfillAmlCase 限流功能实现

## 功能概述

为 `BackfillAmlCaseLogic` 消费模块实现了基于 Token Bucket 算法的 QPS 限流功能，防止对 RDS 造成过大压力。

## 实现特性

### 1. 核心功能
- **单实例限流**: 针对单个消费实例进行限流
- **Token Bucket算法**: 使用 `golang.org/x/time/rate` 包实现
- **延迟等待**: 触发限流时进行等待而非直接拒绝
- **可配置参数**: QPS、突发容量、等待超时时间均可配置

### 2. 配置参数

```toml
# consumer/etc/consumer.toml
[BackfillAmlCase.RateLimit]
# 是否启用限流
Enabled = true
# 每秒允许的请求数 (QPS)
QPS = 50.0
# Token bucket 容量 (突发处理能力)
Burst = 100
# 等待token的最大超时时间
WaitTimeout = "2s"
```

### 3. 限流触发点

限流检查在 `Consume` 函数的开始处执行，在消息解析和业务处理之前：

```go
func (l *BackfillAmlCaseLogic) Consume(ctx context.Context, message *kafka.Message) error {
    // 限流检查 - 在消息处理前进行限流
    if err := l.waitForRateLimit(ctx); err != nil {
        logc.Errorw(ctx, "rate limit check failed", logc.Field("err", err))
        return err
    }

    // 后续业务逻辑...
}
```

## 文件修改清单

### 1. 配置文件
- `consumer/internal/config/config.go`: 添加限流配置结构体
- `consumer/etc/consumer.toml`: 添加限流配置参数

### 2. 业务逻辑
- `consumer/internal/logic/backfill_aml_case_logic.go`: 实现限流逻辑

### 3. 测试文件
- `consumer/internal/logic/backfill_aml_case_logic_test.go`: 完整的单元测试

## 代码结构

### 限流器初始化
```go
func NewBackfillAmlCaseLogic(ctx *svc.ServiceContext) *BackfillAmlCaseLogic {
    var rateLimiter *rate.Limiter
    config := ctx.Config.BackfillAmlCase.RateLimit

    if config.Enabled && config.QPS > 0 {
        burst := config.Burst
        if burst <= 0 {
            burst = int(config.QPS) // 默认burst等于QPS
        }
        rateLimiter = rate.NewLimiter(rate.Limit(config.QPS), burst)
    }

    return &BackfillAmlCaseLogic{
        svcCtx:      ctx,
        rateLimiter: rateLimiter,
    }
}
```

### 限流等待逻辑
```go
func (l *BackfillAmlCaseLogic) waitForRateLimit(ctx context.Context) error {
    if l.rateLimiter == nil {
        return nil
    }

    config := l.svcCtx.Config.BackfillAmlCase.RateLimit
    waitTimeout := config.WaitTimeout
    if waitTimeout <= 0 {
        waitTimeout = 2 * time.Second // 默认2秒超时
    }

    waitCtx, cancel := context.WithTimeout(ctx, waitTimeout)
    defer cancel()

    return l.rateLimiter.Wait(waitCtx)
}
```

## 测试覆盖

### 1. 限流器初始化测试
- 启用/禁用限流器
- 不同 QPS 配置
- 默认 burst 值处理

### 2. 限流等待测试
- 正常范围内请求
- 超出突发容量的请求
- 等待超时处理

### 3. 消费限流测试
- 不同限流配置下的消费行为
- 严格限流场景测试

### 4. 并发测试
- 多协程并发请求
- 限流器在高并发下的表现

## 运行测试

```bash
# 运行所有限流相关测试
go test -v -run "TestBackfillAmlCaseLogic.*RateLimit"

# 运行特定测试
go test -v -run TestBackfillAmlCaseLogic_RateLimiter_Initialization
go test -v -run TestBackfillAmlCaseLogic_WaitForRateLimit
go test -v -run TestBackfillAmlCaseLogic_Consume_WithRateLimit
go test -v -run TestBackfillAmlCaseLogic_RateLimit_Concurrency
```

## 监控和日志

### 1. 限流器初始化日志
```
backfill aml case rate limiter initialized | qps:50 burst:100 wait_timeout:2s
```

### 2. 限流触发日志
```
rate limit wait applied | wait_duration:15.2ms
rate limit wait failed | err:context deadline exceeded wait_duration:2s timeout:2s
```

### 3. 错误处理日志
```
rate limit check failed | err:rate limit wait failed: context deadline exceeded
```

## 性能影响

- **正常情况**: 限流检查开销极小（纳秒级别）
- **限流触发**: 会有等待时间，但避免了对数据库的冲击
- **内存占用**: 限流器占用内存很小，主要是几个原子变量

## 配置建议

### 生产环境配置
```toml
[BackfillAmlCase.RateLimit]
Enabled = true
QPS = 50.0        # 根据数据库负载能力调整
Burst = 100       # 允许突发处理能力
WaitTimeout = "5s" # 适当的超时时间
```

### 开发环境配置
```toml
[BackfillAmlCase.RateLimit]
Enabled = false   # 开发环境可以禁用
```

## 扩展性

该实现为其他消费模块提供了限流的模板：
1. 在配置中添加对应的限流配置
2. 在logic结构体中添加限流器字段
3. 在Consume方法开始处添加限流检查
4. 编写相应的单元测试

这样可以确保每个消费模块都有独立的限流控制，避免相互影响。
