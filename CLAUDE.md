# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AML Insight is a comprehensive Anti-Money Laundering (AML) compliance microservice system for Bybit's cryptocurrency exchange. The system handles fiat transaction monitoring, blockchain address labeling, risk assessment, and regulatory reporting through integration with multiple third-party services and internal systems.

## Development Commands

### Building
```bash
# Build all services
make build

# Build specific service (consumer, admin)
make build-consumer
make build-admin

# Using Task runner (alternative)
task b:build
```

### Testing
```bash
# Run all tests
make test
go test ./... -count=1

# Run tests with coverage
make coverage
task b:test:cov

# Coverage will generate reports at tmp/coverage.html
```

### Linting
```bash
# Lint changed files only
make lint

# Lint all files using Task
task b:lint

# Format code
task b:format
```

### Development Tools Setup
```bash
# Install development dependencies
task setup

# Force reinstall tools
task setup:force
```

## Service Architecture

The system consists of three main services:

### Consumer Service (`./consumer/`)
- **Purpose**: Event-driven Kafka message processor
- **Key Functions**: Processes fiat transactions, RiskNarrative webhooks, address label updates, security appeals
- **Configuration**: `consumer/etc/consumer.toml`
- **Binary**: `bin/consumer-srv`

### Admin Service (`./service/admin/`)
- **Purpose**: Administrative APIs and batch processing
- **Key Functions**: Address label management, AML configuration, STR/TMR reporting, scheduled data sync
- **Configuration**: `service/admin/etc/admin.toml`
- **Binary**: `bin/admin-srv`
- **Includes**: XXL-Job scheduling for data synchronization tasks

### API Service (`./service/api/`)
- **Purpose**: External-facing RPC API
- **Key Functions**: Pre-KYA address checking, SF case status queries
- **Configuration**: `service/api/etc/api.toml`

## Key Directories

### Data Layer
- `internal/model/`: Database models with auto-generated and custom methods
  - Core entities: AML cases, address labels, transactions, entity mappings
  - Key files: `*_model.go` (custom), `*_model_gen.go` (generated)

### Business Logic
- `consumer/internal/logic/`: Consumer service business logic
- `service/admin/internal/logic/`: Admin API implementations
- `service/api/internal/logic/`: Public API implementations

### Integration Layer
- `internal/pkg/`: External service integrations (RiskNarrative, OKLink, RPC clients)
- `pkg/`: Reusable utilities (cache, risk assessment, ByData, Lark alerts)

### Configuration
- Service configs in `*/etc/*.toml` files
- Uses Nacos for dynamic configuration management

## External Dependencies

### Message Queues (Kafka)
- Topics: `aml_fiat_transaction`, `aml_risknarrative_webhook`, `fiat-pay-security-topic`
- Address label updates: `aml.aml_insight.address_label.*`

### Databases
- MySQL clusters: `aml_insight` (primary), `risk_aml` (configuration)
- TiDB: High-performance address label storage
- Redis: Caching and rate limiting

### Third-Party Services
- **RiskNarrative**: Primary AML risk assessment provider
- **OKLink**: Blockchain data and transaction analysis
- **Wallet Explorer**: Address clustering analysis
- Rate limiting: 50 QPS with 100 burst capacity for backfill operations

## Testing Guidelines

### Running Single Tests
```bash
# Test specific package
go test ./internal/model -v

# Test specific file pattern
go test ./... -run TestAddressLabel

# Test with coverage for specific package
go test -coverprofile=coverage.out ./internal/logic/...
```

### Mock Generation
- Mocks are located in `internal/mock/`
- Generated using `go.uber.org/mock`
- Key mocks: model interfaces, external clients (RN, OKLink)

## Configuration Management

### Environment-Specific Configs
- Development: `etc/*.toml` files in each service
- Production: Managed via Nacos configuration center
- Local overrides: Use `-f` flag when running services

### Key Configuration Areas
- Database connections (MySQL, TiDB, Redis)
- Kafka consumer groups and topics
- External service endpoints and credentials
- Rate limiting parameters
- Feature flags for different AML checks

## Code Generation

### Model Generation
Generated files (`*_gen.go`) are created from SQL schema definitions in `internal/model/_sql.sql`. Do not edit generated files directly.

### Protocol Buffers
Service definitions use gRPC with protobuf definitions from `code.bydev.io/cht/fiat/backend/bufgen.git/pkg`.

## Development Workflows

### Adding New AML Logic
1. Update database schema in `internal/model/_sql.sql`
2. Regenerate models if needed
3. Implement business logic in appropriate service's `internal/logic/`
4. Add integration tests
5. Update configuration if new external services are involved

### External Service Integration
1. Add client implementation in `internal/pkg/`
2. Create mock in `internal/mock/`
3. Add configuration parameters
4. Implement rate limiting and error handling
5. Add monitoring and alerting

## Security Considerations

- HMAC signing for sensitive operations (see `pkg/risk/`)
- SecHub integration for data encryption
- Channel-based authentication for internal service communication
- Comprehensive audit logging for regulatory compliance

## Monitoring and Observability

- OpenTelemetry integration for distributed tracing
- Prometheus metrics collection
- Lark integration for real-time alerting
- Comprehensive logging for audit trails