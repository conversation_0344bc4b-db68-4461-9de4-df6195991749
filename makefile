SHELL := /bin/bash
GO := go
GOFLAGS := -v -gcflags='all=-N -l'

# Directory structure
BIN_DIR := bin
TMP_DIR := tmp
COVERAGE_FILE := $(TMP_DIR)/coverage.out
COVERAGE_HTML := $(TMP_DIR)/coverage.html

# Binary settings
SERVICES := consumer admin
BINARY_NAMES := $(foreach svc,$(SERVICES),$(svc)-srv)
BINARIES := $(foreach name,$(BINARY_NAMES),$(BIN_DIR)/$(name))

# Map services to their main.go locations - direct mapping
CONSUMER_MAIN := ./consumer/main.go
ADMIN_MAIN := ./service/admin/main.go

# Define excluded directories for coverage (comma separated for Python script)
EXCLUDED_DIRS := */mock/*,*/mock,mock/*,mock

# golangci-lint configuration
GOLANGCI_LINT := golangci-lint
LINT_CONFIG := .golangci.yaml

# Add at the top with other variables
# 是否强制使用master分支
FORCE_MASTER ?= false

# Add after other coverage related variables
MASTER_COVERAGE_FILE := $(TMP_DIR)/master_coverage.out

# Python script for coverage statistics  
COVERAGE_SCRIPT := ./scripts/calc_coverage.py

# Add after EXCLUDED_DIRS
# Patterns for mock files to exclude from coverage
MOCK_PATTERNS := -not -name mock.go -not -name '*_mock.go' -not -name 'mock_*.go'

# Get packages for testing (excluding mock files)
GO_TEST_PKGS := $(shell go list ./... | grep -v "mock")

# Add color definitions
GREEN := \033[32m
RED := \033[31m
BOLD := \033[1m
RESET := \033[0m

# Add more color and style definitions
YELLOW := \033[33m
CYAN := \033[36m
CHECKMARK := ✓
CROSSMARK := ✗

# Create necessary directories
.PHONY: init
init:
	@mkdir -p $(BIN_DIR)
	@mkdir -p $(TMP_DIR)

# Default target
.PHONY: all
all: init build test

# Build the project (all services by default)
.PHONY: build
build: init
	@echo -e "$(CYAN)$(BOLD)Building all services...$(RESET)"
	@echo "----------------------------------------"
	@success_count=0; failure_count=0; \
	success_list=""; failure_list=""; \
	for svc in $(SERVICES); do \
		echo -e "\n$(CYAN)Building $$svc...$(RESET)"; \
		if [ "$$svc" = "consumer" ]; then \
			main_path="$(CONSUMER_MAIN)"; \
		elif [ "$$svc" = "admin" ]; then \
			main_path="$(ADMIN_MAIN)"; \
		else \
			echo -e "$(RED)$(BOLD)$(CROSSMARK) Error: No main.go path defined for service $$svc$(RESET)"; \
			failure_list="$$failure_list $$svc"; \
			failure_count=$$((failure_count + 1)); \
			continue; \
		fi; \
		if [ ! -f "$$main_path" ]; then \
			echo -e "$(RED)$(BOLD)$(CROSSMARK) Error: Main file not found at $$main_path$(RESET)"; \
			failure_list="$$failure_list $$svc"; \
			failure_count=$$((failure_count + 1)); \
			continue; \
		fi; \
		if error_msg=$$($(GO) build $(GOFLAGS) -o $(BIN_DIR)/$$svc-srv $$main_path 2>&1); then \
			echo -e "$(GREEN)$(BOLD)$(CHECKMARK) Successfully built $$svc$(RESET)"; \
			success_list="$$success_list $$svc"; \
			success_count=$$((success_count + 1)); \
		else \
			echo -e "$(RED)$(BOLD)$(CROSSMARK) Failed to build $$svc$(RESET)"; \
			echo -e "$(RED)Error details:$(RESET)"; \
			echo -e "  $$error_msg" | sed 's/^/  /'; \
			failure_list="$$failure_list $$svc"; \
			failure_count=$$((failure_count + 1)); \
		fi; \
	done; \
	echo -e "\n$(CYAN)----------------------------------------$(RESET)"; \
	echo -e "$(CYAN)Build Summary:$(RESET)"; \
	if [ $$success_count -gt 0 ]; then \
		echo -e "$(GREEN)$(BOLD)$(CHECKMARK) Successfully built ($$success_count):$(RESET)$$success_list"; \
	fi; \
	if [ $$failure_count -gt 0 ]; then \
		echo -e "$(RED)$(BOLD)$(CROSSMARK) Failed to build ($$failure_count):$(RESET)$$failure_list"; \
		exit 1; \
	fi

# Build a specific service
.PHONY: build-%
build-%: init
	@if echo "$(SERVICES)" | grep -w "$*" > /dev/null; then \
		echo -e "$(CYAN)$(BOLD)Building $*...$(RESET)"; \
		echo "----------------------------------------"; \
		if [ "$*" = "consumer" ]; then \
			main_path="$(CONSUMER_MAIN)"; \
		elif [ "$*" = "admin" ]; then \
			main_path="$(ADMIN_MAIN)"; \
		else \
			echo -e "$(RED)$(BOLD)$(CROSSMARK) Error: No main.go path defined for service $*$(RESET)"; \
			exit 1; \
		fi; \
		if [ ! -f "$$main_path" ]; then \
			echo -e "$(RED)$(BOLD)$(CROSSMARK) Error: Main file not found at $$main_path$(RESET)"; \
			exit 1; \
		fi; \
		if error_msg=$$($(GO) build $(GOFLAGS) -o $(BIN_DIR)/$*-srv $$main_path 2>&1); then \
			echo -e "$(GREEN)$(BOLD)$(CHECKMARK) Successfully built $*$(RESET)"; \
		else \
			echo -e "$(RED)$(BOLD)$(CROSSMARK) Failed to build $*$(RESET)"; \
			echo -e "$(RED)Error details:$(RESET)"; \
			echo -e "  $$error_msg" | sed 's/^/  /'; \
			exit 1; \
		fi \
	else \
		echo "Unknown service: $*"; \
		echo "Available services: $(SERVICES)"; \
		exit 1; \
	fi

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning..."
	@rm -rf $(BIN_DIR) $(TMP_DIR)
	@$(GO) clean


# Run tests
.PHONY: test
test: init
	@echo "Running tests..."
	@$(GO) test $(GOFLAGS) ./... -count=1

# Run lint on changed files
.PHONY: lint
lint:
	@echo "Running lint on changed files..."
	@changed_dirs=$$(git diff --name-only origin/master... | grep '\.go$$' | grep -v '_test\.go$$' | xargs -I{} dirname {} | sort -u); \
	if [ -n "$$changed_dirs" ]; then \
		for dir in $$changed_dirs; do \
			echo "Checking directory: $$dir"; \
			$(GOLANGCI_LINT) run --config $(LINT_CONFIG) $$dir/... || { \
				echo "\n❌ Lint检查失败: $$dir"; \
				exit 1; \
			}; \
		done \
	else \
		echo "No Go files changed"; \
	fi

# Generate coverage report
.PHONY: coverage
coverage: init
	@echo "Generating coverage report..."
	@if ! $(GO) test -v $(GOFLAGS) -p=5 -covermode=count -coverprofile=$(COVERAGE_FILE) ./...; then \
		echo "\n❌ 单元测试失败,退出覆盖率统计"; \
		exit 1; \
	fi
	@if [ ! -f "$(COVERAGE_FILE)" ]; then \
		echo "\n❌ 覆盖率文件未生成,退出覆盖率统计"; \
		exit 1; \
	fi
	@if [ ! -s "$(COVERAGE_FILE)" ]; then \
		echo "\n❌ 覆盖率文件为空,退出覆盖率统计"; \
		exit 1; \
	fi
	@$(GO) tool cover -html=$(COVERAGE_FILE) -o $(COVERAGE_HTML)
	@echo "Coverage report generated at $(COVERAGE_HTML)"
	@echo "正在计算详细覆盖率统计..."
	@if [ -f $(COVERAGE_SCRIPT) ]; then \
		COVERAGE_FILE="$(COVERAGE_FILE)" \
		MASTER_COVERAGE_FILE="$(MASTER_COVERAGE_FILE)" \
		EXCLUDED_DIRS="$(EXCLUDED_DIRS)" \
		FORCE_MASTER="$(FORCE_MASTER)" \
		python3 $(COVERAGE_SCRIPT) || { echo "\n❌ 覆盖率统计失败"; exit 1; }; \
	else \
		echo "\n❌ 未找到覆盖率统计脚本 $(COVERAGE_SCRIPT)"; \
		exit 1; \
	fi

# Show help
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all            - Build and test the project (default)"
	@echo "  build          - Build all services"
	@echo "  build-<svc>    - Build specific service where <svc> is one of: $(SERVICES)"
	@echo "  test           - Run tests"
	@echo "  lint           - Run lint check on changed files"
	@echo "  coverage       - Generate coverage report and detailed statistics"
	@echo "                   Use FORCE_MASTER=true to force master branch coverage update"
	@echo "  clean          - Clean build artifacts"
	@echo "  help           - Show this help message"