# Mode = "dev"
Name = "aml-insight-consumer"

[Log]
BufferSize = 500
DiscardFullLog = true
Encoding = "json"
Mode = "file"
Path = "/data/logs"
ServiceName = "aml-insight-consumer"

[Nacos]
Key = "aml-insight-consumer"
NamespaceId = "public"
Username = "bybit-nacos"
Password = "sec.decrypt{{kIeOAWDRNCBRJI1c929L1RopjdogrncQL+MGVUuIOjNFXyWWcMC8}}"
[[Nacos.ServerConfigs]]
Address = "nacos.prod.infra.ww5sawfyut0k.bitsvc.io:8848"

[[Consumers]]
GroupID = "aml-insight-consumer"
ID = "aml_fiat_transaction"
InitialOffset = "newest"
Topic = "aml_fiat_transaction"
[Consumers.Client]
AuthType = "password"
Brokers = ["cht-fiat-prod-a.kafka.infra.ww5sawfyut0k.bitsvc.io:9093","cht-fiat-prod-b.kafka.infra.ww5sawfyut0k.bitsvc.io:9093","cht-fiat-prod-c.kafka.infra.ww5sawfyut0k.bitsvc.io:9093"]
ClientType = "sarama"
SaslMechanism = "SCRAM-SHA-256"
SaslPassword = "sec.decrypt{{lsv7YuZImt/W+PbI+UP/M+k4pO0dRkHxupZ4KaGOmVl/R2OQnlPZJg==}}"
SaslUsername = "appId_008136_cn"
Version = "2.0.0"
[Consumers.RetryConfig]
MaxRetries = 0

[[Consumers]]
GroupID = "aml-insight-consumer"
ID = "aml_risknarrative_webhook"
InitialOffset = "newest"
Topic = "aml_risknarrative_webhook"
[Consumers.Client]
AuthType = "password"
Brokers = ["cht-fiat-prod-a.kafka.infra.ww5sawfyut0k.bitsvc.io:9093","cht-fiat-prod-b.kafka.infra.ww5sawfyut0k.bitsvc.io:9093","cht-fiat-prod-c.kafka.infra.ww5sawfyut0k.bitsvc.io:9093"]
ClientType = "sarama"
SaslMechanism = "SCRAM-SHA-256"
SaslPassword = "sec.decrypt{{lsv7YuZImt/W+PbI+UP/M+k4pO0dRkHxupZ4KaGOmVl/R2OQnlPZJg==}}"
SaslUsername = "appId_008136_cn"
Version = "2.0.0"
[Consumers.RetryConfig]
MaxRetries = 0

[[Consumers]]
  ID = "aml_appeal_event"
  GroupID = "aml-insight-consumer"
  InitialOffset = "newest"
  Topic = "fiat-pay-security-topic"
[Consumers.Client]
      AuthType = "password"
      Brokers = ["cht-fiat-prod-a.kafka.infra.ww5sawfyut0k.bitsvc.io:9093", "cht-fiat-prod-b.kafka.infra.ww5sawfyut0k.bitsvc.io:9093","cht-fiat-prod-c.kafka.infra.ww5sawfyut0k.bitsvc.io:9093"]
      SaslMechanism = "SCRAM-SHA-256"
      SaslUsername = "appId_008136_cn"
      SaslPassword = "sec.decrypt{{lsv7YuZImt/W+PbI+UP/M+k4pO0dRkHxupZ4KaGOmVl/R2OQnlPZJg==}}"
      Version = "2.7.0"
[Consumers.RetryConfig]
      MaxRetries = 0

[MySql]
Datasource = "aml_insight_rw:sec.decrypt{{3TnxUfw9svQVqrBdDaDR3281MjA62N4aycJ9JeNnUcFv8u9VOYTKRL19DLlbZdG6+kmnwt596u8pyecY}}@tcp(prod-fiat-aml-rds-cluster.cluster-chrzwnvxot1q.ap-southeast-1.rds.amazonaws.com:3306)/aml_insight?charset=utf8mb4&parseTime=True&loc=Local&readTimeout=3s&timeout=3s&writeTimeout=3s"

[AMLInsightMysql]
Datasource = "aml_insight_rw:sec.decrypt{{3TnxUfw9svQVqrBdDaDR3281MjA62N4aycJ9JeNnUcFv8u9VOYTKRL19DLlbZdG6+kmnwt596u8pyecY}}@tcp(prod-fiat-aml-rds-cluster.cluster-chrzwnvxot1q.ap-southeast-1.rds.amazonaws.com:3306)/aml_insight?charset=utf8mb4&parseTime=True&loc=Local&readTimeout=3s&timeout=3s&writeTimeout=3s"

[RiskAmlMysql]
Datasource = "risk_aml_rw:sec.decrypt{{Ss1B5eWuuwvYMCMbIU1G69CDfF6VInLNMPECzddjOwPYZkJiD4xdaXsjYPPBFuyHYWNLPMbwh5RyLvAP}}@tcp(prod-fiat-aml-rds-cluster.cluster-chrzwnvxot1q.ap-southeast-1.rds.amazonaws.com:3306)/risk_aml?charset=utf8mb4&parseTime=true&loc=Local&timeout=3s&readTimeout=3s&writeTimeout=3s"

[[Cache]]
    Host = "fiat-prod-payment-go-redis-cluster.nlrtwh.clustercfg.apse1.cache.amazonaws.com:6379"
    Pass = "sec.decrypt{{kE7JeF93wgp0H+8wI/wS0YRu7t3WzQ9Mvd/zuV0xfsMufQNOlp6rMP65bKJ7kAM/FTCQTSAt4H1z4Q==}}"
    Type = "cluster"
    NonBlock = false

[BizRedis]
    Host = "prod-fiat-aml-security-redis-cluster.nlrtwh.clustercfg.apse1.cache.amazonaws.com:6379"
    Type = "cluster"
    Pass = "sec.decrypt{{tFwuckNrL0WuSjNYr27GuhGYwiwR3fTaMx4tKSXmTKwGh++WwFU3UuTasdqxw8zFheu1qIqxdDA9JA==}}"   

[RNConfig]
Addr = "https://apac.trunarrative.cloud"
CreateUserPath = "/TruAccountAPI/rest/Accounts/v1/RunStrategy"
Debug = true
Name = "RNConfig"
TransactionPath = "/TruAccountAPI/rest/Accounts/v1/RunStrategy"
[RNConfig.FiatChannelOrg]
Password = "sec.decrypt{{+asgin16T9Q7geQaILhKicJDYsqvLgQYJfJcX4LZIFJ4XZBSuBS2mLV3LLo=}}"
StrategyId = 33
Username = "<EMAIL>"

[RNConfig.CardEEAOrg]
Password = "sec.decrypt{{+asgin16T9Q7geQaILhKicJDYsqvLgQYJfJcX4LZIFJ4XZBSuBS2mLV3LLo=}}"
StrategyId = 35
Username = "<EMAIL>"

[RNConfig.CardNonEEAOrg]
Password = "sec.decrypt{{+asgin16T9Q7geQaILhKicJDYsqvLgQYJfJcX4LZIFJ4XZBSuBS2mLV3LLo=}}"
StrategyId = 34
Username = "<EMAIL>"

[KycInternalRpc]
Timeout = 500
[KycInternalRpc.CmdHub]
All = true
Server = "bything:///bything.gw"
[KycInternalRpc.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[KycServicePrivateClient]
Timeout = 1000
NonBlock = true
[KycServicePrivateClient.Nacos]
Key = "kyc-service-private"
[KycServicePrivateClient.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[CardManagerRpc]
Timeout = 2000
NonBlock = true
[CardManagerRpc.Nacos]
Key = "fiat-card-manager"
[CardManagerRpc.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[CardCenterRpc]
Timeout = 2000
NonBlock = true
[CardCenterRpc.Nacos]
Key = "fiat-card-center"
[CardCenterRpc.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[AppealRpc]
Timeout = 10000
NonBlock = true
[AppealRpc.Nacos]
Key = "FiatAppealAPI"
[AppealRpc.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[FiatUserRpc]
Timeout = 1000
NonBlock = true
[FiatUserRpc.Nacos]
Key = "fiat-user-provider"
[FiatUserRpc.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[BizConfig]
Timeout = 1000
NonBlock = true
[BizConfig.Nacos]
Key = "pay-biz-config"
[BizConfig.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[BanInternalClient]
    Timeout = 2000
[BanInternalClient.CmdHub]
    Server = "bything:///bything.gw"
    All = true
[BanInternalClient.Middlewares.Logger]
    Enabled = true
    LogReq = true
    LogResp = true 
    
[NotificationClient]
    Timeout = 2000
[NotificationClient.CmdHub]
    Server = "bything:///bything.gw"
    All = true
[NotificationClient.Middlewares.Logger]
    Enabled = true
    LogReq = true
    LogResp = true 

[RiskControlHubRpc]
Timeout = 3000
NonBlock = false
[RiskControlHubRpc.Nacos]
Key = "risk-control-hub-api"
[RiskControlHubRpc.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[FiatProdProducer]
[FiatProdProducer.Client]
Brokers=["cht-fiat-prod-a.kafka.infra.ww5sawfyut0k.bitsvc.io:9093","cht-fiat-prod-b.kafka.infra.ww5sawfyut0k.bitsvc.io:9093","cht-fiat-prod-c.kafka.infra.ww5sawfyut0k.bitsvc.io:9093"]
AuthType = "password"
SaslMechanism = "SCRAM-SHA-256"
SaslUsername = "appId_008136_cn"
SaslPassword = "sec.decrypt{{lsv7YuZImt/W+PbI+UP/M+k4pO0dRkHxupZ4KaGOmVl/R2OQnlPZJg==}}"
Version = "2.7.0"
ClientType = "sarama"