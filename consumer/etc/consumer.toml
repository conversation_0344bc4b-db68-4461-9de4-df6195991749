Mode = "dev"
Name = "aml-insight-consumer"

[Log]
BufferSize = 500
DiscardFullLog = true
Encoding = "json"
Mode = "file"
Path = "/data/logs"
ServiceName = "aml-insight-consumer"

[Nacos]
Key = "aml-insight-consumer"
NamespaceId = "unify-dev-3"
Password = "bybit-nacos"
Username = "bybit-nacos"
[[Nacos.ServerConfigs]]
Address = "nacos.test.infra.ww5sawfyut0k.bitsvc.io:8848"

[[Consumers]]
GroupID = "aml-insight-consumer"
ID = "aml_fiat_transaction"
InitialOffset = "newest"
Topic = "aml_fiat_transaction"
[Consumers.Client]
AuthType = "none"
Brokers = ["kafka-internal-kafka-0:9094", "kafka-internal-kafka-1:9094", "kafka-internal-kafka-2:9094"]
ClientType = "sarama"
SaslMechanism = "PLAIN"
SaslPassword = ""
SaslUsername = ""
Version = "2.0.0"
[Consumers.RetryConfig]
MaxRetries = 0

[[Consumers]]
GroupID = "aml-insight-consumer"
ID = "aml_risknarrative_webhook"
InitialOffset = "newest"
Topic = "aml_risknarrative_webhook"
[Consumers.Client]
AuthType = "none"
Brokers = ["kafka-internal-kafka-0:9094", "kafka-internal-kafka-1:9094", "kafka-internal-kafka-2:9094"]
ClientType = "sarama"
SaslMechanism = "PLAIN"
SaslPassword = ""
SaslUsername = ""
Version = "2.0.0"
[Consumers.RetryConfig]
MaxRetries = 0

[[Consumers]]
GroupID = "aml-insight-consumer"
ID = "aml_appeal_event"
InitialOffset = "newest"
Topic = "fiat-pay-security-topic"
[Consumers.Client]
AuthType = "none"
Brokers = ["kafka-internal-kafka-0:9094", "kafka-internal-kafka-1:9094", "kafka-internal-kafka-2:9094"]
ClientType = "sarama"
SaslMechanism = "PLAIN"
SaslPassword = ""
SaslUsername = ""
Version = "2.0.0"
[Consumers.RetryConfig]
MaxRetries = 0

[[Consumers]]
GroupID = "aml-insight-consumer"
ID = "aml.aml_insight.address_label.whitelist"
InitialOffset = "newest"
Topic = "aml.aml_insight.address_label.whitelist"
[Consumers.Client]
AuthType = "none"
Brokers = ["kafka-internal-kafka-0:9094", "kafka-internal-kafka-1:9094", "kafka-internal-kafka-2:9094"]
ClientType = "sarama"
SaslMechanism = "PLAIN"
SaslPassword = ""
SaslUsername = ""
Version = "2.0.0"
[Consumers.RetryConfig]
MaxRetries = 0

[[Consumers]]
GroupID = "aml-insight-consumer"
ID = "aml.aml_insight.address_label.blacklist"
InitialOffset = "newest"
Topic = "aml.aml_insight.address_label.blacklist"
[Consumers.Client]
AuthType = "none"
Brokers = ["kafka-internal-kafka-0:9094", "kafka-internal-kafka-1:9094", "kafka-internal-kafka-2:9094"]
ClientType = "sarama"
SaslMechanism = "PLAIN"
SaslPassword = ""
SaslUsername = ""
Version = "2.0.0"
[Consumers.RetryConfig]
MaxRetries = 0

[[Consumers]]
GroupID = "aml-insight-consumer"
ID = "aml.aml_insight.aml_case.backfill"
InitialOffset = "newest"
Topic = "aml.aml_insight.aml_case.backfill"
[Consumers.Client]
AuthType = "none"
Brokers = ["kafka-internal-kafka-0:9094", "kafka-internal-kafka-1:9094", "kafka-internal-kafka-2:9094"]
ClientType = "sarama"
SaslMechanism = "PLAIN"
SaslPassword = ""
SaslUsername = ""
Version = "2.0.0"
[Consumers.RetryConfig]
MaxRetries = 0

[MySql]
Datasource = "app_user:sec.decrypt{{xGxBmN05cyTITHSj+c7I9z6z+1cJu0Tciba9tZMcFY46W59i1mn0lyics90JiL3+BLqlxzuYHHONhJZlow==}}@tcp(mysql-internal:3306)/aml_insight?charset=utf8mb4&parseTime=True&loc=Local&readTimeout=3s&timeout=3s&writeTimeout=3s"
# PLO75FbcfmFYRuQEGmygZ9PyQCQbmgeD5

[TiDBCfg]
Datasource = "fiat_aml_chain_user:sec.decrypt{{F6fwbP/gSSVmd9xcvNg4zUKstzlKHFAap5Y1Zqa/PCMuFas+tv73RIB3/q3qSLmYJ+o9}}@tcp(infra-test-tidb-server-nlb-429c81ada8761c8d.elb.ap-southeast-1.amazonaws.com:4000)/fiat_aml_chain"

[[Cache]]
Host = "standalone-redis-internal:6379"
Pass = ""
Type = "node" #cluster

[RNConfig]
Addr = "https://pp.trunarrative.cloud"
CreateUserPath = "/TruAccountAPI/rest/Accounts/v1/RunStrategy"
Debug = true
Name = "RNConfig"
TransactionPath = "/TruAccountAPI/rest/Accounts/v1/RunStrategy"
[RNConfig.FiatChannelOrg]
Password = "#^%!&?&^r8gWVWWX"
StrategyId = 1896
Username = "<EMAIL>"

[RNConfig.CardEEAOrg]
Password = "#^%!&?&^r8gWVWWX"
StrategyId = 1986
Username = "<EMAIL>"

[RNConfig.CardNonEEAOrg]
Password = "#^%!&?&^r8gWVWWX"
StrategyId = 1986
Username = "<EMAIL>"

[KycInternalRpc]
Timeout = 500
[KycInternalRpc.CmdHub]
All = true
Server = "bything:///bything.gw"
[KycInternalRpc.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[KycServicePrivateClient]
Timeout = 1000
NonBlock = true
[KycServicePrivateClient.Nacos]
Key = "kyc-service-private"
[KycServicePrivateClient.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[CardManagerRpc]
Timeout = 2000
NonBlock = true
[CardManagerRpc.Nacos]
Key = "fiat-card-manager"
[CardManagerRpc.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[CardCenterRpc]
Timeout = 2000
NonBlock = true
[CardCenterRpc.Nacos]
Key = "fiat-card-center"
[CardCenterRpc.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[AppealRpc]
Timeout = 1000
NonBlock = true
[AppealRpc.Nacos]
Key = "FiatAppealAPI"
[AppealRpc.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

[FiatUserRpc]
Timeout = 1000
NonBlock = true
[FiatUserRpc.Nacos]
Key = "fiat-user-provider"
[FiatUserRpc.Middlewares.Logger]
Enabled = true
LogReq = true
LogResp = true

# BackfillAmlCase Consumer Configuration
[BackfillAmlCase]
[BackfillAmlCase.RateLimit]
# 是否启用限流
Enabled = true
# 每秒允许的请求数 (QPS)
QPS = 50.0
# Token bucket 容量 (突发处理能力)
Burst = 100
# 等待token的最大超时时间
WaitTimeout = "2s"
