package svc

import (
	amlv1 "code.bydev.io/cht/customer/kyc-stub.git/pkg/bybit/kyc/aml/v1"
	appealv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/appeal/v1"
	bizconfigv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/bizconfig/v1"
	risk_control_hubv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/risk_control_hub/v1"
	"code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/card"
	card_center "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/card-center"
	"code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/user"
	"code.bydev.io/frameworks/byone/core/stores/redis"
	"code.bydev.io/frameworks/byone/kafka"
	"code.bydev.io/frameworks/byone/zrpc"
	"git.bybit.com/svc/stub/pkg/pb/api/ban"
	platformkyc "git.bybit.com/svc/stub/pkg/pb/api/kyc"
	"git.bybit.com/svc/stub/pkg/pb/api/notification"
	site "git.bybit.com/svc/stub/pkg/pb/api/sitv1"

	"aml-insight/consumer/internal/config"
	"aml-insight/internal/model"
	"aml-insight/internal/pkg/rn"
)

type ServiceContext struct {
	Config config.Config

	// aml_insight
	FiatAmlTransactionModel model.FiatAmlTransactionModel
	RNLogModel              model.RnLogModel
	RNUserModel             model.RnUserModel
	BizRedis                *redis.Redis
	// risk_aml
	SfCaseModel            model.SfCaseModel
	AmlCaseModel           model.AmlCaseModel
	AllAddressLabelModel   model.AllAddressLabelModel
	EntityNameMappingModel model.EntityNameMappingModel

	RNClient rn.Client

	KycInternalClient       platformkyc.KycInternalClient              // bything 调用
	AmlAPIClient            amlv1.AmlAPIClient                         // Kyc-service-private for amlAPI
	CardManagerClient       card.CardManagerFacadeServiceClient        // Card manager client
	CardCenterClient        card_center.CardCenterServiceClient        // Card center client
	AppealClient            appealv1.AppealAPIClient                   // security-appeal
	FiatUserClient          user.GrpcUserServiceClient                 // fiat user provider
	BizConfigClient         bizconfigv1.CustomerAPIClient              // biz config client
	RiskControlHubAPIClient risk_control_hubv1.RiskControlHubAPIClient // risk control hub
	BanInternalClient       ban.BanInternalClient                      // ban internal client
	NotificationClient      notification.NotificationClient            // notification
	SiteServiceClient       site.SiteServiceClient

	// kafka
	FiatProdProducer kafka.Producer // fiat 的 kafka producer
}

func NewServiceContext(c config.Config) *ServiceContext {

	return &ServiceContext{
		Config:                  c,
		FiatAmlTransactionModel: model.MustNewFiatAmlTransactionModel(c.AMLInsightMysql),
		RNLogModel:              model.MustNewRnLogModel(c.AMLInsightMysql),
		RNUserModel:             model.MustNewRnUserModel(c.AMLInsightMysql, c.Cache),
		SfCaseModel:             model.MustNewSfCaseModel(c.RiskAmlMysql),
		AmlCaseModel:            model.MustNewAmlCaseModel(c.RiskAmlMysql),
		AllAddressLabelModel:    model.MustNewAllAddressLabelModel(c.TiDBCfg),
		EntityNameMappingModel:  model.MustNewEntityNameMappingModel(c.AMLInsightMysql, model.CacheConfig{}), // FIXME: 需要配置缓存
		BizRedis:                c.BizRedis.NewRedis(),

		RNClient: rn.NewClient(c.RNConfig),

		KycInternalClient:       platformkyc.NewKycInternalClient(zrpc.MustNewClient(c.KycInternalRpc).Conn()),
		AmlAPIClient:            amlv1.NewAmlAPIClient(zrpc.MustNewClient(c.KycServicePrivateClient).Conn()),
		CardManagerClient:       card.NewCardManagerFacadeServiceClient(zrpc.MustNewClient(c.CardManagerRpc).Conn()),
		CardCenterClient:        card_center.NewCardCenterServiceClient(zrpc.MustNewClient(c.CardCenterRpc).Conn()),
		AppealClient:            appealv1.NewAppealAPIClient(zrpc.MustNewClient(c.AppealRpc).Conn()),
		FiatUserClient:          user.NewGrpcUserServiceClient(zrpc.MustNewClient(c.FiatUserRpc).Conn()),
		BizConfigClient:         bizconfigv1.NewCustomerAPIClient(zrpc.MustNewClient(c.BizConfig).Conn()),
		RiskControlHubAPIClient: risk_control_hubv1.NewRiskControlHubAPIClient(zrpc.MustNewClient(c.RiskControlHubRpc).Conn()),
		BanInternalClient:       ban.NewBanInternalClient(zrpc.MustNewClient(c.BanInternalClient).Conn()),
		NotificationClient:      notification.NewNotificationClient(zrpc.MustNewClient(c.NotificationClient).Conn()),
		SiteServiceClient:       site.NewSiteServiceClient(zrpc.MustNewClient(c.SiteServiceClient).Conn()),

		FiatProdProducer: kafka.MustNewProducer(c.FiatProdProducer),
	}
}
