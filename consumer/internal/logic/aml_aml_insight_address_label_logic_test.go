package logic

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"aml-insight/consumer/internal/svc"
	mockmodel "aml-insight/internal/mock/model"
	"aml-insight/internal/model"

	"code.bydev.io/frameworks/byone/kafka"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestAllAddressLabelLogic_Consume(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	t.Run("success", func(t *testing.T) {
		// Setup
		mockAddressLabelModel := mockmodel.NewMockAllAddressLabelModel(ctrl)
		svcCtx := &svc.ServiceContext{
			AllAddressLabelModel: mockAddressLabelModel,
		}
		logic := NewAllAddressLabelLogic(svcCtx)

		// Create test data
		addressLabel := &model.AllAddressLabel{
			Address: "0x1234567890",
			Chain:   "ethereum",
		}
		messageBytes, _ := json.Marshal(addressLabel)
		message := &kafka.Message{
			Value: messageBytes,
		}

		// Set expectations
		mockAddressLabelModel.EXPECT().
			Upsert(gomock.Any(), gomock.Eq(addressLabel)).
			Return(int64(1), nil)

		// Execute
		err := logic.Consume(context.Background(), message)

		// Assert
		assert.NoError(t, err)
	})

	t.Run("empty_message", func(t *testing.T) {
		// Setup
		mockAddressLabelModel := mockmodel.NewMockAllAddressLabelModel(ctrl)
		svcCtx := &svc.ServiceContext{
			AllAddressLabelModel: mockAddressLabelModel,
		}
		logic := NewAllAddressLabelLogic(svcCtx)

		// Empty message
		message := &kafka.Message{
			Value: []byte{},
		}

		// No expectations - should return early

		// Execute
		err := logic.Consume(context.Background(), message)

		// Assert
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "message or message value is empty")
	})

	t.Run("nil_message", func(t *testing.T) {
		// Setup
		mockAddressLabelModel := mockmodel.NewMockAllAddressLabelModel(ctrl)
		svcCtx := &svc.ServiceContext{
			AllAddressLabelModel: mockAddressLabelModel,
		}
		logic := NewAllAddressLabelLogic(svcCtx)

		// Nil message
		var message *kafka.Message = nil

		// No expectations - should return early

		// Execute
		err := logic.Consume(context.Background(), message)

		// Assert
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "message is nil")
	})

	t.Run("unmarshal_error", func(t *testing.T) {
		// Setup
		mockAddressLabelModel := mockmodel.NewMockAllAddressLabelModel(ctrl)
		svcCtx := &svc.ServiceContext{
			AllAddressLabelModel: mockAddressLabelModel,
		}
		logic := NewAllAddressLabelLogic(svcCtx)

		// Invalid message format
		message := &kafka.Message{
			Value: []byte("invalid json"),
		}

		// No expectations for the model - should return early with error

		// Execute
		err := logic.Consume(context.Background(), message)

		// Assert
		assert.Error(t, err)
	})

	t.Run("upsert_error", func(t *testing.T) {
		// Setup
		mockAddressLabelModel := mockmodel.NewMockAllAddressLabelModel(ctrl)
		svcCtx := &svc.ServiceContext{
			AllAddressLabelModel: mockAddressLabelModel,
		}
		logic := NewAllAddressLabelLogic(svcCtx)

		// Create test data
		addressLabel := &model.AllAddressLabel{
			Address: "0x1234567890",
			Chain:   "ethereum",
		}
		messageBytes, _ := json.Marshal(addressLabel)
		message := &kafka.Message{
			Value: messageBytes,
		}

		// Set expectations with error
		expectedErr := errors.New("database error")
		mockAddressLabelModel.EXPECT().
			Upsert(gomock.Any(), gomock.Eq(addressLabel)).
			Return(int64(0), expectedErr)

		// Execute
		err := logic.Consume(context.Background(), message)

		// Assert
		// The function logs the error but returns nil
		assert.NoError(t, err)
	})
}
