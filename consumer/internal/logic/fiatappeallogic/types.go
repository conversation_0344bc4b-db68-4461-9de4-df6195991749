package fiatappeallogic

type (
	MQBody struct {
		T        int64  `json:"t,omitempty"`        // 秒级时间戳
		TStr     string `json:"tstr,omitempty"`     // 时间方便可视化
		MQSource string `json:"ms,omitempty"`       // 消息来源
		Event    string `json:"event,omitempty"`    // 事件类型
		TraceId  string `json:"trace_id,omitempty"` // 作为消息的标识同时串联起来
		Ext      string `json:"ext,omitempty"`      // 其他说明
		Body     string `json:"body,omitempty"`     // 消息体，消费者自己解析
	}

	SyncAmlAppealInfo struct {
		UserID           int64    `json:"user_id,omitempty"`
		OrderID          string   `json:"order_id,omitempty"`
		RelatedOrderID   []string `json:"related_order_id,omitempty"`
		Status           string   `json:"status,omitempty"`
		AppealID         string   `json:"appeal_id,omitempty"`
		BizAppealStatus  string   `json:"biz_appeal_status,omitempty"`
		AppealAddressWeb string   `json:"appeal_address_web,omitempty"`
		AppealAddressApp string   `json:"appeal_address_app,omitempty"`
		Ext              Ext      `json:"ext,omitempty"`
	}

	Ext struct {
		Salesforce SFBusinessKafkaMessage `json:"salesforce,omitempty"`
	}
)

type (
	SFBusinessKafkaMessage struct {
		BusinessSource string         `json:"business_source,omitempty"`
		SObject        string         `json:"s_object,omitempty"`
		UniqueID       string         `json:"unique_id,omitempty"`
		UserEmail      string         `json:"user_email,omitempty"`
		BusinessData   SFBusinessData `json:"business_data,omitempty"`
	}
	SFBusinessData struct {
		UID                   string          `json:"uid,omitempty"`          // Account.Name
		CaseType              string          `json:"caseType,omitempty"`     // Case_Typev2__c
		SubType               string          `json:"subType,omitempty"`      // Sub_Typev2__c
		Category              string          `json:"category,omitempty"`     // Categoryv2__c
		RequestID             string          `json:"requestId,omitempty"`    // 原始的reqId
		GroupID               string          `json:"groupId,omitempty"`      // 原始的groupId，即风控工单id
		CaseNumber            string          `json:"caseNumber,omitempty"`   // CaseNumber
		CaseStatus            string          `json:"caseStatus,omitempty"`   // Status
		AMLStatus             string          `json:"amlStatus,omitempty"`    // Review_Results__c
		AMLFlow               int             `json:"amlFlow,omitempty"`      // 流程节点
		EDDFlow               string          `json:"eddFlow,omitempty"`      // Approved, Rejected, RFI, STR
		RFIMaterials          []SFRFIMaterial `json:"RFIMaterials,omitempty"` // 驳回需要补充的材料
		BanType               []SFBanType     `json:"banType,omitempty"`      // 封禁相关的处置
		JustifiedAnnualIncome string          `json:"justifiedAnnualIncome,omitempty"`
		NextSpendingTrigger   string          `json:"nextSpendingTrigger,omitempty"`
		RejectReason          string          `json:"rejectReason,omitempty"`
		Comments              string          `json:"comments,omitempty"`
	}

	SFRFIMaterial struct {
		DocumentCode string `json:"documentCode,omitempty"` // PIDC, POF, CUSTOM_DOCUMENT, CUSTOM_INSTRUCTION
	}

	SFBanType struct {
		ActionType string `json:"action_type,omitempty"`
		Params     string `json:"params,omitempty"`
	}
)
