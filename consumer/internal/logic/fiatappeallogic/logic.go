package fiatappeallogic

import (
	"context"
	"fmt"
	"strings"

	"code.bydev.io/frameworks/byone/core/logc"
	"git.bybit.com/svc/stub/pkg/pb/api/ban"
	"git.bybit.com/svc/stub/pkg/pb/api/notification"
	"github.com/google/uuid"

	"aml-insight/consumer/internal/svc"
	"aml-insight/internal/model"
	"aml-insight/internal/pkg/event"
	"aml-insight/pkg/common"
)

type Logic struct {
	svcCtx *svc.ServiceContext
}

const (
	AdditionalKyaScanRequestPrefix = "AS-" // AS: Additional Scan
)

func IsAdditionalKyaReq(requestId string) bool {
	return strings.HasPrefix(requestId, AdditionalKyaScanRequestPrefix)
}

func NewLogic(svcCtx *svc.ServiceContext) *Logic {
	return &Logic{svcCtx: svcCtx}
}

// ProcessAppealStatusChangeEvent 处理申诉状态变更事件
// 1. 根据 appeal_id 查找所有对应的 sfCases。
// 2. 更新 sfCase 的状态。
// 3. 对资产归集发送状态变更。
// 4. 对 asset 发送状态变更。
// 5. 解封用户。
// 6. 更新本地状态。
// 7. 关闭ChTransferRegisterModel,不在 20250316 实现。
func (l *Logic) ProcessAppealStatusChangeEvent(ctx context.Context, appealInfo *SyncAmlAppealInfo) error {
	if appealInfo.AppealID == "" {
		logc.Infow(ctx, "Empty appeal_id", logc.Field("appeal_id", appealInfo.AppealID))
		return nil
	}
	cases, err := l.svcCtx.SfCaseModel.FindListByGroupID(ctx, appealInfo.AppealID)
	if err != nil {
		logc.Errorw(ctx, "FindListByGroupID error", logc.Field("err", err))
		return err
	}

	if len(cases) == 0 {
		logc.Infow(ctx, "No case found", logc.Field("appeal_id", appealInfo.AppealID))
		return nil
	}
	var tryUnban bool
	for _, each := range cases {
		if each.WorkOrderChannel != "appealAPI" {
			continue
		}
		oldAssetStatus := common.ConvertAssetCaseStatus(each, appealInfo.Status)
		each.SfStatus = appealInfo.BizAppealStatus
		each.AmlStatus = model.StringToNullString(appealInfo.Ext.Salesforce.BusinessData.AMLStatus)
		each.AmlFlow = int32(appealInfo.Ext.Salesforce.BusinessData.AMLFlow)
		each.WebEddLink = appealInfo.AppealAddressWeb
		each.AppEddLink = appealInfo.AppealAddressApp

		if !IsAdditionalKyaReq(each.RequestId) {
			// 优先给资产归集发送消息
			l.processSendMsgToCollectionAndDw(ctx, each)
			l.processSendMsgToAsset(ctx, each, oldAssetStatus, appealInfo.Status)
		} else {
			logc.Infow(ctx, "additional kya scan, ignore it", logc.Field("appeal_id", appealInfo.AppealID), logc.Field("request_id", each.RequestId))
		}

		switch each.CaseType {
		case 21, 22, 23, 24, 25, 26, 27: // 需要尝试解封用户
			tryUnban = true
		}

		if err := l.svcCtx.SfCaseModel.Update(ctx, each); err != nil {
			logc.Errorw(ctx, "Update error", logc.Field("err", err))
		}
	}

	if tryUnban {
		l.processLiftBanIfNeed(ctx, appealInfo.AppealID, appealInfo.Ext.Salesforce.BusinessData.AMLStatus, appealInfo.Ext.Salesforce.UserEmail, int64(cases[0].MemberId)) // 我们直接取第一个 uid 即可
	}
	if appealInfo.Status == "REJECTED" || appealInfo.BizAppealStatus == "Pending Trader Reply additional EDD" {
		params := make(map[string]string)
		params["UID"] = fmt.Sprintf("%d", cases[0].MemberId)
		params["SFCaseID"] = cases[0].CaseId

		notificationResp, err := l.svcCtx.NotificationClient.Send(ctx, &notification.SendRequest{
			UserIds: []int64{appealInfo.UserID},
			Params:  params,
			TopicId: 390000011,
			Scene:   "AML-EDD",
			SysType: 1,
		})
		switch {
		case err != nil:
			logc.Errorw(ctx, "notificationClientSendErr", logc.Field("error", err))
		case notificationResp.GetError() != nil || len(notificationResp.GetFailedUserIds()) > 0:
			logc.Errorw(ctx, "SendAppealNotificationError", logc.Field("resp", notificationResp))
		}
	}

	return nil

}

// processSendMsgToCollectionAndDw 判断是否需要发送消息，
// 需要是充值的请求；只有 KYA & STR 才需要发送；只有到底终态的才需要发送。
func (l *Logic) processSendMsgToCollectionAndDw(ctx context.Context, sfCase *model.SfCase) {
	amlCase, err := l.svcCtx.AmlCaseModel.FindOneByRequestId(ctx, sfCase.RequestId)
	if err != nil {
		logc.Errorw(ctx, "processSendMsgToCollectionAndDw failed error", logc.Field("err", err))
		return
	}
	if amlCase.ActionType != 1 { // 只有充值的需要处理, actionType, 1: 充值，2: 提现
		// 忽略非充值的 case
		return
	}

	// SF_CASE_KYA_NEW       = 21
	// SF_CASE_KYA_EMPTY_NEW = 22
	// SF_CASE_KYT_NEW       = 23
	// SF_CASE_KYT_EMPTY_NEW = 24
	// SF_CASE_MANUAL_NEW    = 25
	// SF_CASE_STR_NEW       = 27
	switch sfCase.CaseType {
	case 21, 22, 26, 27:
	default: // 其他的资产都没有冻结的资金。
		return
	}

	if sfCase.SfStatus != "Solved" {
		return
	}

	switch sfCase.AmlFlow {
	case 6, 7, 8:
	default: // 非终态的不需要发送
		return
	}

	// 发送资金归集解冻消息 给人 risk-aml-gateway
	event.DoSendRiskGatewayProxyMsg(ctx, l.svcCtx.FiatProdProducer, sfCase, int(amlCase.TxIndex))
}

// processSendMsgToAsset 判断是否需要发送消息，
func (l *Logic) processSendMsgToAsset(ctx context.Context, sfCase *model.SfCase,
	oldAssetStatus string, appealStatus string) {
	//newAssetStatus := convertAssetCaseStatus(sfCase)
	//if oldAssetStatus == newAssetStatus { // 这块尽量全部推送，保证资产能够更新消息
	//	return
	//}
	// 发送资产状态变更消息
	if sfCase.DepositWithdrawal != "received" {
		logc.Infow(ctx, "processSendMsgToAsset ignore not received:", logc.Field("sfCase", sfCase))
		return
	}
	event.DoSendNoticeToAsset(ctx, l.svcCtx.FiatProdProducer, sfCase, appealStatus)
}

func (l *Logic) processLiftBanIfNeed(ctx context.Context, requestId, amlStatus, userEmail string, memberId int64) {
	var banTags []*ban.BanTag
	if strings.Contains(amlStatus, "Lift Withdrawal Ban") {
		//unBanTags = append(unBanTags, label.BanTagsWithdraw...)
		banTags = append(banTags,
			&ban.BanTag{BizType: "WITHDRAW", Name: "withdraw", Value: "all_withdraw"},
			&ban.BanTag{BizType: "WITHDRAW", Name: "withdraw", Value: "non-principal"})
	}
	// 交易解封
	if strings.Contains(amlStatus, "Lift Trading Ban") {
		banTags = append(banTags,
			&ban.BanTag{BizType: "TRADE", Name: "trade", Value: "all_trade"},
			&ban.BanTag{BizType: "TRADE", Name: "trade", Value: "lighten_up"})
	}
	if len(banTags) <= 0 {
		logc.Infow(ctx, "no need to unban user", logc.Field("amlStatus", amlStatus))
		return
	}

	newRequestId := uuid.New().String()
	logc.Infow(ctx, "use uuid replace request id", logc.Field("request_id", requestId), logc.Field("new_request_id", newRequestId))
	requestId = newRequestId
	// 这块没有检查用户现在是否正在封禁，我们直接进行解封
	resp, err := l.svcCtx.BanInternalClient.DisableBan(ctx, &ban.DisableBanRequest{
		Uids:            []int64{memberId},
		From:            "02ed58363420991001d16c60bd01d0a0", // aml 的
		Operator:        userEmail,
		BanTag:          banTags,
		RequestId:       requestId,
		Comment:         "aml-unban",
		BrokerId:        "0",
		IsNotification:  false,
		OperatorType:    0,
		IsLarkGroup:     false,
		IsBanSubaccount: false,
	})
	// 这块可以进行发送 lark 告警
	if err != nil {
		logc.Errorw(ctx, "DisableBan error", logc.Field("err", err))
		return
	}
	if resp.GetError() != nil {
		logc.Errorw(ctx, "unban by grpc",
			logc.Field("err", resp.GetError()),
		)
		return
	}
	if len(resp.GetFailedUids()) != 0 {
		logc.Errorw(ctx, "unban by grpc",
			logc.Field("failed Uids", resp.GetFailedUids),
		)
		return
	}
}
