package logic

import (
	"context"
	"database/sql"
	"encoding/json"
	"testing"
	"time"

	"aml-insight/consumer/internal/config"
	"aml-insight/consumer/internal/svc"
	"aml-insight/internal/model"

	"code.bydev.io/frameworks/byone/kafka"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockAmlCaseModel is a mock implementation of AmlCaseModel
type MockAmlCaseModel struct {
	mock.Mock
}

func (m *MockAmlCaseModel) Insert(ctx context.Context, data *model.AmlCase) (sql.Result, error) {
	args := m.Called(ctx, data)
	return args.Get(0).(sql.Result), args.Error(1)
}

func (m *MockAmlCaseModel) FindOne(ctx context.Context, id uint64) (*model.AmlCase, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.AmlCase), args.Error(1)
}

func (m *MockAmlCaseModel) FindOneByRequestId(ctx context.Context, requestId string) (*model.AmlCase, error) {
	args := m.Called(ctx, requestId)
	return args.Get(0).(*model.AmlCase), args.Error(1)
}

func (m *MockAmlCaseModel) Update(ctx context.Context, newData *model.AmlCase) error {
	args := m.Called(ctx, newData)
	return args.Error(0)
}

func (m *MockAmlCaseModel) FindListByMemberIDAndActionType(ctx context.Context, memberId int64, actionType int, limit, page int) ([]*model.AmlCase, error) {
	args := m.Called(ctx, memberId, actionType, limit, page)
	return args.Get(0).([]*model.AmlCase), args.Error(1)
}

func (m *MockAmlCaseModel) FindOneByMemberIDAndRequestID(ctx context.Context, memberID int64, requestID string) (*model.AmlCase, error) {
	args := m.Called(ctx, memberID, requestID)
	return args.Get(0).(*model.AmlCase), args.Error(1)
}

func (m *MockAmlCaseModel) FindListByMemberID(ctx context.Context, memberId int64, page, limit int) ([]*model.AmlTransaction, int64, error) {
	args := m.Called(ctx, memberId, page, limit)
	return args.Get(0).([]*model.AmlTransaction), args.Get(1).(int64), args.Error(2)
}

func (m *MockAmlCaseModel) FindFirstStrByMemberID(ctx context.Context, memberId int64) (*model.AmlTransaction, error) {
	args := m.Called(ctx, memberId)
	return args.Get(0).(*model.AmlTransaction), args.Error(1)
}

func (m *MockAmlCaseModel) FindAllByMemberID(ctx context.Context, memberId int64) ([]*model.AmlTransaction, error) {
	args := m.Called(ctx, memberId)
	return args.Get(0).([]*model.AmlTransaction), args.Error(1)
}

func (m *MockAmlCaseModel) FindDepositAddressByMemberID(ctx context.Context, memberId int64) ([]*model.MemberDepositAddress, error) {
	args := m.Called(ctx, memberId)
	return args.Get(0).([]*model.MemberDepositAddress), args.Error(1)
}

func (m *MockAmlCaseModel) UpdateTxHash(ctx context.Context, requestID string, txHash string) error {
	args := m.Called(ctx, requestID, txHash)
	return args.Error(0)
}

func (m *MockAmlCaseModel) InsertWithTime(ctx context.Context, data *model.AmlCase) (sql.Result, error) {
	args := m.Called(ctx, data)
	return args.Get(0).(sql.Result), args.Error(1)
}

// MockKafkaProducer is a mock implementation of kafka.Producer
type MockKafkaProducer struct {
	mock.Mock
}

func (m *MockKafkaProducer) Send(ctx context.Context, messages ...*kafka.Message) error {
	args := m.Called(ctx, messages)
	return args.Error(0)
}

func (m *MockKafkaProducer) SendDelay(ctx context.Context, delay int64, messages ...*kafka.Message) error {
	args := m.Called(ctx, delay, messages)
	return args.Error(0)
}

func (m *MockKafkaProducer) Close() error {
	args := m.Called()
	return args.Error(0)
}

// MockSqlResult is a mock implementation of sql.Result
type MockSqlResult struct {
	insertId int64
	affected int64
}

func (m MockSqlResult) LastInsertId() (int64, error) {
	return m.insertId, nil
}

func (m MockSqlResult) RowsAffected() (int64, error) {
	return m.affected, nil
}

func TestBackfillAmlCaseLogic_Consume(t *testing.T) {
	t.Run("success_insert_aml_case", func(t *testing.T) {
		// Setup
		mockAmlCaseModel := new(MockAmlCaseModel)
		svcCtx := &svc.ServiceContext{
			AmlCaseModel: mockAmlCaseModel,
		}
		logic := NewBackfillAmlCaseLogic(svcCtx)

		// Create test data
		amlCase := &model.AmlCase{
			MemberId:        12345,
			RequestId:       "REQ123",
			Chain:           "BTC",
			Coin:            "BTC",
			FromAddress:     "1from",
			ToAddress:       "1to",
			Amount:          decimal.NewFromFloat(0.05),
			ActionType:      model.TYPE_DEPOSIT,
			Status:          0,
			ExternalStatus:  0,
			ExternalProcess: 0,
			CreatedAt:       time.Now(),
		}
		amlCaseData, _ := json.Marshal(amlCase)

		message := &kafka.Message{Value: amlCaseData}

		// Set expectations - only expect Insert call
		mockResult := MockSqlResult{insertId: 1, affected: 1}
		mockAmlCaseModel.On("InsertWithTime", mock.Anything, mock.MatchedBy(func(data *model.AmlCase) bool {
			return data.MemberId == 12345 && data.RequestId == "REQ123" && data.Status == 7
		})).Return(mockResult, nil)

		// Execute
		err := logic.Consume(context.Background(), message)

		// Assert
		assert.NoError(t, err)
		mockAmlCaseModel.AssertExpectations(t)
	})

	t.Run("success_insert_with_default_values", func(t *testing.T) {
		// Setup
		mockAmlCaseModel := new(MockAmlCaseModel)
		svcCtx := &svc.ServiceContext{
			AmlCaseModel: mockAmlCaseModel,
		}
		logic := NewBackfillAmlCaseLogic(svcCtx)

		// Create test data without RequestId to test default value setting
		amlCase := &model.AmlCase{
			MemberId:   12345,
			TxHash:     "0xhash123",
			ActionType: model.TYPE_WITHDRAWAL,
		}
		amlCaseData, _ := json.Marshal(amlCase)

		message := &kafka.Message{Value: amlCaseData}

		// Set expectations - expect Insert with default values set
		mockResult := MockSqlResult{insertId: 1, affected: 1}
		mockAmlCaseModel.On("InsertWithTime", mock.Anything, mock.MatchedBy(func(data *model.AmlCase) bool {
			return data.MemberId == 12345 &&
				data.RequestId != "" && // RequestId should be generated
				data.Status == 7 &&
				data.ExternalStatus == 3 &&
				data.ExternalProcess == 1
		})).Return(mockResult, nil)

		// Execute
		err := logic.Consume(context.Background(), message)

		// Assert
		assert.NoError(t, err)
		mockAmlCaseModel.AssertExpectations(t)
	})

	t.Run("error_nil_message", func(t *testing.T) {
		// Setup
		svcCtx := &svc.ServiceContext{}
		logic := NewBackfillAmlCaseLogic(svcCtx)

		// Execute
		err := logic.Consume(context.Background(), nil)

		// Assert
		assert.NoError(t, err)
	})

	t.Run("error_empty_message", func(t *testing.T) {
		// Setup
		svcCtx := &svc.ServiceContext{}
		logic := NewBackfillAmlCaseLogic(svcCtx)

		message := &kafka.Message{Value: []byte{}}

		// Execute
		err := logic.Consume(context.Background(), message)

		// Assert
		assert.NoError(t, err)
	})

	t.Run("error_invalid_json", func(t *testing.T) {
		// Setup
		svcCtx := &svc.ServiceContext{}
		logic := NewBackfillAmlCaseLogic(svcCtx)

		message := &kafka.Message{Value: []byte("invalid json")}

		// Execute
		err := logic.Consume(context.Background(), message)

		// Assert
		assert.Error(t, err)
	})
}

func TestSetDefaultIfNeeded(t *testing.T) {
	t.Run("set_all_defaults", func(t *testing.T) {
		// Setup
		amlCase := &model.AmlCase{
			RequestId:       "",
			Status:          0,
			ExternalStatus:  0,
			ExternalProcess: 0,
		}

		// Execute
		setDefaultIfNeeded(amlCase)

		// Assert
		assert.NotEmpty(t, amlCase.RequestId)
		assert.Equal(t, int32(7), amlCase.Status)
		assert.Equal(t, int32(3), amlCase.ExternalStatus)
		assert.Equal(t, int32(1), amlCase.ExternalProcess)
	})

	t.Run("preserve_existing_values", func(t *testing.T) {
		// Setup
		amlCase := &model.AmlCase{
			RequestId:       "EXISTING_REQ",
			Status:          5,
			ExternalStatus:  2,
			ExternalProcess: 4,
		}

		// Execute
		setDefaultIfNeeded(amlCase)

		// Assert - values should remain unchanged
		assert.Equal(t, "EXISTING_REQ", amlCase.RequestId)
		assert.Equal(t, int32(5), amlCase.Status)
		assert.Equal(t, int32(2), amlCase.ExternalStatus)
		assert.Equal(t, int32(4), amlCase.ExternalProcess)
	})

	t.Run("partial_defaults", func(t *testing.T) {
		// Setup
		amlCase := &model.AmlCase{
			RequestId:       "EXISTING_REQ",
			Status:          0, // Should be set to default
			ExternalStatus:  2, // Should remain unchanged
			ExternalProcess: 0, // Should be set to default
		}

		// Execute
		setDefaultIfNeeded(amlCase)

		// Assert
		assert.Equal(t, "EXISTING_REQ", amlCase.RequestId)
		assert.Equal(t, int32(7), amlCase.Status)
		assert.Equal(t, int32(2), amlCase.ExternalStatus)
		assert.Equal(t, int32(1), amlCase.ExternalProcess)
	})
}

func TestBackfillAmlCaseLogic_RateLimiter_Initialization(t *testing.T) {
	tests := []struct {
		name        string
		config      config.BackfillAmlCaseConfig
		expectLimit bool
		description string
	}{
		{
			name: "enabled_with_valid_qps",
			config: config.BackfillAmlCaseConfig{
				RateLimit: config.RateLimitConfig{
					Enabled: true,
					QPS:     10.0,
					Burst:   20,
				},
			},
			expectLimit: true,
			description: "Should create rate limiter when enabled with valid QPS",
		},
		{
			name: "disabled",
			config: config.BackfillAmlCaseConfig{
				RateLimit: config.RateLimitConfig{
					Enabled: false,
					QPS:     10.0,
					Burst:   20,
				},
			},
			expectLimit: false,
			description: "Should not create rate limiter when disabled",
		},
		{
			name: "zero_qps",
			config: config.BackfillAmlCaseConfig{
				RateLimit: config.RateLimitConfig{
					Enabled: true,
					QPS:     0,
					Burst:   20,
				},
			},
			expectLimit: false,
			description: "Should not create rate limiter when QPS is zero",
		},
		{
			name: "default_burst",
			config: config.BackfillAmlCaseConfig{
				RateLimit: config.RateLimitConfig{
					Enabled: true,
					QPS:     5.0,
					Burst:   0, // 应该使用默认值
				},
			},
			expectLimit: true,
			description: "Should use default burst when burst is zero",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svcCtx := &svc.ServiceContext{
				Config: config.Config{
					BackfillAmlCase: tt.config,
				},
			}

			logic := NewBackfillAmlCaseLogic(svcCtx)

			if tt.expectLimit {
				assert.NotNil(t, logic.rateLimiter, tt.description)
			} else {
				assert.Nil(t, logic.rateLimiter, tt.description)
			}
		})
	}
}

func TestBackfillAmlCaseLogic_WaitForRateLimit(t *testing.T) {
	tests := []struct {
		name         string
		config       config.RateLimitConfig
		requestCount int
		expectWait   bool
		description  string
	}{
		{
			name: "no_rate_limiter",
			config: config.RateLimitConfig{
				Enabled: false,
			},
			requestCount: 5,
			expectWait:   false,
			description:  "Should not rate limit when limiter is disabled",
		},
		{
			name: "within_rate_limit",
			config: config.RateLimitConfig{
				Enabled: true,
				QPS:     10.0,
				Burst:   10,
			},
			requestCount: 5,
			expectWait:   false,
			description:  "Should not wait when requests are within rate limit",
		},
		{
			name: "exceed_burst_with_adaptive_wait",
			config: config.RateLimitConfig{
				Enabled: true,
				QPS:     2.0, // 2 requests per second, so adaptive wait = 500ms
				Burst:   2,   // burst of 2
			},
			requestCount: 5,
			expectWait:   true,
			description:  "Should use adaptive wait when exceeding burst capacity",
		},
		{
			name: "low_qps_adaptive_wait",
			config: config.RateLimitConfig{
				Enabled: true,
				QPS:     1.0, // 1 request per second, so adaptive wait = 1000ms
				Burst:   1,   // Small burst
			},
			requestCount: 3,
			expectWait:   true,
			description:  "Should use longer adaptive wait for low QPS",
		},
		{
			name: "context_cancellation",
			config: config.RateLimitConfig{
				Enabled: true,
				QPS:     0.5, // Very low QPS to ensure wait
				Burst:   1,
			},
			requestCount: 2,
			expectWait:   false, // Context will be cancelled during wait
			description:  "Should handle context cancellation during adaptive wait",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svcCtx := &svc.ServiceContext{
				Config: config.Config{
					BackfillAmlCase: config.BackfillAmlCaseConfig{
						RateLimit: tt.config,
					},
				},
			}

			logic := NewBackfillAmlCaseLogic(svcCtx)
			ctx := context.Background()

			// For context cancellation test, use a cancelled context
			if tt.name == "context_cancellation" {
				cancelCtx, cancel := context.WithCancel(ctx)
				cancel() // Immediately cancel context
				ctx = cancelCtx
			}

			start := time.Now()

			// Send multiple requests
			for i := 0; i < tt.requestCount; i++ {
				logic.waitForRateLimit(ctx)
			}

			duration := time.Since(start)

			if tt.expectWait {
				// Calculate expected minimum wait time based on adaptive wait formula
				expectedWaitPerRequest := time.Duration(1000/tt.config.QPS) * time.Millisecond
				// Expect at least some waiting time (not exactly, due to burst and timing variations)
				minExpectedDuration := expectedWaitPerRequest / 4 // Allow for some variance
				assert.True(t, duration >= minExpectedDuration,
					"Expected some waiting time. Duration: %v, Expected min: %v", duration, minExpectedDuration)
			} else {
				// For no-wait cases, duration should be relatively short
				maxExpectedDuration := 100 * time.Millisecond
				if tt.name == "context_cancellation" {
					maxExpectedDuration = 50 * time.Millisecond // Should return quickly on cancelled context
				}
				assert.True(t, duration <= maxExpectedDuration,
					"Expected minimal waiting time. Duration: %v, Expected max: %v", duration, maxExpectedDuration)
			}

			t.Logf("Test %s completed in %v", tt.name, duration)
		})
	}
}

func TestBackfillAmlCaseLogic_Consume_WithRateLimit(t *testing.T) {
	tests := []struct {
		name        string
		config      config.RateLimitConfig
		description string
	}{
		{
			name: "consume_with_rate_limit_disabled",
			config: config.RateLimitConfig{
				Enabled: false,
			},
			description: "Should consume normally when rate limit is disabled",
		},
		{
			name: "consume_with_rate_limit_enabled",
			config: config.RateLimitConfig{
				Enabled: true,
				QPS:     100.0,
				Burst:   10,
			},
			description: "Should consume normally when within rate limit",
		},
		{
			name: "consume_with_low_qps_rate_limit",
			config: config.RateLimitConfig{
				Enabled: true,
				QPS:     2.0, // Low QPS will trigger adaptive wait
				Burst:   1,
			},
			description: "Should handle adaptive wait with low QPS rate limit",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svcCtx := &svc.ServiceContext{
				Config: config.Config{
					BackfillAmlCase: config.BackfillAmlCaseConfig{
						RateLimit: tt.config,
					},
				},
			}

			logic := NewBackfillAmlCaseLogic(svcCtx)
			ctx := context.Background()

			// Verify rate limiter configuration
			if tt.config.Enabled {
				assert.NotNil(t, logic.rateLimiter, "Rate limiter should be initialized when enabled")
			} else {
				assert.Nil(t, logic.rateLimiter, "Rate limiter should be nil when disabled")
			}

			// Test waitForRateLimit function directly (no error expected)
			start := time.Now()
			logic.waitForRateLimit(ctx)
			duration := time.Since(start)

			// Log timing information for debugging
			t.Logf("Rate limiter test passed for config: %+v, Duration: %v", tt.config, duration)

			// Verify adaptive wait behavior for low QPS
			if tt.config.Enabled && tt.config.QPS < 5.0 {
				// For low QPS, multiple calls should demonstrate adaptive wait
				totalStart := time.Now()
				for i := 0; i < 3; i++ {
					logic.waitForRateLimit(ctx)
				}
				totalDuration := time.Since(totalStart)

				// Should take some time due to adaptive wait after burst is exceeded
				expectedMinDuration := time.Duration(1000/tt.config.QPS) * time.Millisecond / 2
				t.Logf("Multiple calls took %v, expected min %v", totalDuration, expectedMinDuration)
			}
		})
	}
}

func TestBackfillAmlCaseLogic_Consume_EmptyMessage(t *testing.T) {
	svcCtx := &svc.ServiceContext{
		Config: config.Config{
			BackfillAmlCase: config.BackfillAmlCaseConfig{
				RateLimit: config.RateLimitConfig{
					Enabled: true,
					QPS:     10.0,
					Burst:   5,
				},
			},
		},
	}

	logic := NewBackfillAmlCaseLogic(svcCtx)
	ctx := context.Background()

	tests := []struct {
		name    string
		message *kafka.Message
	}{
		{
			name:    "nil_message",
			message: nil,
		},
		{
			name: "empty_value",
			message: &kafka.Message{
				Value: []byte{},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := logic.Consume(ctx, tt.message)
			// 空消息应该正常处理，不应该调用限流器
			assert.NoError(t, err, "Empty messages should be handled gracefully")
		})
	}
}

func TestBackfillAmlCaseLogic_RateLimit_Concurrency(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping concurrency test in short mode")
	}

	svcCtx := &svc.ServiceContext{
		Config: config.Config{
			BackfillAmlCase: config.BackfillAmlCaseConfig{
				RateLimit: config.RateLimitConfig{
					Enabled: true,
					QPS:     5.0, // 5 requests per second
					Burst:   2,   // burst of 2
				},
			},
		},
	}

	logic := NewBackfillAmlCaseLogic(svcCtx)
	ctx := context.Background()

	// 并发发送请求
	const goroutines = 10
	const requestsPerGoroutine = 3

	results := make(chan time.Duration, goroutines*requestsPerGoroutine)

	start := time.Now()
	for i := 0; i < goroutines; i++ {
		go func() {
			for j := 0; j < requestsPerGoroutine; j++ {
				requestStart := time.Now()
				logic.waitForRateLimit(ctx)
				requestDuration := time.Since(requestStart)
				results <- requestDuration
			}
		}()
	}

	// 收集结果
	var durations []time.Duration
	for i := 0; i < goroutines*requestsPerGoroutine; i++ {
		duration := <-results
		durations = append(durations, duration)
	}

	totalDuration := time.Since(start)

	t.Logf("Concurrency test completed in %v", totalDuration)
	t.Logf("Individual request durations: %v", durations)

	// 验证rate limiter产生了适当的限流效果
	// 由于adaptive wait机制，应该有一些请求需要等待
	waitingRequests := 0
	for _, d := range durations {
		if d > 10*time.Millisecond { // 超过10ms认为是等待了
			waitingRequests++
		}
	}

	t.Logf("Requests that waited: %d out of %d", waitingRequests, len(durations))

	// 在高并发情况下，应该有一些请求需要等待
	assert.True(t, waitingRequests > 0,
		"Expected some requests to wait due to rate limiting, but none did")

	// 总体执行时间应该比无限流情况要长
	assert.True(t, totalDuration > 100*time.Millisecond,
		"Rate limiter should cause some delays. Total duration: %v", totalDuration)
}
