package logic

import (
	"context"
	"encoding/json"

	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/kafka"

	"aml-insight/consumer/internal/logic/rnlogic"
	"aml-insight/consumer/internal/svc"
)

type (
	AmlFiatTransactionLogic struct {
		svcCtx *svc.ServiceContext
		*rnlogic.Logic
	}
)

func NewAmlFiatTransactionLogic(svcCtx *svc.ServiceContext) *AmlFiatTransactionLogic {
	return &AmlFiatTransactionLogic{
		svcCtx: svcCtx,
		Logic:  rnlogic.NewRNLogic(svcCtx),
	}
}

func (l *AmlFiatTransactionLogic) Consume(ctx context.Context, message *kafka.Message) error {
	if message == nil || len(message.Value) == 0 {
		logc.Errorw(ctx, "Consume message error", logc.Field("msg", message))
		return nil
	}
	logc.Infow(ctx, "AmlFiatTransactionLogic Message value", logc.Field("message.Value", string(message.Value)))

	var body *rnlogic.AMLFiatTransactionBody
	if err := json.Unmarshal(message.Value, &body); err != nil {
		logc.Errorw(ctx, "Consume Unmarshal error", logc.Field("message.Value", string(message.Value)), logc.Field("error", err))
		return err
	}
	err := l.ProcessAMLFiatTransaction(ctx, body)
	if err != nil {
		logc.Error(ctx, "ProcessAMLFiatTransaction failed", logc.Field("err", err))
	}
	return nil
}
