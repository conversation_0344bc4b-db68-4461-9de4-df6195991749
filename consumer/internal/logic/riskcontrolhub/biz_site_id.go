package riskcontrolhub

import (
	"context"

	"code.bydev.io/frameworks/byone/core/contextutils"
	"code.bydev.io/frameworks/byone/core/logc"

	"aml-insight/consumer/internal/config"
	"aml-insight/consumer/internal/svc"
	"aml-insight/internal/golbal"
	"aml-insight/internal/logic/lib"
)

func GetCardBizSiteId(ctx context.Context, svcCtx *svc.ServiceContext, CardProductCode string) string {
	resp, err := lib.GetBizConfigWithCache[config.AMLInsightConsumerBizConfig](ctx, svcCtx.BizConfigClient, golbal.AmlInsightConsumerBizConfigKey)
	if err != nil {
		logc.Errorw(ctx, "get biz conf error", logc.Field("err", err))
		return ""
	}
	siteId := contextutils.GetReferSiteId(ctx)
	siteConfig, ok := resp.BizSiteIDMap.BybitCard[siteId]
	if !ok {
		logc.Warnw(ctx, "get biz site config failed", logc.Field("siteId", siteId))
		return ""
	}
	bizSiteId, ok := siteConfig[CardProductCode]
	if !ok {
		logc.Warnw(ctx, "get biz site failed", logc.Field("cardProductCode", CardProductCode))
		return ""
	}
	return bizSiteId
}
