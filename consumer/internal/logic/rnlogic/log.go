package rnlogic

import (
	"context"

	"aml-insight/internal/model"
	"aml-insight/internal/pkg/rn"
)

func (l *Logic) saveRnRequestLog(ctx context.Context, requestId, req, method string,
	raw *rn.RawResponse, err error) error {
	var resp string
	if err != nil {
		resp = err.Error()
	} else {
		resp = string(raw.Body())
	}
	m := &model.RnLog{
		RequestId: requestId,
		Method:    method,
		Req:       model.StringToNullString(req),
		Resp:      model.StringToNullString(resp),
		Status:    int32(raw.StatusCode()),
		Duration:  raw.Time().Milliseconds(),
	}
	_, e := l.svcCtx.RNLogModel.Insert(ctx, m)
	return e
}
