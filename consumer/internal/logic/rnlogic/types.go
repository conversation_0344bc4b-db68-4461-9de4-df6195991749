package rnlogic

import (
	"aml-insight/internal/model"
)

var (
	// onlychainChannel https://uponly.larksuite.com/wiki/ZtG6wCDk5i6TeTkaLDbuxkrbstg
	// change at 2025-07-29 remove "yellowcard", "inswitch", "facilitapay", "guavapay", "truelayer"
	onlychainChannel = []string{"worldpay", "mercuryo",
		"moorwand", "easyeuro", "zen", "nuvei", "circle", "bcb", "ovex", "actyve", "nuvei-sepa"}
	kzChannel = []string{"mercuryo-highrisk", "bcc"}
	trChannel = []string{"narkasa", "birpay"}

	euChannel = []string{"zen", "worldpay", "nuvei", "easyeuro", "clearbank"}

	applicationCountryMap = map[string]string{
		model.TransactionCategoryOnlychain: "LTU",
		model.TransactionCategoryKZ:        "KAZ",
		model.TransactionCategoryTR:        "TUR",
		model.TransactionCategoryCardEEA:   "LTU",
		model.TransactionCategoryCardAU:    "AUS",
		model.TransactionCategoryCardAR:    "ARG",
		model.TransactionCategoryCardBR:    "BRA",
		model.TransactionCategoryCardKZ:    "KAZ",
		model.TransactionCategoryCardHKG:   "HKG",
		model.TransactionCategoryCardGEO:   "GEO",
		//model.TransactionCategoryCardMEX:   "MEX",

		model.TransactionCategoryFiatEU: "LTU",
		model.TransactionCategoryCardEU: "LTU",

		model.TransactionCategoryFiatOther: "", // FiatOther
		model.TransactionCategoryCardOther: "", // CardOther
	}

	// https://uponly.larksuite.com/wiki/U7EMw0wRrijq4ykSxH2uCIwcsBd
	CardProductIDCardMapping = map[string]string{
		"0": model.TransactionCategoryCardEEA, // Bybit 和 EU 的 EEA Card Product ID 都是 0
		"1": model.TransactionCategoryCardAU,
		"2": model.TransactionCategoryCardAR,
		"3": model.TransactionCategoryCardBR,
		"4": model.TransactionCategoryCardKZ,
		"5": model.TransactionCategoryCardHKG,
		"6": model.TransactionCategoryCardGEO,
		//"7": model.TransactionCategoryCardMEX,
	}

	// highRiskContry onlychain high risk country
	highRiskCountry = []string{"AUT", "BEL", "BGR", "HRV", "CYP", "CZE", "DNK", "EST", "FIN",
		"FRA", "DEU", "GRC", "HUN", "IRL", "ITA", "LVA", "LTU", "LUX", "MLT", "NLD", "POL",
		"PRT", "ROU", "SVK", "SVN", "ESP", "SWE", "ISL", "LIE", "NOR"}
)

func isInArray(s string, arr []string) bool {
	for _, v := range arr {
		if v == s {
			return true
		}
	}
	return false
}

func GetCardTransactionCategory(cardProductId string) string {
	if category, ok := CardProductIDCardMapping[cardProductId]; ok {
		return category
	}
	return model.TransactionCategoryCardOther
}
