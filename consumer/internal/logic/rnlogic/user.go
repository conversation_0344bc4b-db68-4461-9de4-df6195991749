package rnlogic

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"code.bydev.io/frameworks/byone/core/logc"
	platformkyc "git.bybit.com/svc/stub/pkg/pb/api/kyc"
	"git.bybit.com/svc/stub/pkg/pb/enums/kyc"
	"github.com/pkg/errors"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"aml-insight/internal/model"
	"aml-insight/internal/pkg/rn"
)

// findOrCreateUserId 通过 fuid 查找或者创建用户，同时我们需要在不同的 orgID 都需要创建一次。
// 这里我们没有考虑并发的问题。
func (l *Logic) findOrCreateUserId(ctx context.Context, body *AMLFiatTransactionBody, trans *model.FiatAmlTransaction) (string, error) {
	org := l.selectOrg(ctx, trans)

	u, err := l.svcCtx.RNUserModel.FindOneByUserId(ctx, uint64(body.GeneralRequest.Fuid))
	switch {
	case err == nil:
		// 未来可以添加 update 更新用户的逻辑
		key := gjson.Escape(org.Username)
		if !gjson.Get(u.ExtInfo.String, key).Exists() { // 不存在的 org 的userID，在创建一次。
			reqPerson, err := l.sendCreateRNPersonRequest(ctx, body, org)
			if err != nil {
				logc.Error(ctx, "sendCreateRNPersonRequest failed", logc.Field("err", err))
				return fmt.Sprintf("AC%d", body.GeneralRequest.Fuid), nil
			}
			extInfo, err := sjson.Set(u.ExtInfo.String, key, reqPerson)
			if err != nil {
				logc.Error(ctx, "set extInfo failed", logc.Field("err", err))
				return fmt.Sprintf("AC%d", body.GeneralRequest.Fuid), nil
			}
			u.ExtInfo = model.StringToNullString(extInfo)
			err = l.svcCtx.RNUserModel.Update(ctx, u) // just insert, no need to check error
			if err != nil {
				// 插入db失败，不影响后续流程，等下次再 insert
				logc.Error(ctx, "insert user failed", logc.Field("err", err))
			}
		}

		return u.ClientReferenceId, nil
	case errors.Is(err, model.ErrNotFound):
		// go on to create user
	default:
		return "", errors.Wrap(err, "findOrCreateUserId")
	}

	reqPerson, err := l.sendCreateRNPersonRequest(ctx, body, org)
	if err != nil {
		return fmt.Sprintf("AC%d", body.GeneralRequest.Fuid), nil
	}
	m := map[string]any{
		org.Username: reqPerson,
	}
	extInfo, _ := json.Marshal(m)
	// save user
	uModel := &model.RnUser{
		UserId:            uint64(body.GeneralRequest.Fuid),
		ClientReferenceId: reqPerson.AccountReference,
		ExtInfo:           model.StringToNullString(string(extInfo)),
	}
	_, err = l.svcCtx.RNUserModel.Insert(ctx, uModel) // just insert, no need to check error
	if err != nil {
		// 插入db失败，不影响后续流程，等下次再 insert
		logc.Error(ctx, "insert user failed", logc.Field("err", err))
	}
	return reqPerson.AccountReference, nil
}

// sendCreateRNPersonRequest 将会 request 返回去的数据保存到数据库
func (l *Logic) sendCreateRNPersonRequest(ctx context.Context, body *AMLFiatTransactionBody, org *rn.OrgConfig) (*rn.Request, error) {
	p, err := l.createRNPerson(ctx, body.GeneralRequest.Fuid)
	if err != nil {
		return nil, errors.Wrap(err, "createRNPerson")
	}

	req := new(rn.Request)
	req.AccountStrategyId = org.StrategyId
	req.AccountReference = fmt.Sprintf("AC%d", body.GeneralRequest.Fuid)
	req.Application.CustomerReference = req.AccountReference
	req.Person = []*rn.Person{p}

	reqBody, _ := json.Marshal(req)

	_, raw, err := l.svcCtx.RNClient.SendReq(ctx, req, org)
	// we don't care resp, it saves in log.
	// to save log
	_ = l.saveRnRequestLog(ctx, body.GeneralRequest.RequestId, string(reqBody), model.RNLogMethodCreateUser,
		raw, err)
	if err != nil {
		return nil, errors.Wrap(err, "SendReq")
	}
	if strings.Contains(string(raw.Body()), `"Errors"`) {
		logc.Errorw(ctx, "sendCreateRNPersonRequest to Rn failed", logc.Field("err", raw.Body()))
	}
	return req, nil
}

func (l *Logic) createRNPerson(ctx context.Context, uid int64) (*rn.Person, error) {
	person := &rn.Person{
		ClientReference:    fmt.Sprintf("AC%d", uid),
		Gender:             "",
		DateOfBirth:        "",
		FirstName:          "dummy",
		LastName:           "dummy",
		Title:              "",
		IsPrimary:          true,
		Role:               "APPLICANT",
		ResidentialAddress: nil,
	}

	userResp, err := l.svcCtx.KycInternalClient.GetMemberKYC(ctx, &platformkyc.GetMemberKYCRequest{
		MemberId:                  uid,
		ObtainBaseInfo:            true,
		ObtainOnboardingAuthInfos: true,
		ObtainSupplementAuthInfos: false,
		ObtainCustodyInfo:         false,
		ObtainDuplicateFaceInfo:   false,
		ObtainAuthWorkflowInfo:    false,
		ObtainVerificationProcess: false,
	})
	if err != nil {
		return nil, errors.Wrap(err, "GetMemberKYC")
	}
	if userResp.GetLevel() == kyc.MemberKYCLevel_LEVEL_DEFAULT {
		logc.Warnw(ctx, "kyc info not found", logc.Field("uid", uid))
		// 还未完成 kyc 的我们直接使用默认值
		return person, nil
	}
	if len(userResp.GetOnboardingAuthInfos()) == 0 {
		logc.Warnw(ctx, "kyc info not found", logc.Field("uid", uid))
		// 还未完成 kyc 的我们直接使用默认值
		return person, nil
	}

	kycLevelMap := make(map[kyc.MemberKYCLevel]*platformkyc.GetMemberKYCResponse_KYCAuthInfo)
	for _, onboardingAuthInfo := range userResp.GetOnboardingAuthInfos() {
		kycLevelMap[onboardingAuthInfo.Level] = onboardingAuthInfo
	}

	kycInfo, ok := kycLevelMap[kyc.MemberKYCLevel_LEVEL_1]
	if !ok {
		logc.Warnw(ctx, "kyc level 1 info not found", logc.Field("uid", uid))
		// 没有 kyc level 1 信息的我们直接使用默认值
		return person, nil
	}

	switch kycInfo.GetGender() {
	case kyc.KYCGender_FeMale:
		person.Gender = "F"
	case kyc.KYCGender_Male:
		person.Gender = "M"
	default:
	}
	person.DateOfBirth = kycInfo.GetDob()
	person.FirstName = kycInfo.GetFirstnameEn()
	person.LastName = kycInfo.GetLastnameEn()

	if len(person.FirstName) == 0 && len(person.LastName) == 0 {
		kycLevel2Info, ok := kycLevelMap[kyc.MemberKYCLevel_LEVEL_2]
		if ok {
			person.FirstName = kycLevel2Info.GetFirstnameEn()
			person.LastName = kycLevel2Info.GetLastnameEn()
		}
	}
	if len(person.FirstName) == 0 && len(person.LastName) == 0 {
		person.FirstName = "dummy"
		person.LastName = "dummy"
	}

	address := &rn.PersonResidentialAddress{
		Country: userResp.GetBaseInfo().GetAddress().GetCountry(),
		City:    userResp.GetBaseInfo().GetAddress().GetCity(),
		Street:  userResp.GetBaseInfo().GetAddress().GetFullAddressEn(),
	}
	person.ResidentialAddress = []*rn.ResidentialAddress{
		{
			Standardised: address,
		},
	}

	return person, nil
}
