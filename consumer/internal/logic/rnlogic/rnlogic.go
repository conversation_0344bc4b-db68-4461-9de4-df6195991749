package rnlogic

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	risk_control_hubv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/risk_control_hub/v1"
	"code.bydev.io/frameworks/byone/core/contextutils"
	site "git.bybit.com/svc/stub/pkg/pb/api/sitv1"

	"aml-insight/consumer/internal/config"
	"aml-insight/consumer/internal/logic/card"
	"aml-insight/internal/golbal"
	"aml-insight/internal/logic/lib"

	bizconfigv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/bizconfig/v1"
	securityv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/security/v1"
	card_center "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/card-center"
	"code.bydev.io/frameworks/byone/core/logc"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/tidwall/gjson"

	"aml-insight/consumer/internal/svc"
	"aml-insight/internal/model"
	"aml-insight/internal/pkg/rn"
)

type Logic struct {
	svcCtx *svc.ServiceContext

	rnTriggerLogic        *RNTriggerLogic
	lastMerchCountryLogic *card.LastMerchCountryLogic
}

var (
	SceneMapping = map[string]string{
		"DepositAndBuyDepositTransfer": "AMLFiatDepositAndBuyDepositTransfer",
		"DepositAndBuyPostTransfer":    "AMLFiatDepositAndBuyPostTransfer",
		"RechargeTransfer":             "AMLFiatRechargeTransfer",
		"TradeTransfer":                "AMLFiatTradeTransfer",
		"WithdrawTransfer":             "AMLFiatWithdrawTransfer",
	}
)

type AMLFiatTransactionBody struct {
	GeneralRequest *securityv1.GeneralRequest `json:"general_request"`
	CardId         int64                      `json:"card_id"`
	CardNo         string                     `json:"card_no"`
	Amount         string                     `json:"amount"` //换算成usd金额
	Currency       string                     `json:"currency" res:"法币"`
	CurrencyAmount string                     `json:"currency_amount"`
	Crypto         string                     `json:"crypto" res:"数字币"`
	CryptoAmount   string                     `json:"crypto_amount"`
	RiskInfo       string                     `json:"risk_info"`     // json for RequestRiskInfo
	Scene          string                     `json:"scene"`         // 风控场景（绑卡前、绑卡后、支付前、支付时）
	Source         string                     `json:"source"`        // 调用风控的服务(交易核心，卡中心，充提中心)
	OrderId        string                     `json:"order_id"`      // 交易处理
	CardTokenId    string                     `json:"card_token_id"` // pci后的新cardid
	CurrencyFee    string                     `json:"currency_fee"`  // 法币手续费
	CryptoFee      string                     `json:"crypto_fee"`    // 数字币手续费
}

func NewRNLogic(svcCtx *svc.ServiceContext) *Logic {
	return &Logic{
		svcCtx:                svcCtx,
		rnTriggerLogic:        NewRNTriggerLogic(svcCtx.BizRedis),
		lastMerchCountryLogic: card.NewLastMerchCountryLogic(svcCtx.BizRedis),
	}
}

func (l *Logic) ProcessAMLFiatTransaction(ctx context.Context, body *AMLFiatTransactionBody) error {

	if !l.sceneEnabled(ctx, body.Scene) {
		logc.Warnw(ctx, "scene not enabled", logc.Field("scene", body.Scene), logc.Field("request_id", body.GeneralRequest.RequestId))
		return nil
	}
	// TODO card case set site id ?
	ctx, bizSiteID := l.fillAndCheckReferSiteID(ctx, body.GeneralRequest)
	if err := l.RiskControl(ctx, body, bizSiteID); err != nil {
		logc.Warnw(ctx, "RiskControl failed", logc.Field("err", err))
	}
	// filter
	// save transaction
	trans, cardProductCode, err := l.saveRnTransactionToDb(ctx, body)
	if err != nil {
		return errors.Wrap(err, "saveRnTransactionToDb")
	}
	if trans.TransactionType == "" {
		return errors.Wrap(err, "Not support scene: "+body.Scene)
	}
	if trans.TransactionCategory == model.TransactionCategoryCardOther ||
		trans.TransactionCategory == model.TransactionCategoryFiatOther {
		// 不支持的 case 先不请求 RN, 因为 RN 不支持 Other 的国家
		//return errors.Wrap(err, "Not support category: "+body.Scene)
		logc.Warnw(ctx, "Not support category", logc.Field("category", trans.TransactionCategory))
		return nil
	}
	if !l.grayCheckReportRN(ctx, trans) {
		// 不上报 RN
		logc.Infow(ctx, "gray check off, not report RN", logc.Field("requestID", trans.RequestId))
		return nil
	}

	// find or create user
	accountID, err := l.findOrCreateUserId(ctx, body, trans)
	if err != nil {
		return errors.Wrap(err, "findOrCreateUserId")
	}

	err = l.monitorTransaction(ctx, accountID, body, trans, cardProductCode)
	if err != nil {
		return errors.Wrap(err, "monitorTransaction")
	}

	return nil
}

func (l *Logic) grayCheckReportRN(ctx context.Context, trans *model.FiatAmlTransaction) bool {
	org := l.selectOrg(ctx, trans)
	resp, err := l.svcCtx.BizConfigClient.GetBizSwitchRes(ctx, &bizconfigv1.GetBizSwitchResRequest{
		UserInfo: &bizconfigv1.UserInfo{
			UserId: fmt.Sprintf("%d", trans.MemberId),
		},
		Scenario: fmt.Sprintf("aml-insight:rn_org:%d", org.StrategyId),
	})
	if err != nil {
		return false
	}
	return resp.GetResult().CanPass
}

func (l *Logic) sceneEnabled(ctx context.Context, scene string) bool {
	resp, err := lib.GetBizConfigWithCache[config.AMLInsightConsumerBizConfig](ctx,
		l.svcCtx.BizConfigClient, golbal.AmlInsightConsumerBizConfigKey)
	if err != nil {
		logc.Errorw(ctx, "get biz conf error", logc.Field("err", err))
		return false
	}
	if enabled, ok := resp.EnabledSceneMap[scene]; ok {
		return enabled
	}
	return false
}

// selectOrg select org by trans category
func (l *Logic) selectOrg(_ context.Context, trans *model.FiatAmlTransaction) *rn.OrgConfig {
	if contextutils.GetEnvSiteId() == golbal.SiteIdEU {
		switch trans.TransactionCategory {
		case model.TransactionCategoryFiatEU:
			return l.svcCtx.Config.RNConfig.FiatChannelOrgEU
		case model.TransactionCategoryCardEU:
			return l.svcCtx.Config.RNConfig.CardEEAOrgEU
		}
	}
	switch trans.TransactionCategory {
	case model.TransactionCategoryOnlychain,
		model.TransactionCategoryKZ,
		model.TransactionCategoryTR:
		return l.svcCtx.Config.RNConfig.FiatChannelOrg

	case model.TransactionCategoryCardEEA:
		return l.svcCtx.Config.RNConfig.CardEEAOrg

	case model.TransactionCategoryCardAU,
		model.TransactionCategoryCardAR,
		model.TransactionCategoryCardBR,
		model.TransactionCategoryCardKZ,
		model.TransactionCategoryCardHKG,
		model.TransactionCategoryCardGEO:
		//model.TransactionCategoryCardMEX:
		return l.svcCtx.Config.RNConfig.CardNonEEAOrg

	default:
		return l.svcCtx.Config.RNConfig.FiatChannelOrg
	}
}

// ----------------------------------------------------------------------------
// transaction to local db

// saveRnTransactionToDb
func (l *Logic) saveRnTransactionToDb(ctx context.Context, body *AMLFiatTransactionBody) (*model.FiatAmlTransaction, string, error) {
	extInfo, _ := json.Marshal(body)
	// save transaction
	trans := &model.FiatAmlTransaction{
		RequestId:              body.GeneralRequest.RequestId,
		MemberId:               uint64(body.GeneralRequest.Fuid),
		AtTime:                 time.Now(),
		TransactionType:        "",
		TransactionCategory:    "",
		FiatConvertedUsdAmount: decimal.Decimal{},
		FiatCurrencySymbol:     body.Currency,
		FiatCurrencyAmount:     decimal.Decimal{},
		DigitalCurrencySymbol:  body.Crypto,
		DigitalCurrencyAmount:  decimal.Decimal{},
		PaymentType:            "",
		ChannelType:            "",
		CardToken:              body.CardTokenId,
		OrderNo:                body.OrderId,
		ExtInfo:                model.StringToNullString(string(extInfo)),
		Decision:               "",
		RnResp:                 sql.NullString{},
		Status:                 model.TransactionStatusInit,
		RnCallback:             "",
		//CreatedAt:              time.Time{},
		//UpdatedAt:              time.Time{},
	}
	cardProductCode := ""

	if body.GeneralRequest.Timestamp != "" { // ms
		if millis, err := decimal.NewFromString(body.GeneralRequest.Timestamp); err != nil {
		} else {
			trans.AtTime = time.UnixMilli(millis.IntPart())
		}
	}
	switch body.Scene {
	case "RechargeTransfer", "DepositAndBuyDepositTransfer", "DepositAndBuyPostTransfer":
		trans.TransactionType = model.TransactionTypeDeposit
		trans.TransactionCategory = getTransactionCategory(body, "")
		trans.PaymentType = gjson.Get(body.RiskInfo, "payment_type").String()
		trans.ChannelType = gjson.Get(body.RiskInfo, "channel_type").String()

	case "TradeTransfer":
		trans.TransactionType = model.TransactionTypeTrade
		trans.TransactionCategory = getTransactionCategory(body, "")
		trans.PaymentType = gjson.Get(body.RiskInfo, "payment_type").String()
		trans.ChannelType = gjson.Get(body.RiskInfo, "channel_type").String()

	case "WithdrawTransfer":
		trans.TransactionType = model.TransactionTypeWithdraw
		trans.TransactionCategory = getTransactionCategory(body, "")
		trans.PaymentType = gjson.Get(body.RiskInfo, "payment_type").String()
		trans.ChannelType = gjson.Get(body.RiskInfo, "channel_type").String()

	case golbal.SceneByBitCardFinancial,
		golbal.SceneByBitCardAuth,
		golbal.SceneByBitCardAuthReversal,
		golbal.SceneByBitCardFinancialReversal,
		golbal.SceneByBitCardRefund,
		golbal.SceneByBitCardMoneySendAuth,
		golbal.SceneByBitCardMoneySendAuthReversal:

		cardTokenQueryReq := &card_center.GetCardRequest{}
		if len(body.CardTokenId) != 0 {
			cardTokenQueryReq.PciToken = body.CardTokenId
		} else {
			cartToken := gjson.Get(body.RiskInfo, "bybit_card_asynchronous.cardToken").String()
			if len(cartToken) != 0 {
				cardTokenQueryReq.Token = cartToken
			} else {
				return nil, cardProductCode, errors.New("empty cardToken or cardTokenId")
			}
		}
		cardInfo, err := l.svcCtx.CardCenterClient.GetCardInfo(ctx, cardTokenQueryReq)
		if err != nil {
			return nil, cardProductCode, errors.Wrap(err, "GetCardInfo")
		} else {
			trans.TransactionCategory = getTransactionCategory(body, cardInfo.GetCardInfo().GetProductInfo().GetCardProductCode())
			cardProductCode = cardInfo.GetCardInfo().GetProductInfo().GetCardProductCode()
		}

		trans.TransactionType = model.TransactionTypeCard
		trans.PaymentType = "CARD"

		/*
			ATM
			CRYPTOLOAD
			P2P
			ONLINEAUTHORISATION
			4个的组合 ATMCRYPTOLOAD
		*/
		if gjson.Get(body.RiskInfo, "bybit_card_asynchronous.processingCode").String() == "01" {
			trans.ChannelType = "ATM"
		} else {
			trans.ChannelType = "ONLINEAUTHORISATION" // TODO: 先默认 ONLINE AUTH 先留空
		}
	}
	logc.Debugw(ctx, "set transaction category done", logc.Field("body", body), logc.Field("trans", trans))
	if body.CurrencyAmount != "" {
		trans.FiatCurrencyAmount, _ = decimal.NewFromString(body.CurrencyAmount)
	}
	if body.CryptoAmount != "" {
		trans.DigitalCurrencyAmount, _ = decimal.NewFromString(body.CryptoAmount)
	}
	if body.Amount != "" {
		trans.FiatConvertedUsdAmount, _ = decimal.NewFromString(body.Amount)
	}

	r, err := l.svcCtx.FiatAmlTransactionModel.Insert(ctx, trans)
	if err != nil {
		// 插入失败的，尝试再次查询一下。我们将失败的 case 在重新请求一次
		if t, err := l.svcCtx.FiatAmlTransactionModel.FindOneByRequestId(ctx, trans.RequestId); err != nil {
			logc.Errorw(ctx, "FindOneByRequestId", logc.Field("err", err))
		} else {
			switch t.Status {
			case model.TransactionStatusInit, model.TransactionStatusPending: // 只有这2个状态我们需要重试
				return t, cardProductCode, nil
			}
		}

		return nil, cardProductCode, errors.Wrap(err, "Insert")
	}
	id, err := r.LastInsertId()
	if err != nil {
		return nil, cardProductCode, errors.Wrap(err, "LastInsertId")
	}
	trans.Id = uint64(id)
	return trans, cardProductCode, nil
}

func getTransactionCategory(body *AMLFiatTransactionBody, cardProductID string) string {
	env := contextutils.GetEnvSiteId()
	channel := gjson.Get(body.RiskInfo, "channel_type").String()
	switch body.Scene {
	case "RechargeTransfer", "DepositAndBuyDepositTransfer", "TradeTransfer", "WithdrawTransfer", "DepositAndBuyPostTransfer":
		if env == golbal.SiteIdEU {
			return model.TransactionCategoryFiatEU
		}
		switch {
		case isInArray(channel, onlychainChannel):
			return model.TransactionCategoryOnlychain

		case isInArray(channel, kzChannel):
			return model.TransactionCategoryKZ

		case isInArray(channel, trChannel):
			return model.TransactionCategoryTR

		default:
			return model.TransactionCategoryFiatOther
		}

	case golbal.SceneByBitCardFinancial,
		golbal.SceneByBitCardAuth,
		golbal.SceneByBitCardAuthReversal,
		golbal.SceneByBitCardFinancialReversal,
		golbal.SceneByBitCardRefund,
		golbal.SceneByBitCardMoneySendAuth,
		golbal.SceneByBitCardMoneySendAuthReversal:
		// EU 站的 EEA Card 和主站一致，因此无需更改
		return GetCardTransactionCategory(cardProductID)
	default:
		return model.TransactionCategoryFiatOther
	}
}

func (l *Logic) RiskControl(ctx context.Context, reqBody *AMLFiatTransactionBody, bizSiteID string) error {

	if reqBody == nil || reqBody.GeneralRequest == nil {
		logc.Warnw(ctx, "RiskControl reqBody is nil", logc.Field("reqBody", reqBody))
		return nil
	}
	if !l.grayRiskControl(ctx, reqBody.GeneralRequest.Fuid) {
		return nil
	}

	scene, ok := SceneMapping[reqBody.Scene]
	if !ok {
		logc.Warnw(ctx, "Scene not found in Mapping", logc.Field("scene", reqBody.Scene), logc.Field("", reqBody))
		return nil
	}

	reqBodyStr, err := json.Marshal(reqBody)
	if err != nil {
		return err
	}

	_, err = l.svcCtx.RiskControlHubAPIClient.RiskControl(ctx, &risk_control_hubv1.RiskControlRequest{
		Business:       "AML_INSIGHT",
		Scene:          scene,
		Uid:            reqBody.GeneralRequest.Fuid,
		Amount:         reqBody.Amount,
		Currency:       reqBody.Currency,
		CurrencyAmount: reqBody.CurrencyAmount,
		Crypto:         reqBody.Crypto,
		CryptoAmount:   reqBody.CryptoAmount,
		OrderId:        reqBody.OrderId,
		CardTokenId:    reqBody.CardTokenId,
		Data:           string(reqBodyStr),
		BizSiteId:      bizSiteID,
	})

	return err

}

func (l *Logic) grayRiskControl(ctx context.Context, uid int64) bool {
	scenario := fmt.Sprintf("aml-insight-consumer:fiat:risk")
	resp, err := l.svcCtx.BizConfigClient.GetBizSwitchRes(ctx, &bizconfigv1.GetBizSwitchResRequest{
		UserInfo: &bizconfigv1.UserInfo{
			UserId: fmt.Sprintf("%d", uid),
		},
		Scenario: scenario,
	})
	logc.Infow(ctx, "GetBizSwitchRes Done", logc.Field("resp", resp), logc.Field("uid", uid), logc.Field("scenario", scenario))
	if err != nil {
		logc.Errorw(ctx, "GetBizSwitchRes Error", logc.Field("err", err))
		return false
	}
	return resp.GetResult().CanPass
}

//AML Fiat 取 Biz Site ID 逻辑
//
//req.generalRequest.siteID 不为空，Biz Site ID = req.generalRequest.siteID
//req.generalRequest.siteID 为空，查询 user site 做兜底
//ctx  x-refer-site-id 校验
//	ctx  x-refer-site-id == Biz Site ID，结束
//	ctx  x-refer-site-id ！= Biz Site ID
//		ctx  x-refer-site-id 为空则覆盖为 Biz Site ID ，添加告警，结束
//		ctx  x-refer-site-id  不为空，添加告警，结束

func (l *Logic) fillAndCheckReferSiteID(ctx context.Context, generalRequest *securityv1.GeneralRequest) (context.Context, string) {
	if generalRequest == nil {
		logc.Errorw(ctx, "empty general request")
		return ctx, ""
	}
	var (
		bizSiteID   = ""
		referSiteID = contextutils.GetReferSiteId(ctx)
		uid         = generalRequest.GetFuid()
	)

	if len(generalRequest.SiteId) != 0 {
		bizSiteID = generalRequest.SiteId
	} else {
		userInfo, err := l.svcCtx.SiteServiceClient.GetUser(ctx, &site.GetUserRequest{UserId: uid})
		if err != nil {
			logc.Errorw(ctx, "get user info error", logc.Field("err", err), logc.Field("uid", uid))
			return ctx, ""
		} else {
			bizSiteID = userInfo.GetData().GetSiteId()
		}
	}

	if bizSiteID != referSiteID {
		logc.Warnw(ctx, "site id not match", logc.Field("uid", uid), logc.Field("biz_site_id", bizSiteID), logc.Field("refer_site_id", referSiteID))
	}
	if len(referSiteID) == 0 {
		ctx = contextutils.SetReferSiteId(ctx, bizSiteID)
	}
	return ctx, bizSiteID
}
