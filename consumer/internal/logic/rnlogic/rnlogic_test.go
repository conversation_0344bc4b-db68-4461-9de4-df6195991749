package rnlogic

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"testing"
	"time"

	risk_control_hubv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/risk_control_hub/v1"
	card_center "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/card-center"
	"code.bydev.io/frameworks/byone/core/proc"
	"code.bydev.io/frameworks/byone/core/stores/redis/redistest"
	"google.golang.org/grpc"

	"aml-insight/internal/golbal"

	amlv1 "code.bydev.io/cht/customer/kyc-stub.git/pkg/bybit/kyc/aml/v1"
	appealv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/appeal/v1"
	bizconfigv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/bizconfig/v1"
	securityv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/security/v1"
	"code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/user"
	platformkyc "git.bybit.com/svc/stub/pkg/pb/api/kyc"
	"git.bybit.com/svc/stub/pkg/pb/enums/kyc"
	"github.com/go-resty/resty/v2"
	"go.uber.org/mock/gomock"

	"aml-insight/consumer/internal/config"
	"aml-insight/consumer/internal/svc"
	mockmodel "aml-insight/internal/mock/model"
	mockrn "aml-insight/internal/mock/rn"
	mockrpc "aml-insight/internal/mock/rpc"
	"aml-insight/internal/model"
	"aml-insight/internal/pkg/rn"

	site "git.bybit.com/svc/stub/pkg/pb/api/sitv1"
	"github.com/stretchr/testify/mock"
)

type MockSiteServiceClient struct {
	mock.Mock
}

func (m *MockSiteServiceClient) GetUser(ctx context.Context, in *site.GetUserRequest, opts ...grpc.CallOption) (*site.GetUserResponse, error) {
	args := m.Called(ctx, in, opts)
	return args.Get(0).(*site.GetUserResponse), args.Error(1)
}

func TestLogic_ProcessAMLFiatTransaction(t *testing.T) {
	t.Run("normal", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		transactionModel := mockmodel.NewMockFiatAmlTransactionModel(ctrl)
		rnLogModel := mockmodel.NewMockRnLogModel(ctrl)
		rnUserModel := mockmodel.NewMockRnUserModel(ctrl)
		appealClient := appealv1.NewMockAppealAPIClient(ctrl)
		kycInternalClient := mockrpc.NewMockKycInternalClient(ctrl)
		amlClient := mockrpc.NewMockAmlAPIClient(ctrl)
		fiatUserClient := user.NewMockGrpcUserServiceClient(ctrl)
		rnClient := mockrn.NewMockClient(ctrl)
		bizClient := bizconfigv1.NewMockCustomerAPIClient(ctrl)
		riskControlHubAPIClient := risk_control_hubv1.NewMockRiskControlHubAPIClient(ctrl)
		siteClietMock := new(MockSiteServiceClient)
		l := NewRNLogic(&svc.ServiceContext{
			Config: config.Config{
				RNConfig: rn.Config{
					FiatChannelOrg: &rn.OrgConfig{
						Username: "username@bybit",
					},
				},
			},
			FiatAmlTransactionModel: transactionModel,
			RNLogModel:              rnLogModel,
			AppealClient:            appealClient,
			RNUserModel:             rnUserModel,
			RNClient:                rnClient,
			KycInternalClient:       kycInternalClient,
			AmlAPIClient:            amlClient,
			FiatUserClient:          fiatUserClient,
			BizConfigClient:         bizClient,
			RiskControlHubAPIClient: riskControlHubAPIClient,
			SiteServiceClient:       siteClietMock,
		})
		bizClient.EXPECT().GetBizConfig(gomock.Any(), gomock.Any()).Return(&bizconfigv1.GetBizConfigResponse{
			Result: []*bizconfigv1.GetBizConfigResponse_Result{{
				Scenario: golbal.AmlInsightConsumerBizConfigKey,
				Value: &bizconfigv1.GetBizConfigResponse_Result_StrValue{
					StrValue: `{"EnabledSceneMap": {"ByBitCardAuth": true,"ByBitCardFinancial": true,"ByBitCardFinancialReversal": true,"ByBitCardMoneySendAuth": true,"ByBitCardRefund": true,"DepositAndBuyPostTransfer": true,"RechargeTransfer": true,"TradeTransfer": true,"WithdrawTransfer": true } }`,
				},
			}},
		}, nil)
		siteClietMock.On("GetUser", mock.Anything, mock.Anything, mock.Anything).Return(&site.GetUserResponse{Data: &site.User{
			SiteId: "BYBIT",
		}}, nil)
		riskControlHubAPIClient.EXPECT().RiskControl(gomock.Any(), gomock.Any()).Return(&risk_control_hubv1.RiskControlResponse{
			Error:      nil,
			ResultCode: 0,
			RiskInfo:   "",
			RiskId:     "",
			RiskResults: map[string]*risk_control_hubv1.RiskControlResponse_RuleResult{
				"Usage_in_Prohibited_Country": {Result: "Match"},
			},
		}, nil)

		transactionModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(model.NewMockedResult(1, 1), nil).Times(1)
		rnUserModel.EXPECT().FindOneByUserId(gomock.Any(), gomock.Any()).Return(nil, sql.ErrNoRows).Times(1)
		kycInternalClient.EXPECT().GetMemberKYC(gomock.Any(), gomock.Any()).Return(&platformkyc.GetMemberKYCResponse{
			Level: kyc.MemberKYCLevel_LEVEL_1,
			OnboardingAuthInfos: []*platformkyc.GetMemberKYCResponse_KYCAuthInfo{
				{},
			},
		}, nil).Times(1)
		rnUserModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
		rnClient.EXPECT().SendReq(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, &rn.RawResponse{
			Request:     &resty.Request{},
			RawResponse: &http.Response{},
		}, nil).Times(2)
		rnLogModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)
		fiatUserClient.EXPECT().GetUserVipInfo(gomock.Any(), gomock.Any()).Return(&user.GetUserVipInfoResponse{
			VipType: "LABEL_TYPE_VIP",
		}, nil).Times(1)
		transactionModel.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		amlClient.EXPECT().QueryAMLResult(gomock.Any(), gomock.Any()).Return(&amlv1.QueryAMLResultResponse{
			QuestionnaireInfo: &amlv1.QuestionnaireInfo{
				IsFinish: true,
				RsaScore: "4",
			},
		}, nil).Times(1)
		fiatUserClient.EXPECT().GetRiskUserInfoByRealTime(gomock.Any(), gomock.Any()).Return(&user.RiskUserResponse{}, nil).Times(1)
		bizClient.EXPECT().GetBizSwitchRes(gomock.Any(), gomock.Any()).Return(&bizconfigv1.GetBizSwitchResResponse{
			Result: &bizconfigv1.GetBizSwitchResResponse_SwitchRes{
				CanPass: true,
			},
		}, nil).AnyTimes()

		err := l.ProcessAMLFiatTransaction(context.Background(), &AMLFiatTransactionBody{
			GeneralRequest: &securityv1.GeneralRequest{
				Timestamp: fmt.Sprintf("%d", time.Now().UnixMilli()),
				SiteId:    "BYBIT",
			},
			Scene:          "RechargeTransfer",
			CurrencyAmount: "100",
			CryptoAmount:   "0.01",
			Amount:         "100",
			RiskInfo:       `{"channel_type": "nuvei"}`,
		})
		if err != nil {
			t.Errorf("ProcessAMLFiatTransaction() error = %v", err)
			return
		}
	})

	t.Run("bybit card", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		transactionModel := mockmodel.NewMockFiatAmlTransactionModel(ctrl)
		rnLogModel := mockmodel.NewMockRnLogModel(ctrl)
		rnUserModel := mockmodel.NewMockRnUserModel(ctrl)
		appealClient := appealv1.NewMockAppealAPIClient(ctrl)
		kycInternalClient := mockrpc.NewMockKycInternalClient(ctrl)
		amlClient := mockrpc.NewMockAmlAPIClient(ctrl)
		fiatUserClient := user.NewMockGrpcUserServiceClient(ctrl)
		rnClient := mockrn.NewMockClient(ctrl)
		bizClient := bizconfigv1.NewMockCustomerAPIClient(ctrl)
		cardCenter := card_center.NewMockCardCenterServiceClient(ctrl)
		riskControlHubAPIClient := risk_control_hubv1.NewMockRiskControlHubAPIClient(ctrl)
		siteClietMock := new(MockSiteServiceClient)
		bizRedis := redistest.CreateRedis(t)
		l := NewRNLogic(&svc.ServiceContext{
			Config: config.Config{
				RNConfig: rn.Config{
					FiatChannelOrg: &rn.OrgConfig{
						Username: "username@bybit",
					},
					CardEEAOrg: &rn.OrgConfig{
						Username:   "username@bybit",
						StrategyId: 1867,
					},
				},
			},
			FiatAmlTransactionModel: transactionModel,
			RNLogModel:              rnLogModel,
			AppealClient:            appealClient,
			RNUserModel:             rnUserModel,
			RNClient:                rnClient,
			KycInternalClient:       kycInternalClient,
			AmlAPIClient:            amlClient,
			FiatUserClient:          fiatUserClient,
			BizConfigClient:         bizClient,
			CardCenterClient:        cardCenter,
			RiskControlHubAPIClient: riskControlHubAPIClient,
			BizRedis:                bizRedis,
			SiteServiceClient:       siteClietMock,
		})
		bizClient.EXPECT().GetBizConfig(gomock.Any(), gomock.Any()).Return(&bizconfigv1.GetBizConfigResponse{
			Result: []*bizconfigv1.GetBizConfigResponse_Result{{
				Scenario: golbal.AmlInsightConsumerBizConfigKey,
				Value: &bizconfigv1.GetBizConfigResponse_Result_StrValue{
					StrValue: `{"EnabledSceneMap": {"ByBitCardAuth": true,"ByBitCardFinancial": true,"ByBitCardFinancialReversal": true,"ByBitCardMoneySendAuth": true,"ByBitCardRefund": true,"DepositAndBuyPostTransfer": true,"RechargeTransfer": true,"TradeTransfer": true,"WithdrawTransfer": true } }`,
				},
			}},
		}, nil).AnyTimes()

		siteClietMock.On("GetUser", mock.Anything, mock.Anything, mock.Anything).Return(&site.GetUserResponse{Data: &site.User{
			SiteId: "BYBIT",
		}}, nil)
		//"cardInfo": {
		//	"cardId": "1889289430920417281",
		//		"userId": "1000272887",
		//		"cardType": 1,
		//		"maskedCardNumber": "520581******8779",
		//		"cardExpireDate": "01/28",
		//		"cardCurrency": "EUR",
		//		"status": 2,
		//		"abilities": 4,
		//		"subStatus": 200,
		//		"cardToken": "108287303",
		//		"pan6": "520581",
		//		"pan4": "8779",
		//		"pciToken": "MOCK1889289430920417280",
		//		"productInfo": {
		//		"cardProductCode": "0",
		//			"productId": "0",
		//			"showECMarkup": true
		//	}
		//}
		cardCenter.EXPECT().GetCardInfo(gomock.Any(), gomock.Any()).Return(&card_center.CardInfoResponse{
			Result: nil,
			CardInfo: &card_center.BasicCardInfo{
				CardId:           "1889289430920417281",
				UserId:           "1000272887",
				CardType:         1,
				MaskedCardNumber: "520581******8779",
				CardExpireDate:   "01/28",
				CardCurrency:     "EUR",
				Status:           2,
				Abilities:        4,
				SubStatus:        200,
				CardToken:        "108287303",
				Pan6:             "520581",
				Pan4:             "8779",
				PciToken:         "MOCK1889289430920417280",
				ProductInfo: &card_center.ProductInfo{
					CardProductCode: "0",
					ProductId:       "0",
					ShowECMarkup:    true,
				},
			},
		}, nil)

		riskControlHubAPIClient.EXPECT().RiskControl(gomock.Any(), gomock.Any()).Return(&risk_control_hubv1.RiskControlResponse{
			Error:      nil,
			ResultCode: 0,
			RiskInfo:   "",
			RiskId:     "",
			RiskResults: map[string]*risk_control_hubv1.RiskControlResponse_RuleResult{
				"Usage_in_Prohibited_Country": {Result: "Match"},
			},
		}, nil)

		transactionModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(model.NewMockedResult(1, 1), nil).Times(1)
		rnUserModel.EXPECT().FindOneByUserId(gomock.Any(), gomock.Any()).Return(nil, sql.ErrNoRows).Times(1)
		kycInternalClient.EXPECT().GetMemberKYC(gomock.Any(), gomock.Any()).Return(&platformkyc.GetMemberKYCResponse{
			Level: kyc.MemberKYCLevel_LEVEL_1,
			OnboardingAuthInfos: []*platformkyc.GetMemberKYCResponse_KYCAuthInfo{
				{},
			},
		}, nil).Times(1)
		rnUserModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
		rnClient.EXPECT().SendReq(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, &rn.RawResponse{
			Request:     &resty.Request{},
			RawResponse: &http.Response{},
		}, nil).Times(2)
		rnLogModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)
		fiatUserClient.EXPECT().GetUserVipInfo(gomock.Any(), gomock.Any()).Return(&user.GetUserVipInfoResponse{
			VipType: "LABEL_TYPE_VIP",
		}, nil).Times(1)
		transactionModel.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		//amlClient.EXPECT().QueryAMLResult(gomock.Any(), gomock.Any()).Return(&amlv1.QueryAMLResultResponse{
		//	QuestionnaireInfo: &amlv1.QuestionnaireInfo{
		//		IsFinish: true,
		//		RsaScore: "4",
		//	},
		//}, nil).Times(1)
		fiatUserClient.EXPECT().GetRiskUserInfoByRealTime(gomock.Any(), gomock.Any()).Return(&user.RiskUserResponse{}, nil).Times(1)
		bizClient.EXPECT().GetBizSwitchRes(gomock.Any(), gomock.Any()).Return(&bizconfigv1.GetBizSwitchResResponse{
			Result: &bizconfigv1.GetBizSwitchResResponse_SwitchRes{
				CanPass: true,
			},
		}, nil).Times(2)
		err := l.ProcessAMLFiatTransaction(context.Background(), &AMLFiatTransactionBody{
			GeneralRequest: &securityv1.GeneralRequest{
				Timestamp: fmt.Sprintf("%d", time.Now().UnixMilli()),
			},
			Scene:          "ByBitCardAuth",
			CurrencyAmount: "100",
			CryptoAmount:   "0.01",
			Amount:         "100",
			RiskInfo: `{
	"bybit_card_asynchronous": {
		"terminalId": "E6691438",
		"acquirerId": "",
		"cardholderPresence": "NOT_PRESENT_MOTO",
		"dateValidation": "",
		"dataPresence": "",
		"cvvPresence": "",
		"pinValidation": "NOT_PRESENT",
		"pinPresence": "NOT_PRESENT",
		"presence3ds": "FALSE",
		"authMethod3ds": "A",
		"cardholderVerification": "ELECTRNIC_SIGNATURE",
		"posEntryMode": "MANUAL_NO_TERMINAL",
		"transactionStatus": "1",
		"responseMessage": "",
		"pos": "MOTO",
		"cvvValidation": "",
		"merchantCountryCode": "4829",
		"merchId": "gps",
		"merchName": "GOOGLE *TEMPORARY HOLD",
		"cardToken": "108287303",
		"basicAmount": "30.000000000000000000",
		"basicAmountUsd": "32.84318673534546",
		"basicCurrency": "EUR",
		"billAmount": "30.000000000000000000",
		"billAmountUsd": "32.84318673534546",
		"billCurrency": "EUR",
		"status": "1",
		"merchCountry": "AUS",
		"txnType": "1",
		"transactionCurrency": "EUR",
		"transactionAmount": "30.000000000000000000",
		"transactionAmountUsd": "32.84318673534546",
		"cardSchema": "MASTER_CARD"
	}
} `,
		})
		if err != nil {
			t.Errorf("ProcessAMLFiatTransaction() error = %v", err)
			return
		}
	})

	t.Run("update user", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		transactionModel := mockmodel.NewMockFiatAmlTransactionModel(ctrl)
		rnLogModel := mockmodel.NewMockRnLogModel(ctrl)
		rnUserModel := mockmodel.NewMockRnUserModel(ctrl)
		appealClient := appealv1.NewMockAppealAPIClient(ctrl)
		kycInternalClient := mockrpc.NewMockKycInternalClient(ctrl)
		amlClient := mockrpc.NewMockAmlAPIClient(ctrl)
		fiatUserClient := user.NewMockGrpcUserServiceClient(ctrl)
		bizClient := bizconfigv1.NewMockCustomerAPIClient(ctrl)
		riskControlHubAPIClient := risk_control_hubv1.NewMockRiskControlHubAPIClient(ctrl)
		rnClient := mockrn.NewMockClient(ctrl)
		siteClietMock := new(MockSiteServiceClient)
		l := NewRNLogic(&svc.ServiceContext{
			Config: config.Config{
				RNConfig: rn.Config{
					FiatChannelOrg: &rn.OrgConfig{
						Username: "<EMAIL>",
					},
				},
			},
			FiatAmlTransactionModel: transactionModel,
			RNLogModel:              rnLogModel,
			AppealClient:            appealClient,
			RNUserModel:             rnUserModel,
			RNClient:                rnClient,
			KycInternalClient:       kycInternalClient,
			FiatUserClient:          fiatUserClient,
			AmlAPIClient:            amlClient,
			BizConfigClient:         bizClient,
			RiskControlHubAPIClient: riskControlHubAPIClient,
			SiteServiceClient:       siteClietMock,
		})
		bizClient.EXPECT().GetBizConfig(gomock.Any(), gomock.Any()).Return(&bizconfigv1.GetBizConfigResponse{
			Result: []*bizconfigv1.GetBizConfigResponse_Result{{
				Scenario: golbal.AmlInsightConsumerBizConfigKey,
				Value: &bizconfigv1.GetBizConfigResponse_Result_StrValue{
					StrValue: `{"EnabledSceneMap": {"ByBitCardAuth": true,"ByBitCardFinancial": true,"ByBitCardFinancialReversal": true,"ByBitCardMoneySendAuth": true,"ByBitCardRefund": true,"DepositAndBuyPostTransfer": true,"RechargeTransfer": true,"TradeTransfer": true,"WithdrawTransfer": true } }`,
				},
			}},
		}, nil).AnyTimes()
		siteClietMock.On("GetUser", mock.Anything, mock.Anything, mock.Anything).Return(&site.GetUserResponse{Data: &site.User{
			SiteId: "BYBIT",
		}}, nil)
		riskControlHubAPIClient.EXPECT().RiskControl(gomock.Any(), gomock.Any()).Return(&risk_control_hubv1.RiskControlResponse{
			Error:      nil,
			ResultCode: 0,
			RiskInfo:   "",
			RiskId:     "",
			RiskResults: map[string]*risk_control_hubv1.RiskControlResponse_RuleResult{
				"Usage_in_Prohibited_Country": {Result: "Match"},
			},
		}, nil)
		transactionModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(model.NewMockedResult(1, 1), nil).Times(1)
		rnUserModel.EXPECT().FindOneByUserId(gomock.Any(), gomock.Any()).Return(&model.RnUser{
			ExtInfo: model.StringToNullString("{}"),
		}, nil).Times(1)
		rnUserModel.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		kycInternalClient.EXPECT().GetMemberKYC(gomock.Any(), gomock.Any()).Return(&platformkyc.GetMemberKYCResponse{
			Level: kyc.MemberKYCLevel_LEVEL_1,
			OnboardingAuthInfos: []*platformkyc.GetMemberKYCResponse_KYCAuthInfo{
				{},
			},
		}, nil).Times(1)
		// rnUserModel.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		rnClient.EXPECT().SendReq(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, &rn.RawResponse{
			Request:     &resty.Request{},
			RawResponse: &http.Response{},
		}, nil).Times(2)
		rnLogModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)
		fiatUserClient.EXPECT().GetUserVipInfo(gomock.Any(), gomock.Any()).Return(&user.GetUserVipInfoResponse{
			VipType: "LABEL_TYPE_VIP",
		}, nil).Times(1)
		transactionModel.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		amlClient.EXPECT().QueryAMLResult(gomock.Any(), gomock.Any()).Return(&amlv1.QueryAMLResultResponse{
			QuestionnaireInfo: &amlv1.QuestionnaireInfo{
				IsFinish: true,
				RsaScore: "4",
			},
		}, nil).Times(1)
		fiatUserClient.EXPECT().GetRiskUserInfoByRealTime(gomock.Any(), gomock.Any()).Return(&user.RiskUserResponse{
			Data: &user.RiskUserData{
				KybDetail: &user.KybDetail{
					IsKyb: true,
				},
			},
		}, nil).Times(1)
		bizClient.EXPECT().GetBizSwitchRes(gomock.Any(), gomock.Any()).Return(&bizconfigv1.GetBizSwitchResResponse{
			Result: &bizconfigv1.GetBizSwitchResResponse_SwitchRes{
				CanPass: true,
			},
		}, nil).AnyTimes()

		err := l.ProcessAMLFiatTransaction(context.Background(), &AMLFiatTransactionBody{
			GeneralRequest: &securityv1.GeneralRequest{
				Timestamp: fmt.Sprintf("%d", time.Now().UnixMilli()),
			},
			Scene:          "RechargeTransfer",
			CurrencyAmount: "100",
			CryptoAmount:   "0.01",
			Amount:         "100",
			RiskInfo:       `{"channel_type": "nuvei"}`,
		})
		if err != nil {
			t.Errorf("ProcessAMLFiatTransaction() error = %v", err)
			return
		}
	})

	t.Run("update user", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		transactionModel := mockmodel.NewMockFiatAmlTransactionModel(ctrl)

		bizClient := bizconfigv1.NewMockCustomerAPIClient(ctrl)
		riskControlHubAPIClient := risk_control_hubv1.NewMockRiskControlHubAPIClient(ctrl)
		siteClietMock := new(MockSiteServiceClient)
		l := NewRNLogic(&svc.ServiceContext{
			Config: config.Config{
				RNConfig: rn.Config{
					FiatChannelOrg: &rn.OrgConfig{},
				},
			},
			FiatAmlTransactionModel: transactionModel,
			BizConfigClient:         bizClient,
			RiskControlHubAPIClient: riskControlHubAPIClient,
			SiteServiceClient:       siteClietMock,
		})

		bizClient.EXPECT().GetBizConfig(gomock.Any(), gomock.Any()).Return(&bizconfigv1.GetBizConfigResponse{
			Result: []*bizconfigv1.GetBizConfigResponse_Result{{
				Scenario: golbal.AmlInsightConsumerBizConfigKey,
				Value: &bizconfigv1.GetBizConfigResponse_Result_StrValue{
					StrValue: `{"EnabledSceneMap": {"ByBitCardAuth": true,"ByBitCardFinancial": true,"ByBitCardFinancialReversal": true,"ByBitCardMoneySendAuth": true,"ByBitCardRefund": true,"DepositAndBuyPostTransfer": true,"RechargeTransfer": true,"TradeTransfer": true,"WithdrawTransfer": true } }`,
				},
			}},
		}, nil).AnyTimes()
		siteClietMock.On("GetUser", mock.Anything, mock.Anything, mock.Anything).Return(&site.GetUserResponse{Data: &site.User{
			SiteId: "BYBIT",
		}}, nil)
		transactionModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(model.NewMockedResult(1, 1), nil).Times(1)

		bizClient.EXPECT().GetBizSwitchRes(gomock.Any(), gomock.Any()).Return(&bizconfigv1.GetBizSwitchResResponse{
			Result: &bizconfigv1.GetBizSwitchResResponse_SwitchRes{
				CanPass: false,
			},
		}, nil).Times(2)

		err := l.ProcessAMLFiatTransaction(context.Background(), &AMLFiatTransactionBody{
			GeneralRequest: &securityv1.GeneralRequest{
				Timestamp: fmt.Sprintf("%d", time.Now().UnixMilli()),
			},
			Scene:          "RechargeTransfer",
			CurrencyAmount: "100",
			CryptoAmount:   "0.01",
			Amount:         "100",
			RiskInfo:       `{"channel_type": "nuvei"}`,
		})
		if err != nil {
			t.Errorf("ProcessAMLFiatTransaction() error = %v", err)
			return
		}
	})
}

func Test_getTransactionCategory(t *testing.T) {
	type args struct {
		body          *AMLFiatTransactionBody
		cardProductID string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "TransactionCategoryOnlychain",
			args: args{
				body: &AMLFiatTransactionBody{
					Scene:    "RechargeTransfer",
					RiskInfo: `{"channel_type": "nuvei"}`,
				},
				cardProductID: "",
			},
			want: model.TransactionCategoryOnlychain,
		},
		{
			name: "TransactionCategoryKZ",
			args: args{
				body: &AMLFiatTransactionBody{
					Scene:    "RechargeTransfer",
					RiskInfo: `{"channel_type": "bcc"}`,
				},
				cardProductID: "",
			},
			want: model.TransactionCategoryKZ,
		},
		{
			name: "TransactionCategoryTR",
			args: args{
				body: &AMLFiatTransactionBody{
					Scene:    "RechargeTransfer",
					RiskInfo: `{"channel_type": "narkasa"}`,
				},
				cardProductID: "",
			},
			want: model.TransactionCategoryTR,
		},
		{
			name: "TransactionCategoryCardEEA",
			args: args{
				body: &AMLFiatTransactionBody{
					Scene:    golbal.SceneByBitCardFinancial,
					RiskInfo: ``,
				},
				cardProductID: "0",
			},
			want: model.TransactionCategoryCardEEA,
		},
		{
			name: "TransactionCategoryCardEEA",
			args: args{
				body: &AMLFiatTransactionBody{
					Scene:    golbal.SceneByBitCardFinancial,
					RiskInfo: ``,
				},
				cardProductID: "0",
			},
			want: model.TransactionCategoryCardEEA,
		},
		{
			name: "TransactionCategoryCardAU",
			args: args{
				body: &AMLFiatTransactionBody{
					Scene:    golbal.SceneByBitCardFinancial,
					RiskInfo: ``,
				},
				cardProductID: "1",
			},
			want: model.TransactionCategoryCardAU,
		},
		{
			name: "TransactionCategoryCardAR",
			args: args{
				body: &AMLFiatTransactionBody{
					Scene:    golbal.SceneByBitCardFinancial,
					RiskInfo: ``,
				},
				cardProductID: "2",
			},
			want: model.TransactionCategoryCardAR,
		},
		{
			name: "TransactionCategoryCardBR",
			args: args{
				body: &AMLFiatTransactionBody{
					Scene:    golbal.SceneByBitCardFinancial,
					RiskInfo: ``,
				},
				cardProductID: "3",
			},
			want: model.TransactionCategoryCardBR,
		},
		{
			name: "TransactionCategoryCardKZ",
			args: args{
				body: &AMLFiatTransactionBody{
					Scene:    golbal.SceneByBitCardFinancial,
					RiskInfo: ``,
				},
				cardProductID: "4",
			},
			want: model.TransactionCategoryCardKZ,
		},
		{
			name: "TransactionCategoryCardOther",
			args: args{
				body: &AMLFiatTransactionBody{
					Scene:    golbal.SceneByBitCardFinancial,
					RiskInfo: ``,
				},
				cardProductID: "-1",
			},
			want: model.TransactionCategoryCardOther,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getTransactionCategory(tt.args.body, tt.args.cardProductID); got != tt.want {
				t.Errorf("getTransactionCategory() = %v, want %v", got, tt.want)
			}
		})
	}
	defer proc.CleanupEnvForTest(t)
}

func TestLogic_ProcessAMLFiatTransactionEU(t *testing.T) {
	t.Setenv(proc.SiteIdEnvName, golbal.SiteIdEU)
	t.Run("normal eu", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		transactionModel := mockmodel.NewMockFiatAmlTransactionModel(ctrl)
		rnLogModel := mockmodel.NewMockRnLogModel(ctrl)
		rnUserModel := mockmodel.NewMockRnUserModel(ctrl)
		appealClient := appealv1.NewMockAppealAPIClient(ctrl)
		kycInternalClient := mockrpc.NewMockKycInternalClient(ctrl)
		amlClient := mockrpc.NewMockAmlAPIClient(ctrl)
		fiatUserClient := user.NewMockGrpcUserServiceClient(ctrl)
		rnClient := mockrn.NewMockClient(ctrl)
		bizClient := bizconfigv1.NewMockCustomerAPIClient(ctrl)
		riskControlHubAPIClient := risk_control_hubv1.NewMockRiskControlHubAPIClient(ctrl)
		siteClietMock := new(MockSiteServiceClient)

		l := NewRNLogic(&svc.ServiceContext{
			Config: config.Config{
				RNConfig: rn.Config{
					FiatChannelOrgEU: &rn.OrgConfig{
						Username: "username@bybit",
					},
				},
			},
			FiatAmlTransactionModel: transactionModel,
			RNLogModel:              rnLogModel,
			AppealClient:            appealClient,
			RNUserModel:             rnUserModel,
			RNClient:                rnClient,
			KycInternalClient:       kycInternalClient,
			AmlAPIClient:            amlClient,
			FiatUserClient:          fiatUserClient,
			BizConfigClient:         bizClient,
			RiskControlHubAPIClient: riskControlHubAPIClient,
			SiteServiceClient:       siteClietMock,
		})
		siteClietMock.On("GetUser", mock.Anything, mock.Anything, mock.Anything).Return(&site.GetUserResponse{Data: &site.User{
			SiteId: "EU",
		}}, nil)
		transactionModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(model.NewMockedResult(1, 1), nil).Times(1)
		rnUserModel.EXPECT().FindOneByUserId(gomock.Any(), gomock.Any()).Return(nil, sql.ErrNoRows).Times(1)
		kycInternalClient.EXPECT().GetMemberKYC(gomock.Any(), gomock.Any()).Return(&platformkyc.GetMemberKYCResponse{
			Level: kyc.MemberKYCLevel_LEVEL_1,
			OnboardingAuthInfos: []*platformkyc.GetMemberKYCResponse_KYCAuthInfo{
				{},
			},
		}, nil).Times(1)
		riskControlHubAPIClient.EXPECT().RiskControl(gomock.Any(), gomock.Any()).Return(&risk_control_hubv1.RiskControlResponse{
			Error:      nil,
			ResultCode: 0,
			RiskInfo:   "",
			RiskId:     "",
			RiskResults: map[string]*risk_control_hubv1.RiskControlResponse_RuleResult{
				"Usage_in_Prohibited_Country": {Result: "Match"},
			},
		}, nil)

		bizClient.EXPECT().GetBizConfig(gomock.Any(), gomock.Any()).Return(&bizconfigv1.GetBizConfigResponse{
			Result: []*bizconfigv1.GetBizConfigResponse_Result{{
				Scenario: golbal.AmlInsightConsumerBizConfigKey,
				Value: &bizconfigv1.GetBizConfigResponse_Result_StrValue{
					StrValue: `{"EnabledSceneMap": {"ByBitCardAuth": true,"ByBitCardFinancial": true,"ByBitCardFinancialReversal": true,"ByBitCardMoneySendAuth": true,"ByBitCardRefund": true,"DepositAndBuyPostTransfer": true,"RechargeTransfer": true,"TradeTransfer": true,"WithdrawTransfer": true } }`,
				},
			}},
		}, nil).AnyTimes()
		rnUserModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
		rnClient.EXPECT().SendReq(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, &rn.RawResponse{
			Request:     &resty.Request{},
			RawResponse: &http.Response{},
		}, nil).Times(2)
		rnLogModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)
		fiatUserClient.EXPECT().GetUserVipInfo(gomock.Any(), gomock.Any()).Return(&user.GetUserVipInfoResponse{
			VipType: "LABEL_TYPE_VIP",
		}, nil).Times(1)
		transactionModel.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		fiatUserClient.EXPECT().GetRiskUserInfoByRealTime(gomock.Any(), gomock.Any()).Return(&user.RiskUserResponse{}, nil).Times(1)
		bizClient.EXPECT().GetBizSwitchRes(gomock.Any(), gomock.Any()).Return(&bizconfigv1.GetBizSwitchResResponse{
			Result: &bizconfigv1.GetBizSwitchResResponse_SwitchRes{
				CanPass: true,
			},
		}, nil).AnyTimes()

		err := l.ProcessAMLFiatTransaction(context.Background(), &AMLFiatTransactionBody{
			GeneralRequest: &securityv1.GeneralRequest{
				Timestamp: fmt.Sprintf("%d", time.Now().UnixMilli()),
			},
			Scene:          "RechargeTransfer",
			CurrencyAmount: "100",
			CryptoAmount:   "0.01",
			Amount:         "1000",
			RiskInfo:       `{"channel_type": "worldpay"}`,
		})
		if err != nil {
			t.Errorf("ProcessAMLFiatTransaction() error = %v", err)
			return
		}
	})
}
