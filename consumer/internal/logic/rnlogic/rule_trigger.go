package rnlogic

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/core/stores/redis"
)

const (
	timeRangeSec = 24 * 60 * 60
)

type RNTrigger struct {
	//Scene string `json:"s"`
	Code string `json:"c"`
}

type RNTriggerLogic struct {
	bizRedis *redis.Redis
}

func NewRNTriggerLogic(bizRedis *redis.Redis) *RNTriggerLogic {
	return &RNTriggerLogic{
		bizRedis: bizRedis,
	}
}

func (l *RNTriggerLogic) SetHitRule(ctx context.Context, memberId int64, scene string, result []string) error {

	nowSec := time.Now().Unix()
	k := getRnTriggerKey(ctx, memberId, scene)
	for _, r := range result {
		val, err := json.Marshal(&RNTrigger{
			//Scene: scene,
			Code: r,
		})
		if err != nil {
			logc.Errorw(ctx, "marshal rn trigger err")
			continue
		}
		if _, err := l.bizRedis.ZaddCtx(ctx, k, nowSec, string(val)); err != nil {
			logc.Errorw(ctx, "ZaddCtx error", logc.Field("err", err))
		}
	}
	// 删除旧数据
	if _, err := l.bizRedis.ZremrangebyscoreCtx(ctx, k, -1, nowSec-timeRangeSec); err != nil {
		logc.Errorf(ctx, "ZremrangebyscoreCtx error: %v", err)
	}

	// 设置过期时间
	if err := l.bizRedis.ExpireCtx(ctx, k, timeRangeSec); err != nil {
		logc.Errorf(ctx, "ExpireCtx error: %v", err)
	}

	return nil
}

func (l *RNTriggerLogic) GetHitRuleIn24h(ctx context.Context, memberId int64, scene string) (map[string]struct{}, error) {
	nowSec := time.Now().Unix()
	k := getRnTriggerKey(ctx, memberId, scene)
	resp := make(map[string]struct{})

	vs, err := l.bizRedis.ZrangebyscoreWithScoresCtx(ctx, k, nowSec-timeRangeSec, nowSec)
	if err != nil {
		return resp, err
	}

	for _, v := range vs {
		var rnTrigger RNTrigger
		if err := json.Unmarshal([]byte(v.Key), &rnTrigger); err != nil {
			logc.Errorf(ctx, "Unmarshal error: %v", err)
			continue
		}
		resp[rnTrigger.Code] = struct{}{}
	}

	return resp, nil
}

func getRnTriggerKey(ctx context.Context, memberId int64, scene string) string {
	return fmt.Sprintf("aml_insight_consumer:rn_trigger:%s:%d", scene, memberId)
}
