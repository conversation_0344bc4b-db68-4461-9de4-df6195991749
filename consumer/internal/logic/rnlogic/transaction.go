package rnlogic

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	risk_control_hubv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/risk_control_hub/v1"

	"aml-insight/consumer/internal/logic/riskcontrolhub"
	"aml-insight/internal/golbal"

	amlv1 "code.bydev.io/cht/customer/kyc-stub.git/pkg/bybit/kyc/aml/v1"
	"code.bydev.io/cht/customer/kyc-stub.git/pkg/bybit/kyc/common/enums"
	"code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/card"
	"code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/user"
	"code.bydev.io/frameworks/byone/core/logc"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"

	"aml-insight/internal/model"
	"aml-insight/internal/pkg/rn"
)

type (
	// RequestRiskInfo 风控扩展字段
	RequestRiskInfo struct {
		BybitCardAsynchronous struct {
			TerminalId             string `json:"terminalId"`
			AcquirerId             string `json:"acquirerId"`
			CardholderPresence     string `json:"cardholderPresence"`
			DateValidation         string `json:"dateValidation"`
			DataPresence           string `json:"dataPresence"`
			CvvPresence            string `json:"cvvPresence"`
			PinValidation          string `json:"pinValidation"`
			PinPresence            string `json:"pinPresence"`
			Presence3Ds            string `json:"presence3ds"`
			AuthMethod3Ds          string `json:"authMethod3ds"`
			CardholderVerification string `json:"cardholderVerification"`
			PosEntryMode           string `json:"posEntryMode"`
			TransactionStatus      string `json:"transactionStatus"`
			ResponseMessage        string `json:"responseMessage"`
			Pos                    string `json:"pos"`
			CvvValidation          string `json:"cvvValidation"`
			MerchantCountryCode    string `json:"merchantCountryCode"`
			MerchId                string `json:"merchId"`
			MerchName              string `json:"merchName"`
			CardToken              string `json:"cardToken"`
			BasicAmount            string `json:"basicAmount"`
			BasicAmountUsd         string `json:"basicAmountUsd"` // pay-security 补齐
			BasicCurrency          string `json:"basicCurrency"`
			BillAmount             string `json:"billAmount"`
			BillAmountUsd          string `json:"billAmountUsd"` // pay-security 补齐
			BillCurrency           string `json:"billCurrency"`
			Status                 string `json:"status"`
			MerchCountry           string `json:"merchCountry"`
			LastMerchCountry       string `json:"lastMerchCountry"` // AML 自己进行记录
			TxnType                string `json:"txnType"`
			TransactionCurrency    string `json:"transactionCurrency"`
			TransactionAmount      string `json:"transactionAmount"`
			TransactionAmountUsd   string `json:"transactionAmountUsd"` // pay-security 补齐
			CardSchema             string `json:"cardSchema"`
			CardPresence           string `json:"cardPresence"`
			MerchantCountry        string `json:"merchantCountry"`
			ProcessingCode         string `json:"processingCode"`
			ResponseCode           string `json:"responseCode"`
			TokenizationWalletName string `json:"tokenizationWalletName"`
			AcquirerReferenceData  string `json:"acquirerReferenceData"`
		} `json:"bybit_card_asynchronous"`
	}
)

func (l *Logic) monitorTransaction(ctx context.Context,
	accountReference string,
	body *AMLFiatTransactionBody, trans *model.FiatAmlTransaction, cardProductCode string) error {
	org := l.selectOrg(ctx, trans)
	rnFinancial, err := l.createRnTransaction(ctx, body, trans, cardProductCode)
	if err != nil {
		return errors.Wrap(err, "createRnTransaction")
	}

	// send monitor transaction
	req := new(rn.Request)
	req.AccountStrategyId = org.StrategyId
	req.AccountReference = accountReference
	req.Application.CustomerReference = accountReference
	req.Application.Country = applicationCountryMap[trans.TransactionCategory]
	req.Application.ClientApplicationPurpose = applicationCountryMap[trans.TransactionCategory]
	req.FinancialAccount.FinancialTransaction = []*rn.FinancialTransaction{rnFinancial}

	reqBody, _ := json.Marshal(req)

	rnResp, raw, err := l.svcCtx.RNClient.SendReq(ctx, req, org)
	// we don't care resp, it saves in log.
	// to save log
	_ = l.saveRnRequestLog(ctx, body.GeneralRequest.RequestId, string(reqBody), model.RNLogMethodTransaction,
		raw, err)
	if err != nil {
		return errors.Wrap(err, "SendReq")
	}

	if raw.StatusCode() == http.StatusOK {
		trans.Decision = rnResp.Decision.Code
		trans.Status = model.TransactionStatusSent
		rnRespJson, _ := json.Marshal(rnResp)
		trans.RnResp = model.StringToNullString(string(rnRespJson))
	} else {
		trans.Decision = "failed"
		trans.Status = model.TransactionStatusPending
	}
	if err := l.svcCtx.FiatAmlTransactionModel.Update(ctx, trans); err != nil {
		return errors.Wrap(err, "Update")
	}
	if strings.Contains(string(raw.Body()), `"Errors"`) {
		logc.Errorw(ctx, "monitorTransaction to Rn failed", logc.Field("err", string(raw.Body())))
	}
	return nil
}

// ----------------------------------------------------------------------------
// transaction to rn

func (l *Logic) createRnTransaction(ctx context.Context, body *AMLFiatTransactionBody, trans *model.FiatAmlTransaction, cardProductCode string) (*rn.FinancialTransaction, error) {
	rnFinancial := &rn.FinancialTransaction{
		ClientReference: body.GeneralRequest.RequestId,
		BicCode:         "",
		SortCode:        "",
		AccountNumber:   "",
		AccountHolder:   "", // 2024-11-12 ,我们直接丢 Card 的mcc 即可
		IBAN:            "",
		CreditOrDebit:   "",
		CurrencyCode:    "USD", //body.Currency, 我们全部使用 USDT 的
		// 2024-11-25, paymentType 不在是我们的 bybit payment，而是 HIGH-RISK-CUSTOMER 跟进CRAS 的得分进行判断的
		//PaymentType:          trans.PaymentType,
		// 2024-11-25, PaymentSubtype 不在是我们的 channelType， 通道是我们的用户身份，card 是ATM 的标记
		//PaymentSubtype:       trans.ChannelType,
		CountryOfTransaction: "", // 取值逻辑
		Amount:               trans.FiatConvertedUsdAmount.InexactFloat64(),
		PaymentReference:     body.Scene,
		Balance:              0,
		EffectiveDateTime:    trans.AtTime.Format(time.DateTime),
		ExternalRuleOutcomes: nil,
	}

	var triggerRuleOutcomes []*rn.ExternalRuleOutcomes

	switch body.Scene {
	case "RechargeTransfer", "DepositAndBuyDepositTransfer", "TradeTransfer", "DepositAndBuyPostTransfer":
		rnFinancial.CreditOrDebit = golbal.SceneToRNCreditOrDebitMap[body.Scene]

		if body.CardTokenId != "" {
			if cardDetail, err := l.svcCtx.CardManagerClient.GetCardDetail(ctx, &card.CardDetailReq{
				AppId:       "pay-security",
				CardTokenId: body.CardTokenId,
			}); err != nil {
				logc.Errorw(ctx, "GetCardDetail", logc.Field("error", err), logc.Field("cardId", body.CardTokenId))
			} else {

				switch cardDetail.GetCardType() {
				case "CREDITCARD":
					credit := cardDetail.GetCredit()
					rnFinancial.CountryOfTransaction = credit.GetCardBin().GetCountry()

				case "BANK_ACCOUNT":
					account := cardDetail.GetAccount()
					rnFinancial.CountryOfTransaction = account.GetBankInfo().Country
					//rnFinancial.IBAN = account.GetBankInfo().GetIban()
				case "APM": // APM 没有这个

				}
			}
		}

	case "WithdrawTransfer":
		rnFinancial.CreditOrDebit = golbal.SceneToRNCreditOrDebitMap[body.Scene]
		// 提现卡大部分没有国家信息。我们也尝试获取一下。
		if body.CardTokenId != "" {
			if cardDetail, err := l.svcCtx.CardManagerClient.GetCardDetail(ctx, &card.CardDetailReq{
				AppId:       "pay-security",
				CardTokenId: body.CardTokenId,
			}); err != nil {
				logc.Errorw(ctx, "GetCardDetail", logc.Field("error", err), logc.Field("cardId", body.CardTokenId))
			} else {

				switch cardDetail.GetCardType() {
				case "CREDITCARD":
					credit := cardDetail.GetCredit()
					rnFinancial.CountryOfTransaction = credit.GetCardBin().GetCountry()

				case "BANK_ACCOUNT":
					account := cardDetail.GetAccount()
					rnFinancial.CountryOfTransaction = account.GetBankInfo().Country
					//rnFinancial.IBAN = account.GetBankInfo().GetIban()
				case "APM": // APM 没有这个

				}
			}
		}

	case golbal.SceneByBitCardFinancial,
		golbal.SceneByBitCardAuth,
		golbal.SceneByBitCardAuthReversal,
		golbal.SceneByBitCardFinancialReversal,
		golbal.SceneByBitCardRefund,
		golbal.SceneByBitCardMoneySendAuth,
		golbal.SceneByBitCardMoneySendAuthReversal:
		rnFinancial.CreditOrDebit = golbal.SceneToRNCreditOrDebitMap[body.Scene]
		rnFinancial.CountryOfTransaction = gjson.Get(body.RiskInfo, "bybit_card_asynchronous.merchantCountry").String()
		//rnFinancial.CountryOfTransaction = applicationCountryMap[trans.TransactionCategory]
		rnFinancial.AccountHolder = gjson.Get(body.RiskInfo, "bybit_card_asynchronous.merchantCountryCode").String()
		rnFinancial.ThirdPartyId = gjson.Get(body.RiskInfo, "bybit_card_asynchronous.merchName").String()

		rnFinancial.PaymentType = body.Scene
		rnFinancial.PaymentSubtype = trans.ChannelType // trans 帮忙转换了。

		externalRuleOutcomes, err := l.getExternalRuleOutcomes(ctx, body, cardProductCode)
		if err != nil {
			logc.Errorw(ctx, "get external rule outcomes error", logc.Field("error", err), logc.Field("body", body))
		} else {
			triggerRuleOutcomes = externalRuleOutcomes
		}
	}

	outcomes := new(rn.OutcomesFiled)

	// fill outcomes
	// high risk country
	if body.Scene == golbal.SceneByBitCardFinancial {

	} else {
		for _, each := range highRiskCountry {
			if each == rnFinancial.CountryOfTransaction {
				outcomes.HighRiskCountry = true
				break
			}
		}
	}

	if vip, err := l.svcCtx.FiatUserClient.GetUserVipInfo(ctx, &user.GetUserVipInfoRequest{
		UserId:   fmt.Sprintf("%d", body.GeneralRequest.Fuid),
		BrokerId: body.GeneralRequest.BrokerId,
	}); err != nil {
		logc.Errorw(ctx, "GetUserVipInfo", logc.Field("err", err))
	} else {
		// VipType: LABEL_TYPE_NO_VIP = 非VIP 用户； LABEL_TYPE_VIP = VIP 用户; LABEL_TYPE_ORGANIZATION = 机构用户;
		switch vip.GetVipType() {
		case "LABEL_TYPE_VIP":
			rnFinancial.ExternalRuleOutcomes = append(rnFinancial.ExternalRuleOutcomes, &rn.ExternalRuleOutcomes{
				Code:    rn.OutcomesVIP,
				Outcome: "TRUE",
			})
			outcomes.VIP = true

		case "LABEL_TYPE_ORGANIZATION":
			outcomes.VIP = true
			//outcomes.IsKyb = true
		default: // "LABEL_TYPE_NO_VIP"
		}
	}

	if trans.TransactionCategory == model.TransactionCategoryOnlychain {
		if info, err := l.svcCtx.AmlAPIClient.QueryAMLResult(ctx, &amlv1.QueryAMLResultRequest{
			MemberID:  body.GeneralRequest.Fuid,
			SceneCode: enums.SceneCode_SCENE_CODE_ONLYCHAIN,
		}); err != nil {
			logc.Errorw(ctx, "QueryAMLResult", logc.Field("err", err))
		} else {
			if info.GetQuestionnaireInfo().GetIsFinish() {
				// https://uponly.larksuite.com/docx/Tq8ZdZ6ldovd82xPqkVucfCbs69
				/*
				  1. 0<Score<=4        Low
				  2. 4<Score<=8.5        Medium
				  3. 8.5<Score<10        High
				  4. 10<=Score        Prohibited
				*/
				score := cast.ToFloat64(info.GetQuestionnaireInfo().GetRsaScore())
				switch {
				case score >= 0 && score <= 4,
					score > 4 && score <= 8.5:
					outcomes.HighRiskCustomer = false
				case score > 8.5 && score < 10,
					score >= 10:
					outcomes.HighRiskCustomer = true
					rnFinancial.PaymentType = "HIGH-RISK-CUSTOMER"
				}
			}
		}
	}

	// account age
	if u, err := l.svcCtx.FiatUserClient.GetRiskUserInfoByRealTime(ctx, &user.GetRiskUserInfoByRealTimeRequest{
		UserId: body.GeneralRequest.Fuid,
	}); err != nil {
	} else {
		outcomes.AccountAge = int(u.GetData().GetRegDay())
		if u.GetData().GetKybDetail().GetIsKyb() {
			outcomes.IsKyb = true
		} else {
			outcomes.IsKyb = false
		}

		if body.Scene != golbal.SceneByBitCardFinancial {
			if outcomes.IsKyb {
				rnFinancial.PaymentSubtype = "Business"
			} else {
				rnFinancial.PaymentSubtype = "Person"
			}
		}
	}

	rnFinancial.ExternalRuleOutcomes = outcomes.ConvertToOutcomes()

	if len(triggerRuleOutcomes) != 0 {
		rnFinancial.ExternalRuleOutcomes = append(rnFinancial.ExternalRuleOutcomes, triggerRuleOutcomes...)
	}
	return rnFinancial, nil
}

func (l *Logic) getExternalRuleOutcomes(ctx context.Context, body *AMLFiatTransactionBody, cardProductCode string) ([]*rn.ExternalRuleOutcomes, error) {
	var (
		resp           = make([]*rn.ExternalRuleOutcomes, 0)
		currentHitRule = make([]string, 0)
		riskInfo       RequestRiskInfo
	)

	if err := json.Unmarshal([]byte(body.RiskInfo), &riskInfo); err != nil {
		logc.Warnw(ctx, "getExternalRuleOutcomes", logc.Field("body", body))
	} else {
		// 添加 ARN 信息
		resp = append(resp, &rn.ExternalRuleOutcomes{
			Code:    "Acquirer Reference Number (ARN)",
			Outcome: riskInfo.BybitCardAsynchronous.AcquirerReferenceData,
		})
		// get
		if lastMerchCountry, err := l.lastMerchCountryLogic.Get(ctx, body.GeneralRequest.Fuid); err != nil {
			logc.Warnw(ctx, "Get lastMerchCountryLogic error", logc.Field("err", err))
		} else {
			if len(lastMerchCountry) != 0 {
				riskInfo.BybitCardAsynchronous.LastMerchCountry = lastMerchCountry
				if riskInfoStr, err := json.Marshal(riskInfo); err != nil {
					logc.Warnw(ctx, "Marshal riskInfo error", logc.Field("body", body), logc.Field("err", err))
				} else {
					body.RiskInfo = string(riskInfoStr)
				}
			}
		}

		// set
		if len(riskInfo.BybitCardAsynchronous.MerchCountry) != 0 && riskInfo.BybitCardAsynchronous.TxnType == golbal.TxnTypeAuthorization {
			if err := l.lastMerchCountryLogic.Set(ctx, body.GeneralRequest.Fuid, riskInfo.BybitCardAsynchronous.MerchCountry); err != nil {
				logc.Warnw(ctx, "Set lastMerchCountryLogic error", logc.Field("err", err))
			}
		}
	}

	amlScene, ok := golbal.AMLSceneMap[body.Scene]
	if !ok {
		return nil, errors.New("aml scene not found")
	}

	dataStr, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	rcResp, err := l.svcCtx.RiskControlHubAPIClient.RiskControl(ctx, &risk_control_hubv1.RiskControlRequest{
		Business:       "AML_INSIGHT",
		Scene:          amlScene,
		Uid:            body.GeneralRequest.Fuid,
		Amount:         body.Amount,
		Currency:       body.Currency,
		CurrencyAmount: body.CurrencyAmount,
		Crypto:         body.Crypto,
		CryptoAmount:   body.CryptoAmount,
		OrderId:        body.OrderId,
		CardTokenId:    body.CardTokenId,
		Data:           string(dataStr),
		BizSiteId:      riskcontrolhub.GetCardBizSiteId(ctx, l.svcCtx, cardProductCode),
	})
	if err != nil {
		return nil, err
	}

	rc24HoursHitResult, err := l.rnTriggerLogic.GetHitRuleIn24h(ctx, body.GeneralRequest.Fuid, body.Scene)
	if err != nil {
		logc.Errorw(ctx, "get24HoursHitRule", logc.Field("err", err))
	}
	for resultKey, result := range rcResp.RiskResults {
		if result.Result == "Match" {
			currentHitRule = append(currentHitRule, resultKey)
			if _, ok := rc24HoursHitResult[resultKey]; ok {
				resp = append(resp, &rn.ExternalRuleOutcomes{
					Code:    resultKey,
					Outcome: golbal.RnOutComeReTrigger,
				})
				continue
			}
			resp = append(resp, &rn.ExternalRuleOutcomes{
				Code:    resultKey,
				Outcome: golbal.RnOutComeTrigger,
			})
		}
	}

	if err = l.rnTriggerLogic.SetHitRule(ctx, body.GeneralRequest.Fuid, body.Scene, currentHitRule); err != nil {
		logc.Errorw(ctx, "setHitRule", logc.Field("err", err))
	}

	return resp, nil
}
