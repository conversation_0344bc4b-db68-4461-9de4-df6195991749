package logic

import (
	"context"
	"encoding/json"

	"code.bydev.io/frameworks/byone/core/logc"

	"code.bydev.io/frameworks/byone/kafka"

	"aml-insight/consumer/internal/logic/fiatappeallogic"
	"aml-insight/consumer/internal/svc"
)

type FiatPaySecurityTopicLogic struct {
	ctx *svc.ServiceContext
	*fiatappeallogic.Logic
}

func NewFiatPaySecurityTopicLogic(ctx *svc.ServiceContext) *FiatPaySecurityTopicLogic {
	return &FiatPaySecurityTopicLogic{
		ctx:   ctx,
		Logic: fiatappeallogic.NewLogic(ctx),
	}
}

// Consume implements the logic of consuming message from kafka, Topic: fiat-pay-security-topic
func (l *FiatPaySecurityTopicLogic) Consume(ctx context.Context, message *kafka.Message) error {
	if message == nil || len(message.Value) == 0 {
		logc.Errorw(ctx, "Consume message error", logc.Field("msg", message))
		return nil
	}
	logc.Infow(ctx, "FiatPaySecurityTopicLogic Message value", logc.Field("message.Value", string(message.Value)))

	var body fiatappeallogic.MQBody
	if err := json.Unmarshal(message.Value, &body); err != nil {
		logc.Errorw(ctx, "Consume Unmarshal error", logc.Field("message.Value", string(message.Value)), logc.Field("error", err))
		return err
	}

	switch body.Event {
	case "OnChainKYTEdd",
		"OnChainKYAEddEmpty",
		"OnChainKYTEmpty",
		"OnChainKYAEdd",
		"OnChainSTRSAR":

	default:
		logc.Infow(ctx, "Skip FiatPaySecurityTopicLogic Message event", logc.Field("event", body.Event))
		return nil
	}
	if body.Body == "" {
		// empty ext, skip
		logc.Infow(ctx, "Skip FiatPaySecurityTopicLogic Message ext", logc.Field("ext", body.Body))
		return nil
	}

	var appealInfo fiatappeallogic.SyncAmlAppealInfo
	if err := json.Unmarshal([]byte(body.Body), &appealInfo); err != nil {
		logc.Errorw(ctx, "Consume Unmarshal appealInfo error", logc.Field("ext", body.Body), logc.Field("error", err))
		return err
	}

	err := l.ProcessAppealStatusChangeEvent(ctx, &appealInfo)
	if err != nil {
		logc.Error(ctx, "ProcessAppealStatusChangeEvent failed", logc.Field("err", err))
	}
	return nil
}
