package metrics

import (
	"time"

	"code.bydev.io/frameworks/byone/core/metric"
)

const (
	StatusSuccess         = "success"
	StatusTimeoutContinue = "timeout_continue"
	StatusInvalid         = "invalid"
	StatusInsertFailed    = "insert_failed"
)

var (
	metricBackfillAmlCaseTotal = metric.NewCounterVec(
		&metric.CounterVecOpts{
			Namespace: "aml_insight",
			Subsystem: "consumer",
			Name:      "backfill_aml_case_total",
			Help:      "count of backfill aml case events",
			Labels:    []string{"status"},
		},
	)

	metricRateLimitWaitDuration = metric.NewHistogramVec(
		&metric.HistogramVecOpts{
			Namespace: "aml_insight",
			Subsystem: "consumer",
			Name:      "rate_limit_wait_duration_ms",
			Help:      "duration(ms) of rate limit wait",
			Labels:    []string{"status"},
			Buckets:   []float64{10, 50, 100, 200, 500, 1000, 2000, 5000},
		},
	)
)

// IncBackfillAmlCaseTotal: increase backfill aml case count
func IncBackfillAmlCaseTotal(status string) {
	metricBackfillAmlCaseTotal.Inc(status)
}

// ObserveRateLimitWaitDuration: observe rate limit wait duration
func ObserveRateLimitWaitDuration(status string, duration time.Duration) {
	metricRateLimitWaitDuration.Observe(duration.Milliseconds(), status)
}
