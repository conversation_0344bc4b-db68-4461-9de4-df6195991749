package rnwebhooklogic

import (
	"aml-insight/internal/golbal"
	"aml-insight/internal/model"
)

var (
	appealTypeMap = map[string]string{
		model.TransactionCategoryOnlychain: "AMLFiatOnlychainEDD",
		model.TransactionCategoryKZ:        "AMLFiatKZEDD",
		model.TransactionCategoryTR:        "AMLFiatTREDD",
		model.TransactionCategoryCardEEA:   "AMLCardEEAEDD",
		model.TransactionCategoryCardAU:    "AMLCardAUEDD",
		model.TransactionCategoryCardAR:    "AMLCardAREDD",
		model.TransactionCategoryCardBR:    "AMLCardBREDD",
		model.TransactionCategoryCardKZ:    "AMLCardKZEDD",
		model.TransactionCategoryCardHKG:   "AMLCardHKGEDD",
		model.TransactionCategoryCardGEO:   "AMLCardGEOEDD",
		//model.TransactionCategoryCardMEX:   "AMLCardMEXEDD",
		model.TransactionCategoryCardOther: "AMLCardOtherEDD",

		// EU Site
		model.TransactionCategoryFiatEU: "AMLFiatEDD",
		model.TransactionCategoryCardEU: "AMLCardEDD",
	}

	// https://uponly.larksuite.com/wiki/wikusTvqF9u9ztvyfOmMWcalMBi
	salesforceTypeMap = map[string]int{
		model.TransactionCategoryOnlychain: 11,
		model.TransactionCategoryKZ:        12,
		model.TransactionCategoryTR:        13,
		model.TransactionCategoryFiatOther: 19,
		model.TransactionCategoryCardEEA:   14,
		model.TransactionCategoryCardAU:    16,
		model.TransactionCategoryCardAR:    18,
		model.TransactionCategoryCardBR:    17,
		model.TransactionCategoryCardKZ:    15,
		model.TransactionCategoryCardHKG:   28,
		model.TransactionCategoryCardGEO:   29,
		//model.TransactionCategoryCardMEX:   30,
		model.TransactionCategoryCardOther: 20,

		// EU Site
		model.TransactionCategoryFiatEU: 31,
		model.TransactionCategoryCardEU: 32,
	}

	bizCodeMap = map[string]string{
		"RechargeTransfer":                         "DEPOSIT",
		"DepositAndBuyPostTransfer":                "DEPOSIT",
		"DepositAndBuyDepositTransfer":             "DEPOSIT",
		"TradeTransfer":                            "EXPRESS",
		"WithdrawTransfer":                         "WITHDRAW",
		golbal.SceneByBitCardAuth:                  "CARD_CONSUME",
		golbal.SceneByBitCardAuthReversal:          "CARD_CONSUME",
		golbal.SceneByBitCardFinancial:             "CARD_CONSUME",
		golbal.SceneByBitCardFinancialReversal:     "CARD_CONSUME",
		golbal.SceneByBitCardRefund:                "CARD_CONSUME",
		golbal.SceneByBitCardMoneySendAuth:         "CARD_CONSUME",
		golbal.SceneByBitCardMoneySendAuthReversal: "CARD_CONSUME",
	}
	salesforceDepositWithdrawalMap = map[string]string{
		"RechargeTransfer":                         "deposit",
		"DepositAndBuyPostTransfer":                "deposit",
		"DepositAndBuyDepositTransfer":             "deposit",
		"TradeTransfer":                            "trade",
		"WithdrawTransfer":                         "withdraw",
		golbal.SceneByBitCardAuth:                  "bybitCard",
		golbal.SceneByBitCardAuthReversal:          "bybitCard",
		golbal.SceneByBitCardFinancial:             "bybitCard",
		golbal.SceneByBitCardFinancialReversal:     "bybitCard",
		golbal.SceneByBitCardRefund:                "bybitCard",
		golbal.SceneByBitCardMoneySendAuth:         "bybitCard",
		golbal.SceneByBitCardMoneySendAuthReversal: "bybitCard",
	}
)

type SalesforceFiatRequest struct {
	Type           int    `json:"type,omitempty"`
	RequestId      string `json:"request_id,omitempty"`
	Uid            string `json:"uid,omitempty"`
	MainAccountUid string `json:"main_account_uid,omitempty"` // 用户的母账号UID
	//GroupId                    string   `json:"group_id,omitempty"`
	Time                       string `json:"time,omitempty"`
	AmlLabel                   string `json:"aml_label,omitempty"`
	DepositWithdrawal          string `json:"deposit_withdrawal,omitempty"`
	CurrentAmount              string `json:"current_amount,omitempty"`
	Crypto2FiatAmountLastTrade string `json:"crypto_2_fiat_amount_last_trade,omitempty"`
	BuyAmountLastTrade         string `json:"buy_amount_last_trade,omitempty"`
	BuyAmount30Day             string `json:"buy_amount_30_day,omitempty"`
	BuyAmountTotal             string `json:"buy_amount_total,omitempty"`
	SellAmountLastTrade        string `json:"sell_amount_last_trade,omitempty"`
	SellAmount30Day            string `json:"sell_amount_30_day,omitempty"`
	SellAmountTotal            string `json:"sell_amount_total,omitempty"`
	TradeCnt100Minute          string `json:"trade_cnt_100_minute,omitempty"`
	RegisterDays               string `json:"register_days,omitempty"`
	LastTradeDays              string `json:"last_trade_days,omitempty"`
	//RequireMaterials           []string `json:"require_materials,omitempty"`

	// 以下是rn同步到sf的一些字段
	RnUnifiedScore         string   `json:"rn_unified_score,omitempty"`         // 规则总分
	RnRulesTriggered       string   `json:"rn_rules_triggered,omitempty"`       // 规则数量
	RnTranscationReference string   `json:"rn_transaction_reference,omitempty"` // 命中的交易ID
	RnRulesName            []string `json:"rn_rules_name,omitempty"`            // 触发规则
	RnAmount               string   `json:"rn_amount,omitempty"`                // 交易金额。如 "0.12345 USD"
	RnReferralReference    string   `json:"rn_referral_reference,omitempty"`    // alert的工单号
	RnScene                string   `json:"rn_scene,omitempty"`                 // 交易场景
	RnCreditOrDebit        string   `json:"rn_credit_or_debit,omitempty"`       // 交易类型，Credit 或 Debit
}
