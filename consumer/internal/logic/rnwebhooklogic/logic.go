package rnwebhooklogic

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	appealv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/appeal/v1"
	"code.bydev.io/frameworks/byone/core/contextutils"
	"code.bydev.io/frameworks/byone/core/logc"
	site "git.bybit.com/svc/stub/pkg/pb/api/sitv1"
	"github.com/pkg/errors"

	"aml-insight/consumer/internal/config"
	"aml-insight/consumer/internal/logic/rnlogic"
	"aml-insight/consumer/internal/svc"
	"aml-insight/internal/golbal"
	"aml-insight/internal/logic/lib"
	"aml-insight/internal/model"
	"aml-insight/internal/pkg/rn"
)

type (
	Logic struct {
		svcCtx *svc.ServiceContext
	}
)

func NewLogic(svcCtx *svc.ServiceContext) *Logic {
	return &Logic{
		svcCtx: svcCtx,
	}
}

func (l *Logic) ProcessRiskNarrativeWebhook(ctx context.Context, req *rn.ReferralWebhookBody) error {
	trans, err := l.svcCtx.FiatAmlTransactionModel.FindOneByRequestId(ctx, req.ClientApplicationReference)
	if err != nil {
		return errors.Wrap(err, "FindOneByRequestId")
	}
	ctx = l.overwriteSiteID(ctx, int64(trans.MemberId))

	reqBody, _ := json.Marshal(req)
	// save the webhook
	rnLog := &model.RnLog{
		RequestId: trans.RequestId,
		Method:    model.RNLogMethodWebhook,
		Req:       model.StringToNullString(string(reqBody)),
		Resp:      model.StringToNullString(""),
		Status:    200,
		Duration:  0,
	}
	defer func() {
		_, err = l.svcCtx.RNLogModel.Insert(ctx, rnLog)
		if err != nil {
			logc.Errorw(ctx, "RNLogModel.Insert", logc.Field("err", err))
		}
	}()

	trans.RnCallback = req.Status
	if err := l.svcCtx.FiatAmlTransactionModel.Update(ctx, trans); err != nil {
		logc.Errorw(ctx, "FiatAmlTransactionModel.Update", logc.Field("err", err))
	}

	switch req.Status {
	case "DECLINE":
	case "ACCEPT", "CLOSED": // ACCEPT and CLOSED are treated the same, just return
		return nil
	}

	var originReq rnlogic.AMLFiatTransactionBody
	if err := json.Unmarshal([]byte(trans.ExtInfo.String), &originReq); err != nil {
		logc.Errorw(ctx, "json.Unmarshal", logc.Field("err", err))
		//return errors.Wrap(err, "json.Unmarshal")
	}

	appealReq := &appealv1.CreateAppealForInternalRequest{
		Uid:         int64(trans.MemberId),
		Reason:      req.DecisionReason.Label,
		Type:        appealTypeMap[trans.TransactionCategory],
		ExpireTime:  0,
		Source:      "aml-insight",
		RequireType: nil,
		Option: &appealv1.AppealOption{
			TransactionId:   trans.OrderNo,
			Channel:         trans.ChannelType,
			Currency:        trans.FiatCurrencySymbol,
			CurrencyAmount:  trans.FiatCurrencyAmount.String(),
			Crypto:          trans.DigitalCurrencySymbol,
			CryptoAmount:    trans.DigitalCurrencyAmount.String(),
			Operator:        "",
			Ext:             "",
			TransactionType: "orderId",
			PaymentMethod:   trans.PaymentType,
			CardTokenId:     originReq.CardTokenId,
			RelatedAppeal:   true,
			Scene:           originReq.Scene,
			RequestId:       originReq.GeneralRequest.RequestId,
		},
		BizCode: "",
	}
	salesforceExt := &SalesforceFiatRequest{
		Type:              salesforceTypeMap[trans.TransactionCategory],
		RequestId:         trans.RequestId,
		Uid:               fmt.Sprintf("%d", trans.MemberId),
		MainAccountUid:    fmt.Sprintf("%d", trans.MemberId),
		Time:              trans.AtTime.Format(time.DateTime),
		AmlLabel:          "",
		DepositWithdrawal: "",
		CurrentAmount:     originReq.Amount,
		// TODO: fill in the following fields
		//Crypto2FiatAmountLastTrade: "",
		//BuyAmountLastTrade:         "",
		//BuyAmount30Day:             "",
		//BuyAmountTotal:             "",
		//SellAmountLastTrade:        "",
		//SellAmount30Day:            "",
		//SellAmountTotal:            "",
		//TradeCnt100Minute:          "",
		//RegisterDays:               "",
		//LastTradeDays:              "",
	}

	// 同步一些rn的字段
	if trans.RnResp.Valid {
		rnResp := &rn.Response{}
		if err := json.Unmarshal([]byte(trans.RnResp.String), rnResp); err != nil {
			logc.Errorw(ctx, "json.Unmarshal", logc.Field("err", err), logc.Field("rnResp", trans.RnResp.String))
		} else if len(rnResp.Applications) > 0 && len(rnResp.Applications[0].RulesetOutcomes) > 0 {
			triggeredRules := []string{}
			for _, rule := range rnResp.Applications[0].RulesetOutcomes[0].RuleOutcomes {
				if rule.Outcome {
					triggeredRules = append(triggeredRules, rule.Name)
				}
			}
			salesforceExt.RnRulesTriggered = strconv.Itoa(len(triggeredRules))
			salesforceExt.RnRulesName = triggeredRules
			salesforceExt.RnUnifiedScore = strconv.Itoa(rnResp.Applications[0].RulesetOutcomes[0].Score)
		}
		salesforceExt.RnTranscationReference = req.ClientApplicationReference
		salesforceExt.RnAmount = fmt.Sprintf("%f USD", trans.FiatConvertedUsdAmount.InexactFloat64())
		salesforceExt.RnReferralReference = req.ReferralReference
		salesforceExt.RnScene = originReq.Scene
		salesforceExt.RnCreditOrDebit = golbal.SceneToRNCreditOrDebitMap[originReq.Scene]
	}

	appealReq.BizCode = bizCodeMap[originReq.Scene]
	salesforceExt.DepositWithdrawal = salesforceDepositWithdrawalMap[originReq.Scene]

	salesforceExtBody, _ := json.Marshal(map[string]any{"salesforce": salesforceExt})
	appealReq.Option.Ext = string(salesforceExtBody)

	if l.disabledAppeal(ctx, appealReq.Type) {
		appealReqMarshaled, err := json.Marshal(appealReq)
		if err != nil {
			logc.Errorw(ctx, "json.Marshal", logc.Field("err", err))
		} else {
			logc.Infow(ctx, "skip report to appeal", logc.Field("appeal_req", string(appealReqMarshaled)))
		}
		rnLog.Status = 0
		rnLog.Resp = model.StringToNullString(fmt.Sprintf("CreateAppealForInternal skiped"))
		return nil
	}
	if r, err := l.svcCtx.AppealClient.CreateAppealForInternal(ctx, appealReq); err != nil {
		logc.Errorw(ctx, "CreateAppealForInternal", logc.Field("err", err))
		rnLog.Status = 0
		rnLog.Resp = model.StringToNullString(fmt.Sprintf("CreateAppealForInternal failed %+v", err))
	} else {
		if r != nil && r.Error != nil {
			logc.Warnw(ctx, "CreateAppealForInternal error", logc.Field("err", r.Error))
		}
		data, _ := json.Marshal(r)
		rnLog.Resp = model.StringToNullString(string(data))
	}

	return nil
}

// EU Site 站点通用处理流程(主站也适用)
//
//	查询交易中的 UID （或者请求中自带的 UID）
//	根据 UID 查询 SiteService 服务，获取 UserSiteID
//	覆盖 ctx x-user-site-id 和 ctx x-refer-site-id
func (l *Logic) overwriteSiteID(ctx context.Context, uid int64) context.Context {
	userInfo, err := l.svcCtx.SiteServiceClient.GetUser(ctx, &site.GetUserRequest{UserId: uid})
	if err != nil {
		logc.Errorw(ctx, "get user info error", logc.Field("err", err), logc.Field("uid", uid))
		return ctx
	}
	bizSiteID := userInfo.GetData().GetSiteId()
	return contextutils.SetReferSiteId(contextutils.SetUserSiteId(ctx, bizSiteID), bizSiteID)
}

func (l *Logic) disabledAppeal(ctx context.Context, appealType string) bool {
	resp, err := lib.GetBizConfigWithCache[config.AMLInsightConsumerBizConfig](ctx, l.svcCtx.BizConfigClient, golbal.AmlInsightConsumerBizConfigKey)
	if err != nil {
		logc.Errorw(ctx, "get biz conf error", logc.Field("err", err))
		return false
	}
	if resp.DisabledAppeal == nil {
		return false
	}
	if disabled, ok := resp.DisabledAppeal[appealType]; ok {
		return disabled
	}
	return false
}
