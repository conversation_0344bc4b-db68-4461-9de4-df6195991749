package rnwebhooklogic

import (
	"context"
	"testing"

	appealv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/appeal/v1"
	bizconfigv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/bizconfig/v1"
	site "git.bybit.com/svc/stub/pkg/pb/api/sitv1"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/mock"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc"

	"aml-insight/consumer/internal/svc"
	"aml-insight/internal/golbal"
	mockmodel "aml-insight/internal/mock/model"
	"aml-insight/internal/model"
	"aml-insight/internal/pkg/rn"
)

type MockSiteServiceClient struct {
	mock.Mock
}

func (m *MockSiteServiceClient) GetUser(ctx context.Context, in *site.GetUserRequest, opts ...grpc.CallOption) (*site.GetUserResponse, error) {
	args := m.Called(ctx, in, opts)
	return args.Get(0).(*site.GetUserResponse), args.Error(1)
}

func TestLogic_ProcessRiskNarrativeWebhook(t *testing.T) {
	t.Run("DECLINE", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		transactionModel := mockmodel.NewMockFiatAmlTransactionModel(ctrl)
		rnLogModel := mockmodel.NewMockRnLogModel(ctrl)
		appealClient := appealv1.NewMockAppealAPIClient(ctrl)
		bizClient := bizconfigv1.NewMockCustomerAPIClient(ctrl)
		siteClietMock := new(MockSiteServiceClient)
		l := NewLogic(&svc.ServiceContext{
			FiatAmlTransactionModel: transactionModel,
			RNLogModel:              rnLogModel,
			AppealClient:            appealClient,
			BizConfigClient:         bizClient,
			SiteServiceClient:       siteClietMock,
		})

		transactionModel.EXPECT().FindOneByRequestId(gomock.Any(), gomock.Any()).Return(&model.FiatAmlTransaction{
			ExtInfo: model.StringToNullString(`{"general_request":{"request_id":"123"}, "scene": "RechargeTransfer"}`),
		}, nil).Times(1)
		siteClietMock.On("GetUser", mock.Anything, mock.Anything, mock.Anything).Return(&site.GetUserResponse{Data: &site.User{
			SiteId: "BYBIT",
		}}, nil)
		bizClient.EXPECT().GetBizConfig(gomock.Any(), gomock.Any()).Return(&bizconfigv1.GetBizConfigResponse{
			Result: []*bizconfigv1.GetBizConfigResponse_Result{{
				Scenario: golbal.AmlInsightConsumerBizConfigKey,
				Value: &bizconfigv1.GetBizConfigResponse_Result_StrValue{
					StrValue: `{"EnabledSceneMap": {"ByBitCardAuth": true,"ByBitCardFinancial": true,"ByBitCardFinancialReversal": true,"ByBitCardMoneySendAuth": true,"ByBitCardRefund": true,"DepositAndBuyPostTransfer": true,"RechargeTransfer": true,"TradeTransfer": true,"WithdrawTransfer": true } }`,
				},
			}},
		}, nil)
		rnLogModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
		transactionModel.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		appealClient.EXPECT().CreateAppealForInternal(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)

		err := l.ProcessRiskNarrativeWebhook(context.Background(), &rn.ReferralWebhookBody{
			Status: "DECLINE",
		})
		if err != nil {
			t.Errorf("ProcessRiskNarrativeWebhook() error = %v", err)
			return
		}

	})

	t.Run("RN_FIELDS", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		transactionModel := mockmodel.NewMockFiatAmlTransactionModel(ctrl)
		rnLogModel := mockmodel.NewMockRnLogModel(ctrl)
		appealClient := appealv1.NewMockAppealAPIClient(ctrl)
		bizClient := bizconfigv1.NewMockCustomerAPIClient(ctrl)
		siteClietMock := new(MockSiteServiceClient)
		l := NewLogic(&svc.ServiceContext{
			FiatAmlTransactionModel: transactionModel,
			RNLogModel:              rnLogModel,
			AppealClient:            appealClient,
			BizConfigClient:         bizClient,
			SiteServiceClient:       siteClietMock,
		})

		transactionModel.EXPECT().FindOneByRequestId(gomock.Any(), gomock.Any()).Return(&model.FiatAmlTransaction{
			ExtInfo:                model.StringToNullString(`{"general_request":{"request_id":"123"}, "scene": "RechargeTransfer"}`),
			FiatConvertedUsdAmount: decimal.New(1, 2),
			RnResp: model.StringToNullString(`{
				"Applications":[{
					"rulesetOutcomes":[
						{
							"score":10,
							"ruleOutcomes":[
								{
									"outcome":true,
									"name":"rule1",
									"score":11
								}
							]
						}
					]
				}]}`),
		}, nil).Times(1)
		rnLogModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
		siteClietMock.On("GetUser", mock.Anything, mock.Anything, mock.Anything).Return(&site.GetUserResponse{Data: &site.User{
			SiteId: "BYBIT",
		}}, nil)
		transactionModel.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		appealClient.EXPECT().CreateAppealForInternal(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)

		err := l.ProcessRiskNarrativeWebhook(context.Background(), &rn.ReferralWebhookBody{
			Status: "DECLINE",
		})
		if err != nil {
			t.Errorf("ProcessRiskNarrativeWebhook() error = %v", err)
			return
		}
	})
}
