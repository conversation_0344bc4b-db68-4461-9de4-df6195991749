package logic

import (
	"context"
	"errors"
	"fmt"

	"aml-insight/consumer/internal/svc"
	"aml-insight/internal/model"
	"aml-insight/internal/service"
	"aml-insight/pkg/common"

	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/kafka"
)

var (
	errMessageEmpty = errors.New("message or message value is empty")
)

type AllAddressLabelLogic struct {
	ctx              *svc.ServiceContext
	transformService *service.EntityMappingTransformService
}

func NewAllAddressLabelLogic(ctx *svc.ServiceContext) *AllAddressLabelLogic {
	return &AllAddressLabelLogic{
		ctx:              ctx,
		transformService: service.NewEntityMappingTransformService(ctx.EntityNameMappingModel),
	}
}

func (l *AllAddressLabelLogic) Consume(ctx context.Context, message *kafka.Message) error {
	logc.Debugw(ctx, "consume message", logc.Field("message", message))
	if message == nil {
		return fmt.Errorf("message is nil")
	}
	if len(message.Value) == 0 {
		return fmt.Errorf("message or message value is empty, topic: %s", message.Topic)
	}

	addressLabel, err := common.UnmarshalMessage[model.AllAddressLabel](message.Value)
	if err != nil {
		if err == errMessageEmpty {
			logc.Errorw(ctx, "message or message value is empty", logc.Field("msg", message))
			return nil
		} else {
			logc.Errorw(ctx, "failed to unmarshal message", logc.Field("err", err), logc.Field("msg", message))
			return err
		}
	}

	_, err = l.upsert(ctx, addressLabel)
	if err != nil {
		logc.Errorw(ctx, "failed to upsert address label", logc.Field("err", err), logc.Field("addressLabel", addressLabel))
	}
	return nil
}

func (l *AllAddressLabelLogic) upsert(ctx context.Context, data *model.AllAddressLabel) (int64, error) {
	if transformedEntityName, transformedDetailName, err := l.transformService.TransformEntityNameAndDetailName(ctx, data.EntityName, data.DetailName); err == nil {
		data.EntityName = transformedEntityName
		data.DetailName = transformedDetailName
	} else {
		logc.Warnw(ctx, "failed to transform entity name and detail name, using original", logc.Field("original", data.EntityName), logc.Field("error", err))
	}

	transformedSource, err := l.transformService.TransformSource(ctx, data.Source)
	if err != nil {
		logc.Warnw(ctx, "failed to transform source, using original", logc.Field("original", data.Source), logc.Field("error", err))
		transformedSource = data.Source
	}
	data.Source = transformedSource
	return l.ctx.AllAddressLabelModel.Upsert(ctx, data)
}
