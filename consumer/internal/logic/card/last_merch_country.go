package card

import (
	"context"
	"errors"
	"fmt"

	"code.bydev.io/frameworks/byone/core/stores/redis"
)

const (
	keepSec = 60 * 60
)

type LastMerchCountryLogic struct {
	bizRedis *redis.Redis
}

func NewLastMerchCountryLogic(bizRedis *redis.Redis) *LastMerchCountryLogic {
	return &LastMerchCountryLogic{
		bizRedis: bizRedis,
	}
}

func (l *LastMerchCountryLogic) Set(ctx context.Context, memberId int64, merchCountry string) error {
	if memberId <= 0 || merchCountry == "" {
		return errors.New("param error")
	}
	return l.bizRedis.SetexCtx(ctx, l.getKey(ctx, memberId), merchCountry, keepSec)
}

func (l *LastMerchCountryLogic) Get(ctx context.Context, memberId int64) (string, error) {
	if memberId <= 0 {
		return "", errors.New("param error")
	}
	return l.bizRedis.GetCtx(ctx, l.getKey(ctx, memberId))
}

func (l *LastMerchCountryLogic) getKey(ctx context.Context, memberId int64) string {
	return fmt.Sprintf("aml_insight_consumer:last_hour_auth_merch_country:%d", memberId)
}
