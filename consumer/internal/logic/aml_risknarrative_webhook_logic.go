package logic

import (
	"context"
	"encoding/json"

	"code.bydev.io/frameworks/byone/core/logc"

	"aml-insight/consumer/internal/logic/rnwebhooklogic"
	"aml-insight/consumer/internal/svc"
	"aml-insight/internal/pkg/rn"

	"code.bydev.io/frameworks/byone/kafka"
)

type AmlRiskNarrativeWebhookLogic struct {
	ctx *svc.ServiceContext
	*rnwebhooklogic.Logic
}

func NewAmlRisknarrativeWebhookLogic(ctx *svc.ServiceContext) *AmlRiskNarrativeWebhookLogic {
	return &AmlRiskNarrativeWebhookLogic{
		ctx:   ctx,
		Logic: rnwebhooklogic.NewLogic(ctx),
	}
}

func (l *AmlRiskNarrativeWebhookLogic) Consume(ctx context.Context, message *kafka.Message) error {
	if message == nil || len(message.Value) == 0 {
		logc.Errorw(ctx, "Consume message error", logc.Field("msg", message))
		return nil
	}
	logc.Infow(ctx, "AmlRiskNarrativeWebhookLogic Message value", logc.Field("message.Value", string(message.Value)))

	var body *rn.ReferralWebhookBody
	if err := json.Unmarshal(message.Value, &body); err != nil {
		logc.Errorw(ctx, "Consume Unmarshal error", logc.Field("message.Value", string(message.Value)), logc.Field("error", err))
		return err
	}
	err := l.ProcessRiskNarrativeWebhook(ctx, body)
	if err != nil {
		logc.Error(ctx, "ProcessRiskNarrativeWebhook failed", logc.Field("err", err))
	}
	return nil
}
