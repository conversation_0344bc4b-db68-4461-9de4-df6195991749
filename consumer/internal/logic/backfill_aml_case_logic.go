package logic

import (
	"context"
	"time"

	"aml-insight/consumer/internal/logic/metrics"
	"aml-insight/consumer/internal/svc"
	"aml-insight/internal/model"
	"aml-insight/pkg/common"

	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/kafka"
	"golang.org/x/time/rate"
)

type BackfillAmlCaseLogic struct {
	svcCtx      *svc.ServiceContext
	rateLimiter *rate.Limiter
}

func NewBackfillAmlCaseLogic(ctx *svc.ServiceContext) *BackfillAmlCaseLogic {
	var rateLimiter *rate.Limiter
	config := ctx.Config.BackfillAmlCase.RateLimit

	if config.Enabled && config.QPS > 0 {
		burst := config.Burst
		if burst <= 0 {
			burst = int(config.QPS) // default burst equals to QPS
		}
		rateLimiter = rate.NewLimiter(rate.Limit(config.QPS), burst)
		logc.Infow(context.Background(), "backfill aml case rate limiter initialized",
			logc.Field("qps", config.QPS),
			logc.Field("burst", burst),
		)
	}

	return &BackfillAmlCaseLogic{
		svcCtx:      ctx,
		rateLimiter: rateLimiter,
	}
}

// waitForRateLimit: wait for rate limiter to release token
func (l *BackfillAmlCaseLogic) waitForRateLimit(ctx context.Context) {
	if l.rateLimiter == nil {
		return
	}

	config := l.svcCtx.Config.BackfillAmlCase.RateLimit
	start := time.Now()

	if l.rateLimiter.Allow() {
		metrics.ObserveRateLimitWaitDuration(metrics.StatusSuccess, time.Since(start))
		return
	}

	// adaptive wait time based on current QPS
	adaptiveWait := time.Duration(1000/config.QPS) * time.Millisecond
	if adaptiveWait <= 0 {
		adaptiveWait = 5 * time.Millisecond
	}

	select {
	case <-time.After(adaptiveWait):
	case <-ctx.Done():
	}

	metrics.ObserveRateLimitWaitDuration(metrics.StatusTimeoutContinue, time.Since(start))
	logc.Infow(ctx, "adaptive rate limit wait completed",
		logc.Field("wait_duration", adaptiveWait), logc.Field("qps", config.QPS))
}

func (l *BackfillAmlCaseLogic) Consume(ctx context.Context, message *kafka.Message) error {
	logc.Debugw(ctx, "backfill amlcase", logc.Field("msg", message))

	if message == nil || len(message.Value) == 0 {
		logc.Warnw(ctx, "message or message value is empty", logc.Field("msg", message))
		return nil
	}

	l.waitForRateLimit(ctx)

	amlCase, err := common.UnmarshalMessage[model.AmlCase](message.Value)
	if err != nil {
		metrics.IncBackfillAmlCaseTotal(metrics.StatusInvalid)
		logc.Errorw(ctx, "failed to unmarshal message", logc.Field("err", err), logc.Field("msg", message))
		return err
	}

	if amlCase.MemberId == 0 {
		metrics.IncBackfillAmlCaseTotal(metrics.StatusInvalid)
		logc.Warnw(ctx, "member id is empty", logc.Field("msg", amlCase))
		return nil
	}

	setDefaultIfNeeded(amlCase)

	_, err = l.svcCtx.AmlCaseModel.InsertWithTime(ctx, amlCase)
	if err != nil {
		metrics.IncBackfillAmlCaseTotal(metrics.StatusInsertFailed)
		logc.Errorw(ctx, "failed to insert aml case", logc.Field("err", err), logc.Field("msg", amlCase))
		return err
	}

	metrics.IncBackfillAmlCaseTotal(metrics.StatusSuccess)
	return nil
}

func setDefaultIfNeeded(amlCase *model.AmlCase) {
	if amlCase.RequestId == "" {
		amlCase.RequestId = common.GenerateDefaultRequestID()
	}
	if amlCase.Status == 0 {
		amlCase.Status = 7
	}
	if amlCase.ExternalStatus == 0 {
		amlCase.ExternalStatus = 3
	}
	if amlCase.ExternalProcess == 0 {
		amlCase.ExternalProcess = 1
	}
}
