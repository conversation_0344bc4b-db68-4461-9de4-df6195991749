package config

import (
	"code.bydev.io/frameworks/byone/core/discov/nacos"
	"code.bydev.io/frameworks/byone/core/service"
	"code.bydev.io/frameworks/byone/core/stores/cache"
	"code.bydev.io/frameworks/byone/core/stores/redis"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/kafka"
	"code.bydev.io/frameworks/byone/zrpc"

	"aml-insight/internal/pkg/rn"
)

// RateLimitConfig defines rate limiting configuration
type RateLimitConfig struct {
	Enabled bool    `json:",default=true"` // enable rate limit
	QPS     float64 `json:",default=50.0"` // requests per second
	Burst   int     `json:",default=100"`  // token bucket capacity
}

// BackfillAmlCaseConfig defines configuration for BackfillAmlCase consumer
type BackfillAmlCaseConfig struct {
	RateLimit RateLimitConfig
}

type Config struct {
	service.ServiceConf
	Consumers []struct {
		ID string
		kafka.ConsumerGroupConfig
	}
	Nacos nacos.NacosConf // 用于 rpc nacos inherit

	MySql           sqlx.Config // MySql deprecated: please use AMLInsightMysql
	AMLInsightMysql sqlx.Config // aml_insight db
	RiskAmlMysql    sqlx.Config // risk-aml db
	TiDBCfg         sqlx.Config // tidb db

	Cache    cache.CacheConf
	RNConfig rn.Config

	BizRedis redis.RedisConf

	KycInternalRpc          zrpc.RpcClientConf
	KycServicePrivateClient zrpc.RpcClientConf // Kyc-service-private for amlAPI
	CardManagerRpc          zrpc.RpcClientConf
	CardCenterRpc           zrpc.RpcClientConf
	AppealRpc               zrpc.RpcClientConf
	FiatUserRpc             zrpc.RpcClientConf
	BizConfig               zrpc.RpcClientConf
	RiskControlHubRpc       zrpc.RpcClientConf
	BanInternalClient       zrpc.RpcClientConf // 中台封禁服务
	NotificationClient      zrpc.RpcClientConf // 触达服务
	SiteServiceClient       zrpc.RpcClientConf

	// kafka producer
	FiatProdProducer kafka.ProducerConfig

	// consumer specific configs
	BackfillAmlCase BackfillAmlCaseConfig
}
