// Code generated by byctl, DO NOT EDIT.

package handler

import (
	"aml-insight/consumer/internal/logic"
	"aml-insight/consumer/internal/svc"

	"code.bydev.io/frameworks/byone/core/logx"
	"code.bydev.io/frameworks/byone/core/service"
	"code.bydev.io/frameworks/byone/kafka"
)

func Init(svcCtx *svc.ServiceContext) *service.ServiceGroup {
	var sg = service.NewServiceGroup()
	for _, v := range svcCtx.Config.Consumers {
		config := v
		switch config.ID {
		case "aml_fiat_transaction":
			l := logic.NewAmlFiatTransactionLogic(svcCtx)
			s := kafka.MustNewConsumerGroup(config.ConsumerGroupConfig, l.Consume)
			sg.Add(s)
		case "aml_risknarrative_webhook":
			l := logic.NewAmlRisknarrativeWebhookLogic(svcCtx)
			s := kafka.MustNewConsumerGroup(config.ConsumerGroupConfig, l.Consume)
			sg.Add(s)
		case "aml_appeal_event":
			l := logic.NewFiatPaySecurityTopicLogic(svcCtx)
			s := kafka.MustNewConsumerGroup(config.ConsumerGroupConfig, l.Consume)
			sg.Add(s)
		case "aml.aml_insight.address_label.whitelist":
			l := logic.NewAllAddressLabelLogic(svcCtx)
			s := kafka.MustNewConsumerGroup(config.ConsumerGroupConfig, l.Consume)
			sg.Add(s)
		case "aml.aml_insight.address_label.blacklist":
			l := logic.NewAllAddressLabelLogic(svcCtx)
			s := kafka.MustNewConsumerGroup(config.ConsumerGroupConfig, l.Consume)
			sg.Add(s)
		case "aml.aml_insight.aml_case.backfill":
			l := logic.NewBackfillAmlCaseLogic(svcCtx)
			s := kafka.MustNewConsumerGroup(config.ConsumerGroupConfig, l.Consume)
			sg.Add(s)
		default:
			logx.Alert("Illegal topic: " + config.Topic)
		}
	}

	return sg
}
