package main

import (
	"flag"
	"fmt"

	"code.bydev.io/frameworks/byone/core/conf"

	"aml-insight/consumer/internal/config"
	"aml-insight/consumer/internal/handler"
	"aml-insight/consumer/internal/svc"
)

var configFile = flag.String("f", "etc/consumer.toml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)
	c.MustSetUp()

	ctx := svc.NewServiceContext(c)
	consumer := handler.Init(ctx)
	defer consumer.Stop()

	fmt.Println("Starting consumer service...")
	consumer.Start()
}
