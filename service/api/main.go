package main

import (
	"flag"
	"fmt"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/bybit/fiat/aml/v1"
	aml_insightrpcv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"code.bydev.io/frameworks/byone/core/conf"
	"code.bydev.io/frameworks/byone/zrpc"
	"google.golang.org/grpc"

	"aml-insight/service/api/internal/config"
	"aml-insight/service/api/internal/middleware"
	amlinsightapiServer "aml-insight/service/api/internal/server/amlinsightapi"
	amlinsightrpcapiServer "aml-insight/service/api/internal/server/amlinsightrpcapi"
	"aml-insight/service/api/internal/svc"
)

var configFile = flag.String("f", "etc/api.toml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)
	ctx := svc.NewServiceContext(c)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		aml_insightv1.RegisterAmlInsightAPIServer(grpcServer, amlinsightapiServer.NewAmlInsightAPIServer(ctx))
		aml_insightrpcv1.RegisterAmlInsightRPCAPIServer(grpcServer, amlinsightrpcapiServer.NewAmlInsightRPCAPIServer(ctx))
	})
	defer s.Stop()

	s.AddUnaryInterceptors(
		middleware.GrpcChannelAuth(),
	)

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
