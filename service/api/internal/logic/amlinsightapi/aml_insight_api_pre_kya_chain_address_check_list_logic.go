package amlinsightapilogic

import (
	"aml-insight/internal/pkg/prekya"
	"aml-insight/service/api/internal/middleware"
	"code.bydev.io/frameworks/byone/core/logc"
	"context"
	"git.bybit.com/gtdmicro/stub/pkg/pb/loyalty_program/priv"
	"time"

	"aml-insight/service/api/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/bybit/fiat/aml/v1"
)

type AmlInsightAPIPreKyaChainAddressCheckListLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAPIPreKyaChainAddressCheckListLogic(svcCtx *svc.ServiceContext) *AmlInsightAPIPreKyaChainAddressCheckListLogic {
	return &AmlInsightAPIPreKyaChainAddressCheckListLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAPIPreKyaChainAddressCheckListLogic) PreKyaChainAddressCheckList(ctx context.Context, in *aml_insightv1.PreKyaChainAddressCheckListRequest) (*aml_insightv1.PreKyaChainAddressCheckListResponse, error) {

	var (
		metadata              = ctx.Value("metadata").(*middleware.Metadata)
		memberId              = uint64(metadata.UserId)
		brokerId              = int32(metadata.BrokerId)
		prevSunday            = getLastSundayMidnight()
		prevSundayCount int32 = 0
		resp                  = &aml_insightv1.PreKyaChainAddressCheckListResponse{Result: &aml_insightv1.PreKyaChainAddressCheckList{
			Quota:     &aml_insightv1.Quota{},
			CheckList: make([]*aml_insightv1.PreKyaChainAddressCheckResult, 0),
		}}
	)

	kyaHistory, err := l.svcCtx.PreKyaHistoryModel.QueryKyaHistoryByTime(ctx, memberId, time.Now().AddDate(0, 0, -30).UTC(), time.Now().UTC(), "", "", 100, 0)
	if err != nil {
		return nil, err
	}
	for _, history := range kyaHistory {
		preKyaResultInfo, ok := prekya.PreKyaResultMap[history.Result]
		if !ok {
			logc.Errorw(ctx, "preKyaResultInfo not exist", logc.Field("result", history.Result))
			continue
		}
		if prevSunday.Before(history.CreatedAt) && history.NoError() {
			prevSundayCount++
		}
		resp.Result.CheckList = append(resp.Result.CheckList, &aml_insightv1.PreKyaChainAddressCheckResult{
			Id:              int64(history.Id),
			Chain:           history.Chain,
			Address:         history.Address,
			Result:          history.Result,
			Title:           preKyaResultInfo.Title,
			TitleCkey:       preKyaResultInfo.TitleCkey,
			Description:     preKyaResultInfo.Description,
			DescriptionCkey: preKyaResultInfo.DescriptionCkey,
			CheckTime:       history.CreatedAt.UTC().Unix(),
		})
	}

	vipInfo, err := l.svcCtx.LoyaltyProgramClient.GetVIPLabelInfoLight(ctx, &priv.GetVIPLabelInfoLightRequest{
		UserId:   int64(memberId),
		BrokerId: brokerId,
	})
	if err != nil {
		return nil, err
	}

	if vipInfo.GetLabelType() == int32(priv.LabelType_LABEL_TYPE_VIP) {
		if quota, ok := prekya.VipPreCheckQuota[vipInfo.LabelLevel]; ok {
			resp.Result.Quota.Total = quota
			resp.Result.Quota.Remaining = quota - prevSundayCount
			if resp.Result.Quota.Remaining < 0 {
				resp.Result.Quota.Remaining = 0
			}
		}
	}
	return resp, nil
}

func getLastSundayMidnight() time.Time {
	// 获取当前时间
	now := time.Now().UTC()

	// 计算今天是周几 (星期天是 0，星期一是 1，依此类推)
	offset := int(now.Weekday()) // 周日为 0, 我们希望偏移到上一个周日

	// 获取前一个周日的日期
	lastSunday := now.AddDate(0, 0, -offset)

	// 将时间设置为当天的 00:00:00
	lastSundayMidnight := time.Date(lastSunday.Year(), lastSunday.Month(), lastSunday.Day(), 0, 0, 0, 0, time.UTC)

	return lastSundayMidnight
}
