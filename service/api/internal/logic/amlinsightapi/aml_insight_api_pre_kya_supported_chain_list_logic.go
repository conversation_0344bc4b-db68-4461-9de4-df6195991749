package amlinsightapilogic

import (
	"aml-insight/internal/pkg/prekya"
	"context"

	"aml-insight/service/api/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/bybit/fiat/aml/v1"
)

type AmlInsightAPIPreKyaSupportedChainListLogic struct {
	svcCtx *svc.ServiceContext
}

type SupportChain struct {
	Chain   string
	Display string
}

func NewAmlInsightAPIPreKyaSupportedChainListLogic(svcCtx *svc.ServiceContext) *AmlInsightAPIPreKyaSupportedChainListLogic {
	return &AmlInsightAPIPreKyaSupportedChainListLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAPIPreKyaSupportedChainListLogic) PreKyaSupportedChainList(ctx context.Context, in *aml_insightv1.PreKyaSupportedChainListRequest) (*aml_insightv1.PreKyaSupportedChainListResponse, error) {
	return &aml_insightv1.PreKyaSupportedChainListResponse{
		RetCode:    0,
		RetMsg:     "",
		Result:     &aml_insightv1.SupportChainList{ChainList: prekya.SupportChain},
		RetExtMap:  nil,
		RetExtInfo: nil,
	}, nil
}
