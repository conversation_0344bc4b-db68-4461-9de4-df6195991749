package amlinsightapilogic

import (
	"aml-insight/internal/model"
	"aml-insight/internal/pkg/prekya"
	"aml-insight/pkg/common"
	"aml-insight/pkg/risk"
	"aml-insight/service/api/internal/middleware"
	"aml-insight/service/api/internal/svc"
	"code.bydev.io/frameworks/byone/core/contextx"
	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/core/stores/redis"
	"context"
	"fmt"
	"git.bybit.com/gtdmicro/stub/pkg/pb/loyalty_program/priv"
	"github.com/google/uuid"
	"time"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/bybit/fiat/aml/v1"
)

type AmlInsightAPIPreKyaChainAddressCheckLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAPIPreKyaChainAddressCheckLogic(svcCtx *svc.ServiceContext) *AmlInsightAPIPreKyaChainAddressCheckLogic {
	return &AmlInsightAPIPreKyaChainAddressCheckLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAPIPreKyaChainAddressCheckLogic) PreKyaChainAddressCheck(ctx context.Context, in *aml_insightv1.PreKyaChainAddressCheckRequest) (*aml_insightv1.PreKyaChainAddressCheckResponse, error) {

	resp, err := l.preKyaChainAddressCheckWithError(ctx, in)
	if err != nil {
		logc.Errorw(ctx, "pre kya check error", logc.Field("in", in), logc.Field("resp", resp), logc.Field("err", err))
		return resultResp(ctx, 0, in.GetChain(), in.GetAddress(), prekya.PreKyaResultError, time.Now().UTC()), nil
	}
	if len(resp.Result.Result) == 0 {
		logc.Errorw(ctx, "pre kya check empty", logc.Field("in", in), logc.Field("resp", resp), logc.Field("err", err))
		return resultResp(ctx, 0, in.GetChain(), in.GetAddress(), prekya.PreKyaResultError, time.Now().UTC()), nil
	}
	return resp, nil
}

func (l *AmlInsightAPIPreKyaChainAddressCheckLogic) preKyaChainAddressCheckWithError(ctx context.Context, in *aml_insightv1.PreKyaChainAddressCheckRequest) (*aml_insightv1.PreKyaChainAddressCheckResponse, error) {
	// 分布式锁
	// 查询 VIP 身份
	// 查询 check 次数
	// 存储 case
	// 查询 AML 系统
	// 更新 case
	// 返回结果

	var (
		metadata           = ctx.Value("metadata").(*middleware.Metadata)
		memberId           = uint64(metadata.UserId)
		brokerId           = int32(metadata.BrokerId)
		redisLockKey       = fmt.Sprintf("aml_insight:pre_kya_scan:%d", memberId)
		lock               = redis.NewRedisLock(l.svcCtx.Redis, redisLockKey)
		id           int64 = 0
	)

	ok, err := lock.AcquireWithExpire(ctx, 60)
	if err != nil {
		return nil, err
	}
	if !ok {
		return resultResp(ctx, id, in.GetChain(), in.GetAddress(), prekya.PreKyaResultErrorInProgress, time.Now().UTC()), nil
	}
	defer func() {
		_, err := lock.ReleaseCtx(ctx)
		if err != nil {
			logc.Errorw(ctx, "release redis lock failed", logc.Field("lock_key", redisLockKey))
		}
	}()

	vipInfo, err := l.svcCtx.LoyaltyProgramClient.GetVIPLabelInfoLight(ctx, &priv.GetVIPLabelInfoLightRequest{
		UserId:   int64(memberId),
		BrokerId: brokerId,
	})
	if err != nil {
		return nil, err
	}

	if vipInfo.GetLabelType() != int32(priv.LabelType_LABEL_TYPE_VIP) {
		return resultResp(ctx, id, in.GetChain(), in.GetAddress(), prekya.PreKyaResultErrorNoVIP, time.Now().UTC()), nil
	}

	count, err := l.svcCtx.PreKyaHistoryModel.CountKyaHistoryResultIn(ctx, memberId, getLastSundayMidnight(), time.Now().UTC(), "", prekya.NoErrorResult)
	if err != nil {
		logc.Errorw(ctx, "CountKyaHistoryResultIn error", logc.Field("err", err), logc.Field("req", in))
		return resultResp(ctx, id, in.GetChain(), in.GetAddress(), prekya.PreKyaResultError, time.Now().UTC()), nil
	}
	quota, ok := prekya.VipPreCheckQuota[vipInfo.LabelLevel]
	if !ok {
		logc.Errorw(ctx, "vip pre check quota not found", logc.Field("vip_info", vipInfo))
		return resultResp(ctx, id, in.GetChain(), in.GetAddress(), prekya.PreKyaResultErrorNoQuota, time.Now().UTC()), nil
	}
	if quota <= count {
		return resultResp(ctx, id, in.GetChain(), in.GetAddress(), prekya.PreKyaResultErrorNoQuota, time.Now().UTC()), nil
	}

	preKyaHistory := &model.PreKyaHistory{
		RequestId: fmt.Sprintf("PR-%s", uuid.New().String()),
		MemberId:  memberId,
		Chain:     in.GetChain(),
		Address:   in.GetAddress(),
		Status:    model.PreKyaStatusPending,
		VipType:   vipInfo.LabelType,
		VipLevel:  vipInfo.LabelLevel,
		Result:    "",
	}

	result, err := l.svcCtx.PreKyaHistoryModel.Insert(ctx, preKyaHistory)
	if err != nil {
		return nil, err
	}
	id, err = result.LastInsertId()
	if err != nil {
		return nil, err
	}
	preKyaHistory.Id = uint64(id)

	coin, amount := prekya.GetCoinAmountByChain(in.GetChain())

	ctxWithTimeout, cancel := context.WithTimeout(contextx.ValueOnlyFrom(ctx), time.Second*8)
	defer cancel()
	decisionResp, _, err := l.svcCtx.RiskHttpClient.SendReq(ctxWithTimeout, &risk.Request{
		RequestId:    preKyaHistory.RequestId,
		MemberId:     preKyaHistory.MemberId,
		Chain:        preKyaHistory.Chain,
		Coin:         coin,
		FromAddress:  "",
		ToAddress:    preKyaHistory.Address,
		Tag:          "",
		ActionType:   "",
		Amount:       amount,
		CreateTime:   uint64(time.Now().UTC().Unix()),
		SpendSubType: "",
	})
	if err != nil {
		logc.Errorw(ctx, "riskHttpSendReq error", logc.Field("err", err), logc.Field("request_id", preKyaHistory.RequestId))
		preKyaHistory.Result = prekya.PreKyaResultError
	} else {
		logc.Infow(ctx, "decisionResp", logc.Field("decision_resp", decisionResp))
		if decisionResp == nil || len(decisionResp.Conclusion) == 0 {
			logc.Errorw(ctx, "decisionResp empty", logc.Field("decision_resp", decisionResp))
			preKyaHistory.Result = prekya.PreKyaResultError
		} else if decisionResp.Conclusion == common.DECISION_ATYPE_REJECT || decisionResp.Conclusion == common.DECISION_ATYPE_REJECT_AML {
			preKyaHistory.Result = prekya.PreKyaResultHighRisk
		} else {
			preKyaHistory.Result = prekya.PreKyaResultLowRisk
		}
	}

	preKyaHistory.Status = model.PreKyaStatusSuccess
	err = l.svcCtx.PreKyaHistoryModel.Update(ctx, preKyaHistory)
	if err != nil {
		return nil, err
	}

	return resultResp(ctx, id, in.GetChain(), in.GetAddress(), preKyaHistory.Result, time.Now().UTC()), nil
}

func resultResp(ctx context.Context, id int64, chain, address string, result string, checkTime time.Time) *aml_insightv1.PreKyaChainAddressCheckResponse {
	resp := &aml_insightv1.PreKyaChainAddressCheckResponse{Result: &aml_insightv1.PreKyaChainAddressCheckResult{
		Id:              id,
		Chain:           chain,
		Address:         address,
		Result:          "",
		Title:           "",
		TitleCkey:       "",
		Description:     "",
		DescriptionCkey: "",
		CheckTime:       checkTime.UTC().Unix(),
	}}
	preKyaResult, ok := prekya.PreKyaResultMap[result]
	if !ok {
		logc.Warnf(ctx, "prekya result not found for result: %s", result)
		return resp
	}
	resp.Result.Result = result
	resp.Result.Title = preKyaResult.Title
	resp.Result.TitleCkey = preKyaResult.TitleCkey
	resp.Result.Description = preKyaResult.Description
	resp.Result.DescriptionCkey = preKyaResult.DescriptionCkey
	return resp
}
