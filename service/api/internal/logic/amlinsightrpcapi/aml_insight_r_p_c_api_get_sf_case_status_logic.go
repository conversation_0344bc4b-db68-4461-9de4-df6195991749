package amlinsightrpcapilogic

import (
	"context"
	"strconv"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	appealv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/appeal/v1"
	"code.bydev.io/frameworks/byone/core/logc"
	"github.com/pkg/errors"

	"aml-insight/internal/model"
	"aml-insight/internal/pkg/event"
	"aml-insight/pkg/common"
	"aml-insight/service/api/internal/svc"
)

type AmlInsightRPCAPIGetSfCaseStatusLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightRPCAPIGetSfCaseStatusLogic(svcCtx *svc.ServiceContext) *AmlInsightRPCAPIGetSfCaseStatusLogic {
	return &AmlInsightRPCAPIGetSfCaseStatusLogic{
		svcCtx: svcCtx,
	}
}

// GetSfCaseStatus Get salesforce case status
func (l *AmlInsightRPCAPIGetSfCaseStatusLogic) GetSfCaseStatus(ctx context.Context, in *aml_insightv1.GetSfCaseStatusRequest) (*aml_insightv1.GetSfCaseStatusResponse, error) {
	resp := &aml_insightv1.GetSfCaseStatusResponse{
		Result: &aml_insightv1.GetSfCaseStatusResponse_Result{
			RequestId:  in.GetRequestId(),
			CaseStatus: common.AMLCaseStatusUnknown,
		},
	}

	sfCases, err := l.svcCtx.SfCaseModel.FindListByRequestIdOrMemberId(ctx, in.GetRequestId(), 0, 1, 1)
	if err != nil {
		logc.Errorw(ctx, "FindListByRequestIdOrMemberId error",
			logc.Field("err", err), logc.Field("request_id", in.RequestId))
		resp.Code = 500 // Internal Server Error
		resp.Msge = err.Error()
		return resp, nil
	}
	if len(sfCases) == 0 {
		return resp, nil
	}

	sfCase := sfCases[0]
	if newCase, appealStatus, err := l.processSyncAppealToAML(ctx, sfCase); err != nil {
		logc.Errorw(ctx, "processSyncAppealToAML error", logc.Field("err", err), logc.Field("sfCase", sfCase))
		resp.Result.CaseStatus = common.ConvertAssetCaseStatus(sfCase, "")
	} else {
		sfCase = newCase
		resp.Result.CaseStatus = common.ConvertAssetCaseStatus(sfCase, appealStatus)
	}

	// return msg
	resp.Result.RequestId = sfCase.RequestId
	resp.Result.GroupId = sfCase.GroupId
	//resp.Result.CaseStatus = caseStatus
	resp.Result.WorkOrderChannel = sfCase.WorkOrderChannel
	resp.Result.WebEddLink = sfCase.WebEddLink
	resp.Result.AppEddLink = sfCase.AppEddLink
	return resp, nil
}

// processSyncAppealToAML 把工单的状态同步到 AML
func (l *AmlInsightRPCAPIGetSfCaseStatusLogic) processSyncAppealToAML(ctx context.Context,
	sfCase *model.SfCase) (*model.SfCase, string, error) {
	uid, err := strconv.ParseInt(sfCase.MainAccountUid, 10, 64)
	if err != nil {
		return nil, "", errors.WithMessage(err, "strconv.ParseInt error")
	}
	appealResp, err := l.svcCtx.AppealClient.GetAppealDetailForInternal(ctx, &appealv1.GetAppealDetailForInternalRequest{
		AppealId: sfCase.GroupId,
		Uid:      uid,
	})
	if err != nil {
		return nil, "", errors.WithMessage(err, "AppealDetail error")
	}
	//appealResp.GetAppealDetail()
	if appealResp.GetError().GetCode() != 0 {
		return nil, "", errors.New("appeal error" + appealResp.GetError().GetMessage())
	}
	appealDetail := appealResp.GetAppealDetail()
	if appealDetail == nil || appealDetail.GetAppealId() == "" {
		return nil, "", errors.New("appealDetail is empty")
	}

	// 空的不处理
	if appealDetail.GetBizAppealStatus() == "" {
		return nil, "", errors.New("biz_appeal_status is empty")
	}
	sfCase.SfStatus = appealDetail.GetBizAppealStatus()
	sfCase.AmlFlow = int32(appealDetail.GetAmlFlow())
	if err := l.svcCtx.SfCaseModel.Update(ctx, sfCase); err != nil {
		logc.Errorw(ctx, "Update error", logc.Field("err", err))
	}
	l.processSendMsgToCollectionAndDw(ctx, sfCase)
	return sfCase, appealDetail.GetStatus(), nil
}

// processSendMsgToCollectionAndDw 判断是否需要发送消息，这块代码逻辑与
// 需要是充值的请求；只有 KYA & STR 才需要发送；只有到底终态的才需要发送。
func (l *AmlInsightRPCAPIGetSfCaseStatusLogic) processSendMsgToCollectionAndDw(ctx context.Context, sfCase *model.SfCase) {
	amlCase, err := l.svcCtx.AmlCaseModel.FindOneByRequestId(ctx, sfCase.RequestId)
	if err != nil {
		logc.Errorw(ctx, "processSendMsgToCollectionAndDw failed error", logc.Field("err", err))
		return
	}
	if amlCase.ActionType != 1 { // 只有充值的需要处理, actionType, 1: 充值，2: 提现
		// 忽略非充值的 case
		return
	}

	// SF_CASE_KYA_NEW       = 21
	// SF_CASE_KYA_EMPTY_NEW = 22
	// SF_CASE_KYT_NEW       = 23
	// SF_CASE_KYT_EMPTY_NEW = 24
	// SF_CASE_MANUAL_NEW    = 25
	// SF_CASE_STR_NEW       = 27
	switch sfCase.CaseType {
	case 21, 22, 26, 27:
	default: // 其他的资产都没有冻结的资金。
		return
	}

	if sfCase.SfStatus != "Solved" {
		return
	}

	switch sfCase.AmlFlow {
	case 6, 7, 8:
	default: // 非终态的不需要发送
		return
	}

	// 发送资金归集解冻消息
	event.DoSendRiskGatewayProxyMsg(ctx, l.svcCtx.FiatProdProducer, sfCase, int(amlCase.TxIndex))
}
