package svc

import (
	appealv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/appeal/v1"
	"code.bydev.io/frameworks/byone/core/stores/redis"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/kafka"
	"code.bydev.io/frameworks/byone/zrpc"
	"git.bybit.com/gtdmicro/stub/pkg/pb/loyalty_program/priv"

	"aml-insight/internal/model"
	"aml-insight/pkg/risk"
	"aml-insight/service/api/internal/config"
)

type ServiceContext struct {
	Config config.Config

	Redis *redis.Redis

	Mysql sqlx.Config

	PreKyaHistoryModel model.PreKyaHistoryModel
	SfCaseModel        model.SfCaseModel
	AmlCaseModel       model.AmlCaseModel

	LoyaltyProgramClient priv.LoyaltyProgramClient
	AppealClient         appealv1.AppealAPIClient

	// 集团风控调用 http client
	RiskHttpClient risk.Client

	// kafka
	FiatProdProducer kafka.Producer
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config: c,
		Redis:  c.RedisConf.NewRedis(),

		PreKyaHistoryModel: model.MustNewPreKyaHistoryModel(c.RiskAmlMysql),
		SfCaseModel:        model.MustNewSfCaseModel(c.RiskAmlMysql),
		AmlCaseModel:       model.MustNewAmlCaseModel(c.RiskAmlMysql),

		LoyaltyProgramClient: priv.NewLoyaltyProgramClient(zrpc.MustNewClient(c.LoyaltyClient).Conn()),
		AppealClient:         appealv1.NewAppealAPIClient(zrpc.MustNewClient(c.AppealClient).Conn()),
		// 集团风控 http client
		RiskHttpClient: risk.MustNewClient(c.RiskHttpClient),

		// kafka
		FiatProdProducer: kafka.MustNewProducer(c.FiatProdProducer),
	}

}
