package config

import (
	"code.bydev.io/frameworks/byone/core/stores/redis"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/kafka"
	"code.bydev.io/frameworks/byone/zrpc"

	"aml-insight/pkg/risk"
)

type Config struct {
	zrpc.RpcServerConf

	RedisConf redis.RedisConf

	//AMLInsightMysql sqlx.Config // aml_insight db
	RiskAmlMysql sqlx.Config // risk-aml db

	// rpc
	LoyaltyClient zrpc.RpcClientConf
	AppealClient  zrpc.RpcClientConf // need bything

	// business conf
	BusinessConf BusinessConf

	RiskHttpClient risk.Config

	FiatProdProducer kafka.ProducerConfig
}

type (
	BusinessConf struct {
		//PreKyaConf PreKyaConf
	}
	//
	//PreKyaConf struct {
	//	SupportChain
	//}
	//
	//<PERSON><PERSON>hain struct {
	//	Chain   string
	//	Display string // 显示名称
	//}
)
