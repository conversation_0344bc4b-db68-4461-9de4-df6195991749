package middleware

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"git.bybit.com/svc/stub/pkg/pb/api/geoip"
	"github.com/google/uuid"
	"github.com/spf13/cast"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"
)

type MetadataContextKey struct{}

type Metadata struct {
	metadata.MD
	UserId          int64             `json:"memberid,omitempty"`
	FiatUserId      int64             `json:"fuid,omitempty"`
	PlatformId      string            `json:"platform_id,omitempty"`
	AccountId       int64             `json:"accountid,omitempty"`
	RequestTime     int64             `json:"reqtime,omitempty"` //time.UnixNano
	TraceId         string            `json:"traceid,omitempty"`
	UA              string            `json:"user-agent,omitempty"`
	Extension       MetadataExtension `json:"extension,omitempty"`
	Header          map[string]string `json:"header,omitempty"`
	Cookie          map[string]string `json:"cookie,omitempty"`
	LabelEnv        string            `json:"label_env"`               // env label
	LabelTraffic    string            `json:"label_traffic"`           // traffic label
	SubVisionId     int64             `json:"sub_vision_id,omitempty"` // 省份ID，配置geo过滤器才生效
	UserNamespace   string            `json:"user_namespace"`
	BrokerId        int64             `json:"broker_id,omitempty"`
	SiteId          string            `json:"user-site-id,omitempty"`
	RefSiteId       string            `json:"x-refer-site-id,omitempty"`
	MerchantId      string            `json:"-"`                     // 商户id
	MerchantName    string            `json:"-"`                     // 商户名称
	IsInnerMerchant bool              `json:"-"`                     // 是否内部商户,内部商户跟主站用一套清结算配置
	ProfileId       string            `json:"-"`                     // 权益级别
	SiteConfig      string            `json:"site_config,omitempty"` // 站点配置
	ErrArgs         map[string]string `json:"-"`                     // 错误参数
}

type MetadataExtension struct {
	RemoteIP         string  `json:"ip,omitempty"`
	Platform         string  `json:"pf,omitempty"`
	OpPlatform       string  `json:"opf,omitempty"`
	NextToken        *string `json:"-"`             // token refresh
	ReqExpireTime    string  `json:"exp,omitempty"` // for mixer trading
	AppVersion       string  `json:"ver,omitempty"`
	Referer          string  `json:"ref,omitempty"`
	CountryISO       string  `json:"ctry_iso,omitempty"`
	CountryISO3      string  `json:"ctry_iso3,omitempty"`
	CurrencyCode     string  `json:"fiat,omitempty"`
	CountryGeoNameID int64   `json:"ctry_id,omitempty"`
	CityName         string  `json:"ct_name,omitempty"`
	CityGeoNameID    int64   `json:"ct_id,omitempty"`
	Language         string  `json:"lang,omitempty"`
	DeviceID         string  `json:"dev_id,omitempty"`
}

func GrpcChannelAuth() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (

		resp any, err error) {
		// parse metadata from bgw
		md, _ := ParseMetadata(ctx)

		md.UserId = md.GetUserId()
		md.BrokerId = md.GetBrokerId()
		md.SiteId = md.GetSiteId()
		md.RefSiteId = md.GetRefSiteId()

		ctx = context.WithValue(ctx, "metadata", md)

		return handler(ctx, req)
	}
}

func ParseMetadata(ctx context.Context) (*Metadata, bool) {
	md, b := metadata.FromIncomingContext(ctx)

	desensitizeMetadataHeader(&md)

	return &Metadata{MD: md}, b
}

func (m *Metadata) GetUserId() int64 {
	if m.UserId > 0 {
		return m.UserId
	}

	v := m.Get("memberid")
	if len(v) > 0 {
		uid, _ := strconv.ParseInt(v[0], 10, 64)
		m.UserId = uid
		return uid
	}
	return 0
}

func (m *Metadata) GetFiatUserId() int64 {
	if m.FiatUserId > 0 {
		return m.FiatUserId
	}

	v := m.Get("fuid")
	if len(v) > 0 {
		uid, _ := strconv.ParseInt(v[0], 10, 64)
		m.FiatUserId = uid
		return uid
	}
	return 0
}

func (m *Metadata) SetFiatUserId(fuid int64) {
	m.Set("fuid", strconv.FormatInt(fuid, 10))
	m.FiatUserId = fuid
}

func (m *Metadata) GetPlatformId() string {
	if m.PlatformId != "" {
		return m.PlatformId
	}

	v := m.Get("platformid")
	if len(v) > 0 {
		m.PlatformId = v[0]
		return v[0]
	}
	return ""
}

func (m *Metadata) SetPlatformId(platformid string) {
	m.PlatformId = platformid
}

func (m *Metadata) IsAuth() bool {
	return m.GetUserId() > 0
}

func (m *Metadata) GetAccountID() int64 {
	v := m.Get("accountid")
	if len(v) > 0 {
		aid, _ := strconv.ParseInt(v[0], 10, 64)
		m.AccountId = aid
		return aid
	}
	return 0
}

func (m *Metadata) GetProfileId() string {
	return m.ProfileId
}

func (m *Metadata) SetProfileId(profileId string) {
	m.ProfileId = profileId
}

func (m *Metadata) GetRequestTime() int64 {
	v := m.Get("reqtime")
	if len(v) > 0 {
		r, _ := strconv.ParseInt(v[0], 10, 64)
		m.RequestTime = r
		return r
	}
	return 0
}

func (m *Metadata) GetExtension() MetadataExtension {
	var ext MetadataExtension
	v := m.Get("extension")
	if len(v) > 0 {
		_ = json.Unmarshal([]byte(v[0]), &ext)
		// app使用英语的国家使用en，现阶段由收银台转
		if ext.Language == "en-US" {
			ext.Language = "en"
		}
		m.Extension = ext
		return ext
	}
	return ext
}

func (m *Metadata) GetTraceID() string {
	v := m.Get("traceid")
	if len(v) > 0 {
		m.TraceId = v[0]
		return v[0]
	}
	return ""
}

func (m *Metadata) GetRequestID() string {
	return uuid.NewString()
}

func (m *Metadata) GetUA() string {
	v := m.Get("user-agent")
	if len(v) > 0 {
		m.UA = v[0]
		return v[0]
	}
	return ""
}

func (m *Metadata) GetCookie(k string) string {
	if m.Cookie == nil {
		m.Cookie = make(map[string]string)

		v := m.Get("cookie")
		if len(v) > 0 {
			_ = json.Unmarshal([]byte(v[0]), &m.Cookie)
		}
	}

	if len(m.Cookie) > 0 {
		if value, ok := m.Cookie[k]; ok {
			return value
		}
	}

	return ""
}

func (m *Metadata) GetCountry() *geoip.GeonameCountry {
	e := m.GetExtension()
	if (MetadataExtension{}) == e {
		return &geoip.GeonameCountry{}
	}

	return &geoip.GeonameCountry{
		Iso:          e.CountryISO,
		Iso_3:        e.CountryISO3,
		CurrencyCode: e.CurrencyCode,
		Geonameid:    e.CountryGeoNameID,
	}
}

func (m *Metadata) GetHeader(k string) string {
	if m.Header == nil {
		m.Header = make(map[string]string)

		v := m.Get("header")
		if len(v) > 0 {
			_ = json.Unmarshal([]byte(v[0]), &m.Header)
		}
	}

	if len(m.Header) > 0 {
		if value, ok := m.Header[k]; ok {
			return value
		}
	}

	return ""
}

func (m *Metadata) GetUserNamespace() string {
	v := m.Get("user_namespace")
	if len(v) > 0 {
		m.UA = v[0]
		return v[0]
	}
	return ""
}

func (m *Metadata) GetSubVisionId() int64 {
	v := m.Get("sub_vision_id")
	if len(v) > 0 {
		m.SubVisionId = cast.ToInt64(v[0])
		return m.SubVisionId
	}
	return 0
}

func (m *Metadata) GetBrokerId() int64 {
	v := m.Get("broker_id")
	if len(v) > 0 {
		m.BrokerId = cast.ToInt64(v[0])
		return m.BrokerId
	}
	return 0
}

func (m *Metadata) GetMerchantId() string {
	return m.MerchantId
}

func (m *Metadata) SetMerchantId(merchantId string) {
	m.MerchantId = merchantId
}

func (m *Metadata) GetSiteId() string {
	v := m.Get("user-site-id")
	if len(v) > 0 {
		m.SiteId = v[0]
		return m.SiteId
	}
	return ""
}

func (m *Metadata) GetRefSiteId() string {
	v := m.Get("x-refer-site-id")
	if len(v) > 0 {
		m.RefSiteId = v[0]
		return m.RefSiteId
	}
	return ""
}

func (m *Metadata) GetSiteConfig() string {
	v := m.Get("site_config")
	if len(v) > 0 {
		m.SiteConfig = v[0]
		return m.SiteConfig
	}
	return ""
}

// 脱敏header敏感字段
func desensitizeMetadataHeader(md *metadata.MD) {
	v := md.Get("header")
	md.Delete("usertoken")
	if len(v) > 0 {
		target := make(map[string]string)
		_ = json.Unmarshal([]byte(v[0]), &target)
		if userToken, ok := target["usertoken"]; ok {
			h := md5.New()
			h.Write([]byte(userToken))
			target["session_id"] = hex.EncodeToString(h.Sum(nil))
			delete(target, "usertoken")
			dest, _ := json.Marshal(target)
			md.Set("header", string(dest))
		}
	}
}
