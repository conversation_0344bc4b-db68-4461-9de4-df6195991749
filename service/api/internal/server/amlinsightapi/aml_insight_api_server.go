// Code generated by byctl. DO NOT EDIT!
// Source: https://code.bydev.io/cht/fiat/backend/bufmodule/-/blob/feat/additional_kya_scan/fiat/channel/aml_insight/v1/aml_insight_api.proto

package server

import (
	"context"

	amlinsightapilogic "aml-insight/service/api/internal/logic/amlinsightapi"
	"aml-insight/service/api/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/bybit/fiat/aml/v1"
)

type AmlInsightAPIServer struct {
	svcCtx *svc.ServiceContext
	aml_insightv1.UnimplementedAmlInsightAPIServer
}

func NewAmlInsightAPIServer(svcCtx *svc.ServiceContext) *AmlInsightAPIServer {
	return &AmlInsightAPIServer{
		svcCtx: svcCtx,
	}
}

// option (bgw.v1.service_options) = {
func (s *AmlInsightAPIServer) PreKyaSupportedChainList(ctx context.Context, in *aml_insightv1.PreKyaSupportedChainListRequest) (*aml_insightv1.PreKyaSupportedChainListResponse, error) {
	l := amlinsightapilogic.NewAmlInsightAPIPreKyaSupportedChainListLogic(s.svcCtx)
	return l.PreKyaSupportedChainList(ctx, in)
}

func (s *AmlInsightAPIServer) PreKyaChainAddressCheck(ctx context.Context, in *aml_insightv1.PreKyaChainAddressCheckRequest) (*aml_insightv1.PreKyaChainAddressCheckResponse, error) {
	l := amlinsightapilogic.NewAmlInsightAPIPreKyaChainAddressCheckLogic(s.svcCtx)
	return l.PreKyaChainAddressCheck(ctx, in)
}

func (s *AmlInsightAPIServer) PreKyaChainAddressCheckList(ctx context.Context, in *aml_insightv1.PreKyaChainAddressCheckListRequest) (*aml_insightv1.PreKyaChainAddressCheckListResponse, error) {
	l := amlinsightapilogic.NewAmlInsightAPIPreKyaChainAddressCheckListLogic(s.svcCtx)
	return l.PreKyaChainAddressCheckList(ctx, in)
}
