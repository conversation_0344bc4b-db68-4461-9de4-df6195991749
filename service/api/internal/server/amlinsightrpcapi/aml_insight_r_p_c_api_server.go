// Code generated by byctl. DO NOT EDIT!
// Source: https://code.bydev.io/cht/fiat/backend/bufmodule/-/blob/feat/aml_onchain_edd/fiat/channel/aml_insight/v1/aml_insight.proto

package server

import (
	"context"

	"aml-insight/service/api/internal/logic/amlinsightrpcapi"
	"aml-insight/service/api/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightRPCAPIServer struct {
	svcCtx *svc.ServiceContext
	aml_insightv1.UnimplementedAmlInsightRPCAPIServer
}

func NewAmlInsightRPCAPIServer(svcCtx *svc.ServiceContext) *AmlInsightRPCAPIServer {
	return &AmlInsightRPCAPIServer{
		svcCtx: svcCtx,
	}
}

// GetSfCaseStatus Get salesforce case status
func (s *AmlInsightRPCAPIServer) GetSfCaseStatus(ctx context.Context, in *aml_insightv1.GetSfCaseStatusRequest) (*aml_insightv1.GetSfCaseStatusResponse, error) {
	l := amlinsightrpcapilogic.NewAmlInsightRPCAPIGetSfCaseStatusLogic(s.svcCtx)
	return l.GetSfCaseStatus(ctx, in)
}
