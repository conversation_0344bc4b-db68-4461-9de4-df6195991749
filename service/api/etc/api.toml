Name = "aml-insight-api"
ListenOn = ":9102"
Mode = "dev"
Timeout = 3000

[[MethodTimeout]]
	Methods=['/bybit.fiat.aml.v1.AmlInsightAPI/PreKyaChainAddressCheck']
	Timeout=10000

[Nacos]
Key = "aml-insight-api"
NamespaceId = '##{MY_PROJECT_ENV_NAME}##'
Username = "bybit-nacos"
Password = "bybit-nacos"
[[Nacos.ServerConfigs]]
Address = "nacos.test.infra.ww5sawfyut0k.bitsvc.io:8848"

[Mysql]
DataSource = "app_user:PLO75FbcfmFYRuQEGmygZ9PyQCQbmgeD5@tcp(devtest-risk-common-mysql-cluster.master.devtest-storage.ww5sawfyut0k.bitsvc.io:3306)/risk_aml?charset=utf8mb4&parseTime=true&loc=Local&timeout=10s&readTimeout=10s&writeTimeout=10s"
DataSourceSlave = "app_user:PLO75FbcfmFYRuQEGmygZ9PyQCQbmgeD5@tcp(devtest-risk-common-mysql-cluster.master.devtest-storage.ww5sawfyut0k.bitsvc.io:3306)/risk_aml?charset=utf8mb4&parseTime=true&loc=Local&timeout=10s&readTimeout=10s&writeTimeout=10s"

[RedisConf]
    Host = "***********:6378"

[LoyaltyClient]
Timeout = 5000
NonBlock = true

[LoyaltyClient.Nacos]
Username = "bybit-nacos"
Password = "bybit-nacos"
NamespaceId = "unify-test-1"
Key = "loyalty-program-private-serv"
Group = "DEFAULT_GROUP"

[[LoyaltyClient.Nacos.ServerConfigs]]
Address = "nacos.test.infra.ww5sawfyut0k.bitsvc.io:8848"


[RiskHttpClient]
Name = "cht_fiat"
Addr = "http://asset-apisix.test.efficiency.ww5sawfyut0k.bitsvc.io"
#Addr = "http://x-lab.devtest.ww5sawfyut0k.bitsvc.io/mock/##{SVC_REGISTER_NAMESPACE}##/security"
AppID = "FIAT2403"
AccessKey = "69663C24B86FB"
SecretKey = "C621C68CBDFDE"
PrivateKey = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIFGczsQ/slWDvf+U2fq6is40a6NyfUe3aQbVO7pNXaTs
-----END PRIVATE KEY-----"""
Breaker = true