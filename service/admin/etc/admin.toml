Name = "aml-insight-admin"
ListenOn = ":9102"
Mode = "dev"
Timeout = 10000

[Nacos]
Key = "aml-insight-admin"
NamespaceId = "unify-dev-1"
Username = "bybit-nacos"
Password = "bybit-nacos"
[[Nacos.ServerConfigs]]
Address = "nacos.test.infra.ww5sawfyut0k.bitsvc.io:8848"

[Mysql]
DataSource = "app_user:PLO75FbcfmFYRuQEGmygZ9PyQCQbmgeD5@tcp(devtest-risk-common-mysql-cluster.master.devtest-storage.ww5sawfyut0k.bitsvc.io:3306)/risk_aml?charset=utf8mb4&parseTime=true&loc=Local&timeout=10s&readTimeout=10s&writeTimeout=10s"

[AMLInsightMysql]
Datasource = "app_user:PLO75FbcfmFYRuQEGmygZ9PyQCQbmgeD5@tcp(devtest-risk-common-mysql-cluster.master.devtest-storage.ww5sawfyut0k.bitsvc.io:3306)/aml_insight?charset=utf8mb4&parseTime=true&loc=Local&timeout=10s&readTimeout=10s&writeTimeout=10s"

[SyncAddrLabelMysql]
DataSource = "app_user:PLO75FbcfmFYRuQEGmygZ9PyQCQbmgeD5@tcp(devtest-risk-common-mysql-cluster.master.devtest-storage.ww5sawfyut0k.bitsvc.io:3306)/risk_aml?charset=utf8mb4&parseTime=true&loc=Local&timeout=10s&readTimeout=10s&writeTimeout=10s"

# 此处为数据库类型加解密
[StoreSecConfig]
	host = "**********:9091"
    app_name = "fiat-pay-public-_test"
    #app_id = "ded2f7bf-58b8-4686-81a3-03efd298e77b"
	app_id = ""
    app_sign_key = "RVxVXUAJRVdRWh5aRlwLAxxUG1ZWA0xTQVBbWR9SRlU="
    tls_cert  = "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"

[BizRedis]
Host = "***********:6378"

[ByData]
Host = "http://bydata-interface-data-eyes-test.test.efficiency.ww5sawfyut0k.bitsvc.io"
#AppId = "pDGM8kNziyUVyXvZbEYM"
#AppSecret = "SsE3tDgSbXeZZuJxbxW7SnPaYeSwFZNKPAGe8GB4"
AppId = "vd7SIdEWk2fXEfJ27jn6"
AppSecret = "Cfb4jpbmpypFFxNEM7FnrdwnycVEGPSUs4j5dQcJ"

[AddressLabelAutoSync]
ByDataTableName = "cdm.sanctions_tag_library"
ExtendedAddressLabelLarkAlertUrl = "https://open.larksuite.com/open-apis/bot/v2/hook/7a121556-7068-47e6-99a1-300e0f1c1bcf"
ExtendedAddressLabelLarkAlertUrlAt = ["243f9b1f","8457f8c6"]

[OklinkCfg]
Name = "oklink"
Addr = "https://www.oklink.com"
APIKey = "76f621c1-da71-4c24-a05e-abc56b200ac5"
Debug = true
EnableMetricInterceptor = true
RateLimit = 2
RateBurst = 3

#[SecurityClient]
#Timeout = 4000
#[SecurityClient.Nacos]
#Key = "pay-security"
#[SecurityClient.Middlewares.Logger]
#Enabled = true
#LogReq = true
#LogResp = true

JobWorkerPoolSize = 5

[ZcronConf]
Name = "aml-insight-admin"
[ZcronConf.XxlJobConfig]
Namespace = "pubilc"
[[ZcronConf.Jobs]]
  CronType = "xxljob"
  Name = "addressLabelSyncTaskCreate"
[[ZcronConf.Jobs]]
  CronType = "xxljob"
  Name = "addressLabelSyncQuery"
[[ZcronConf.Jobs]]
  CronType = "xxljob"
  Name = "syncRealtimeTx"
[[ZcronConf.Jobs]]
  CronType = "xxljob"
  Name = "syncHistoricalTx"
[[ZcronConf.Jobs]]
  CronType = "xxljob"
  Name = "syncHackTx"
[[ZcronConf.Jobs]]
  CronType = "xxljob"
  Name = "syncHackTxFull"