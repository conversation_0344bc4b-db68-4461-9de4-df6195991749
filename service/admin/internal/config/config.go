package config

import (
	"code.bydev.io/frameworks/byone/kafka"

	"aml-insight/internal/pkg/crawl"
	"aml-insight/internal/pkg/oklink"
	"aml-insight/internal/pkg/rpc"
	"aml-insight/pkg/bydata"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/client/bhttpclient"
	"code.bydev.io/frameworks/byone/core/stores/redis"
	"code.bydev.io/frameworks/byone/core/stores/sqlx"
	"code.bydev.io/frameworks/byone/zcron"
	"code.bydev.io/frameworks/byone/zrpc"
	"code.bydev.io/frameworks/sechub-go/client"
)

type Config struct {
	zrpc.RpcServerConf
	Mysql              sqlx.Config
	AMLInsightMysql    sqlx.Config
	SyncAddrLabelMysql sqlx.Config // sync_address_label mysql address
	TiDBCfg            sqlx.Config // tidb config

	BizRedis *redis.RedisConf

	// xxljob 配置
	ZcronConf         zcron.CronConf
	JobWorkerPoolSize int `json:",default=5"`

	ByData               bydata.ByDataConf
	AddressLabelAutoSync AddressLabelAutoSync
	HackTxConfig         HackTxConfig

	// rpc
	KycInternalClient       zrpc.RpcClientConf // bything
	FiatUserClient          zrpc.RpcClientConf // fiat-user: fiat-user-provider
	QuoteClient             zrpc.RpcClientConf // pay-quote
	AssetShowInternalClient zrpc.RpcClientConf // asset-show-service
	FiatCardManagerClient   zrpc.RpcClientConf // fiat-card-manager
	PayBizConfig            zrpc.RpcClientConf // pay-biz-config
	AppealAdminClient       zrpc.RpcClientConf // appeal-admin-service

	StoreSecConfig client.Config

	// oklink client
	OklinkCfg oklink.Config

	// thornode client
	ThorNodeCfg rpc.Config

	// switch for address label db
	AddressLabelDBSwitch AddressLabelDBSwitch

	// kafka producer
	FiatProdProducer kafka.ProducerConfig

	// wallet explorer
	WalletExplorerCfg WalletExplorerConfig

	// photoSharing key
	PhotoSharingKey string
}

type AddressLabelAutoSync struct {
	ByDataTableName                    string
	ExtendedAddressLabelLarkAlertUrl   string
	ExtendedAddressLabelLarkAlertUrlAt []string
}

type HackTxConfig struct {
	bhttpclient.Config
	HackAddressURI string `json:",default=/public/hack-address.json"`
}

type AddressLabelDBSwitch struct {
	EnableReadTiDB    bool `json:",default=false"`
	DisableWriteMysql bool `json:",default=false"`
}

type WalletExplorerConfig struct {
	WebhookURL string   `json:",default=https://open.larksuite.com/open-apis/bot/v2/hook/8cd38dda-45ab-493e-8c20-fcaa9e00f522"`
	AtUsers    []string `json:",default=[\"56c5f451\",\"8457f8c6\"]"`
	crawl.WalletExplorerCrawlConfig
}
