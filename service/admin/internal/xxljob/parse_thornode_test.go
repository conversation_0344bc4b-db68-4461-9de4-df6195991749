package xxljob

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"
	"time"

	"aml-insight/internal/pkg/rpc"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/client/bhttpclient"
)

type mockTransport struct {
	responses map[string]*http.Response
}

func (m *mockTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	url := req.URL.String()
	if res, ok := m.responses[url]; ok {
		return res, nil
	}
	return nil, fmt.Errorf("unexpected request to: %s", url)
}

func createMockClient(responses map[string]*http.Response) *http.Client {
	return &http.Client{
		Transport: &mockTransport{responses: responses},
		Timeout:   1 * time.Second,
	}
}

func TestParseThorNode(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name          string
		txID          string
		mockResponses map[string][]byte
		want          ThorNodeOutBound
		wantErr       bool
	}{
		{
			name: "single action valid",
			txID: "single123",
			mockResponses: map[string][]byte{
				"https://thornode.ninerealms.com/thorchain/tx/details/single123": []byte(`{
					"actions": [{
						"chain": "BTC",
						"to_address": "bc1qaddress",
						"coin": {"asset": "BTC.BTC", "amount": "150000000"}
					}],
					"out_txs": [{"id": "btc-txid", "chain": "BTC"}]
				}`),
			},
			want: ThorNodeOutBound{
				ToChainType:    "BTC",
				AssetChainCoin: "BTC.BTC",
				ToChainAddr:    "bc1qaddress",
				ToChainTxHash:  "btc-txid",
				ChainAmount:    "1.5",
			},
		},
		{
			name: "memo out case",
			txID: "memo123",
			mockResponses: map[string][]byte{
				"https://thornode.ninerealms.com/thorchain/tx/details/memo123": []byte(`{
					"tx": {"tx": {"memo": "OUT:1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"}}
				}`),
				"https://thornode.ninerealms.com/thorchain/tx/details/1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef": []byte(`{
					"actions": [{
						"chain": "ETH",
						"to_address": "0xaddress",
						"coin": {"asset": "ETH.ETH", "amount": "123456789"}
					}],
					"out_txs": [{"id": "eth-txid", "chain": "ETH"}]
				}`),
			},
			want: ThorNodeOutBound{
				ToChainType:    "ETH",
				AssetChainCoin: "ETH.ETH",
				ToChainAddr:    "0xaddress",
				ToChainTxHash:  "eth-txid",
				ChainAmount:    "1.23456789",
			},
		},
		{
			name:    "invalid tx id",
			txID:    "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock响应
			responses := make(map[string]*http.Response)
			for url, body := range tt.mockResponses {
				responses[url] = &http.Response{
					StatusCode: http.StatusOK,
					Body:       io.NopCloser(bytes.NewReader(body)),
					Header:     http.Header{"Content-Type": []string{"application/json"}},
				}
			}
			cli := rpc.NewClient(rpc.Config{
				Config: bhttpclient.Config{
					ReadTimeout: 1 * time.Second,
				},
				RetryCount:       1,
				RetryWaitTime:    100,
				RetryMaxWaitTime: 1000,
				ContentType:      "application/json",
			})

			client := createMockClient(responses)

			cli.Client.Client.SetTransport(client.Transport)
			got, err := ParseThorNode(ctx, cli, tt.txID)

			if (err != nil) != tt.wantErr {
				t.Errorf("ParseThorNode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareThorNodeOutBound(got, tt.want) {
				t.Errorf("ParseThorNode() = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func compareThorNodeOutBound(a, b ThorNodeOutBound) bool {
	return a.ToChainType == b.ToChainType &&
		a.AssetChainCoin == b.AssetChainCoin &&
		a.ToChainAddr == b.ToChainAddr &&
		a.ToChainTxHash == b.ToChainTxHash &&
		a.ChainAmount == b.ChainAmount
}

func TestConvertAmount(t *testing.T) {
	tests := []struct {
		input string
		want  string
		err   bool
	}{
		{"123456789", "1.23456789", false},
		{"100000000", "1", false},
		{"invalid", "", true},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			got, err := convertAmount(tt.input)
			if (err != nil) != tt.err {
				t.Errorf("convertAmount() error = %v, wantErr %v", err, tt.err)
				return
			}
			if got != tt.want {
				t.Errorf("convertAmount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestHandleSingleAction(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name    string
		details TxDetailsResponse
		want    ThorNodeOutBound
	}{
		{
			name: "valid single action",
			details: TxDetailsResponse{
				Actions: []Action{{
					Chain:     "BTC",
					ToAddress: "address1",
					Coin:      Coin{Asset: "BTC.BTC", Amount: "150000000"},
				}},
				OutTxs: []OutTx{{ID: "tx1", Chain: "BTC"}},
			},
			want: ThorNodeOutBound{
				ToChainType:    "BTC",
				AssetChainCoin: "BTC.BTC",
				ToChainAddr:    "address1",
				ToChainTxHash:  "tx1",
				ChainAmount:    "1.5",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := handleSingleAction(ctx, &tt.details, "testhash")
			if err != nil {
				t.Errorf("handleSingleAction() unexpected error: %v", err)
			}
			if !compareThorNodeOutBound(got, tt.want) {
				t.Errorf("handleSingleAction() = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func TestHandleMemoOutCaseExceptions(t *testing.T) {
	ctx := context.Background()

	cli := rpc.NewClient(rpc.Config{
		Config: bhttpclient.Config{
			ReadTimeout: 1 * time.Second,
		},
		RetryCount:       1,
		RetryWaitTime:    100,
		RetryMaxWaitTime: 1000,
		ContentType:      "application/json",
	})

	tests := []struct {
		name    string
		memo    string
		txHash  string
		wantErr string
	}{
		{
			name:    "memo length is too short",
			memo:    "OUT:123", // 只有7个字符
			txHash:  "test-tx-hash",
			wantErr: "memo length is too short: OUT:123",
		},
		{
			name:    "nextHash is empty",
			memo:    "OUT:" + strings.Repeat(" ", 69), // OUT:后面是64个空格
			txHash:  "test-tx-hash",
			wantErr: "nextHash extracted from memo is empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := handleMemoOutCase(ctx, cli, tt.memo, tt.txHash)

			if err == nil {
				t.Errorf("handleMemoOutCase() expected error but got nil")
				return
			}

			if err.Error() != tt.wantErr {
				t.Errorf("handleMemoOutCase() error = %v, wantErr %v", err.Error(), tt.wantErr)
			}
		})
	}
}
