package xxljob

import (
	"context"
	"errors"

	"aml-insight/service/admin/internal/svc"

	"code.bydev.io/frameworks/byone/core/contextutils"
	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/zcron"
)

type CronLogic struct {
	svcCtx     *svc.ServiceContext
	JobManager *JobManager
}

type (
	XxlJobWithZcronParam func(ctx context.Context, param *zcron.XxlJobParam) (result string, err error)
)

func Init(svcCtx *svc.ServiceContext) *zcron.Cron {
	var cronJobs = make(map[string]zcron.Job)
	for _, v := range svcCtx.Config.ZcronConf.Jobs {
		v := v
		logc.Infof(context.Background(), "init cron:%s", v.Name)
		cronJobs[v.Name] = func(ctx context.Context) error {
			return NewCronLogic(svcCtx).Do(ctx, v.Name)
		}
	}

	c := zcron.MustNewCron(svcCtx.Config.ZcronConf, cronJobs)
	return c
}

func NewCronLogic(svcCtx *svc.ServiceContext) *CronLogic {
	return &CronLogic{
		svcCtx:     svcCtx,
		JobManager: NewJobManager(svcCtx),
	}
}

func (l *CronLogic) Do(ctx context.Context, jobName string) error {
	ctx = contextutils.SetReferSiteId(ctx, contextutils.GetEnvSiteId())

	switch jobName {
	case "addressLabelSyncTaskCreate":
		return DoXxlJob(ctx, jobName, l.JobManager.AddressLabelSyncTaskCreate)
	case "addressLabelSyncQuery":
		return DoXxlJob(ctx, jobName, l.JobManager.AddressLabelSyncQuery)
	case "syncRealtimeTx":
		return DoXxlJob(ctx, jobName, l.JobManager.SyncRealtimeTx)
	case "syncHistoricalTx":
		return DoXxlJob(ctx, jobName, l.JobManager.SyncHistoricalTx)
	case "syncHackTx":
		return DoXxlJob(ctx, jobName, l.JobManager.TrackHackerTx)
	case "syncHackTxFull":
		return DoXxlJob(ctx, jobName, l.JobManager.TrackFullHackerTx)
	case "syncWalletExplorer":
		return DoXxlJob(ctx, jobName, l.JobManager.SyncWalletExplorerData)
	default:
		return nil
	}
}

func DoXxlJob(ctx context.Context, jobName string, taskFn XxlJobWithZcronParam) error {
	ctxValue := ctx.Value(zcron.XxlJobRunParams)
	if ctxValue == nil {
		return errors.New("ctxValue is nil")
	}
	value, ok := ctxValue.(*zcron.XxlJobParam)
	if !ok {
		return errors.New("ctxValue is not xxlJobParam")
	}
	if value != nil {
		jobResult, err := taskFn(ctx, value)
		logc.Infow(ctx, "do xxljob done", logc.Field("job_name", jobName), logc.Field("job_result", jobResult), logc.Field("err", err))
		return err
	}
	return errors.New("job param nil")
}
