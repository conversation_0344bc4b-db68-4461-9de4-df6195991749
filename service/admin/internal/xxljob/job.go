package xxljob

import (
	"context"

	"aml-insight/internal/pkg/crawl"
	"aml-insight/internal/service"
	"aml-insight/pkg/lark"
	"aml-insight/service/admin/internal/svc"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/client/bhttpclient"
	"code.bydev.io/frameworks/byone/core/conf"
	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/core/threading"
)

// JobManager handles xxl-job task registration and execution
type JobManager struct {
	svc              *svc.ServiceContext
	workerPool       *threading.TaskRunner
	larkAlert        *lark.LarkAlerter
	walletExplorer   *crawl.WalletExplorerCrawler
	transformService *service.EntityMappingTransformService
}

// NewJobManager creates a new JobManager instance
func NewJobManager(s *svc.ServiceContext) *JobManager {
	jm := &JobManager{
		svc:              s,
		workerPool:       threading.NewTaskRunner(s.Config.JobWorkerPoolSize),
		larkAlert:        lark.NewLarkAlerter(),
		transformService: service.NewEntityMappingTransformService(s.EntityNameMappingModel),
	}

	if crawler, err := createWalletExplorerCrawler(s.Config.WalletExplorerCfg.WalletExplorerCrawlConfig); err == nil {
		jm.walletExplorer = crawler
	} else {
		logc.Errorw(context.Background(), "failed to create wallet explorer crawler", logc.Field("error", err))
	}

	return jm
}

// createWalletExplorerCrawler create crawler instance with bhttpclient
func createWalletExplorerCrawler(crawlConfig crawl.WalletExplorerCrawlConfig) (*crawl.WalletExplorerCrawler, error) {
	var httpConfig bhttpclient.Config
	if err := conf.FillDefault(&httpConfig); err != nil {
		return nil, err
	}

	httpConfig.Name = "wallet-explorer-crawler"
	httpConfig.Addr = crawlConfig.Addr
	if crawlConfig.ReadTimeout != 0 {
		httpConfig.ReadTimeout = crawlConfig.ReadTimeout
	}
	if crawlConfig.SlowLogThreshold != 0 {
		httpConfig.SlowLogThreshold = crawlConfig.SlowLogThreshold
	}
	httpConfig.EnableMetricInterceptor = crawlConfig.EnableMetricInterceptor
	httpConfig.EnableAccessInterceptor = crawlConfig.EnableAccessInterceptor
	httpConfig.EnableAccessInterceptorReq = crawlConfig.EnableAccessInterceptorReq
	httpConfig.EnableAccessInterceptorRes = crawlConfig.EnableAccessInterceptorRes

	crawlerConfig := crawl.WalletExplorerCrawlConfig{
		Config:         httpConfig,
		BaseURL:        crawlConfig.BaseURL,
		MaxRetries:     crawlConfig.MaxRetries,
		RetryDelayMs:   crawlConfig.RetryDelayMs,
		MaxConcurrency: crawlConfig.MaxConcurrency,
		PageSize:       crawlConfig.PageSize,
		CallerEmail:    crawlConfig.CallerEmail,
	}

	return crawl.NewWalletExplorerCrawler(crawlerConfig)
}
