package xxljob

import (
	"context"
	"fmt"
	"math/big"
	"strings"

	"aml-insight/internal/pkg/rpc"

	"code.bydev.io/frameworks/byone/core/logc"
)

const thorNodeUrl = "https://thornode.ninerealms.com/thorchain/tx/details/%s"

type (
	TxDetailsResponse struct {
		Actions []Action `json:"actions"`
		OutTxs  []OutTx  `json:"out_txs"`
		Tx      struct {
			Tx struct {
				Memo string `json:"memo"`
			} `json:"tx"`
		} `json:"tx"`
	}
	Action struct {
		Chain     string `json:"chain"`
		ToAddress string `json:"to_address"`
		Coin      Coin   `json:"coin"`
	}

	Coin struct {
		Asset  string `json:"asset"`
		Amount string `json:"amount"`
	}

	OutTx struct {
		ID    string `json:"id"`
		Chain string `json:"chain"`
	}
)

type ThorNodeOutBound struct {
	ToChainType    string // 目标链类型
	AssetChainCoin string // 资产链币种
	ToChainAddr    string // 目标链地址
	ToChainTxHash  string // 目标链交易哈希
	ChainAmount    string // 链上金额
}

func ParseThorNode(ctx context.Context, cli *rpc.Client, txId string) (ThorNodeOutBound, error) {
	if txId == "" {
		logc.Errorw(ctx, "txId is empty")
		return ThorNodeOutBound{}, fmt.Errorf("txId is empty")
	}
	txId = strings.ToLower(txId)

	if strings.HasPrefix(txId, "0x") {
		txId = txId[2:]
	}

	return processTransaction(ctx, cli, fmt.Sprintf(thorNodeUrl, txId), txId)
}

func processTransaction(ctx context.Context, client *rpc.Client, url string, txHash string) (ThorNodeOutBound, error) {
	resp := &TxDetailsResponse{}
	rawResp, err := client.R().SetContext(ctx).SetResult(resp).Get(url)
	if err != nil {
		logc.Errorw(ctx, "failed to get tx details", logc.Field("txHash", txHash), logc.Field("err", err))
		return ThorNodeOutBound{}, err
	}

	logc.Infow(ctx, "response body", logc.Field("txHash", txHash), logc.Field("body", string(rawResp.Body())))

	if len(resp.Actions) == 1 {
		return handleSingleAction(ctx, resp, txHash)
	} else if len(resp.Actions) > 1 {
		return handleMultipleActions(ctx, resp, txHash)
	} else if memo := resp.Tx.Tx.Memo; strings.HasPrefix(memo, "OUT:") {
		return handleMemoOutCase(ctx, client, memo, txHash)
	}

	return ThorNodeOutBound{
		ToChainType:    "none btc/eth tx:",
		AssetChainCoin: "",
		ToChainAddr:    txHash,
		ToChainTxHash:  "",
		ChainAmount:    "",
	}, nil
}

func handleSingleAction(ctx context.Context, details *TxDetailsResponse, txHash string) (ThorNodeOutBound, error) {
	if len(details.OutTxs) == 0 {
		return ThorNodeOutBound{
			ToChainType:    "none btc/eth tx:",
			AssetChainCoin: "",
			ToChainAddr:    txHash,
			ToChainTxHash:  "",
			ChainAmount:    "",
		}, nil
	}

	action := details.Actions[0]
	outTx := details.OutTxs[0]
	amount, err := convertAmount(action.Coin.Amount)
	if err != nil {
		logc.Errorw(ctx, "convert amount error", logc.Field("txHash", txHash), logc.Field("err", err))
		return ThorNodeOutBound{}, err
	}

	return ThorNodeOutBound{
		ToChainType:    action.Chain,
		AssetChainCoin: action.Coin.Asset,
		ToChainAddr:    action.ToAddress,
		ToChainTxHash:  outTx.ID,
		ChainAmount:    amount,
	}, nil
}

func handleMultipleActions(ctx context.Context, details *TxDetailsResponse, txHash string) (ThorNodeOutBound, error) {
	outAction, outTx := findNonThorEntities(details)
	if outAction == nil || outTx == nil {
		return ThorNodeOutBound{
			ToChainType:    "none btc/eth tx:",
			AssetChainCoin: "",
			ToChainAddr:    txHash,
			ToChainTxHash:  "",
			ChainAmount:    "",
		}, nil
	}

	amount, err := convertAmount(outAction.Coin.Amount)
	if err != nil {
		logc.Errorw(ctx, "convert amount error", logc.Field("txHash", txHash), logc.Field("err", err))
		return ThorNodeOutBound{}, err
	}

	return ThorNodeOutBound{
		ToChainType:    outAction.Chain,
		AssetChainCoin: outAction.Coin.Asset,
		ToChainAddr:    outAction.ToAddress,
		ToChainTxHash:  outTx.ID,
		ChainAmount:    amount,
	}, nil
}

func handleMemoOutCase(ctx context.Context, client *rpc.Client, memo string, txHash string) (ThorNodeOutBound, error) {
	// 检查memo长度是否足够
	if len(memo) < 68 {
		logc.Errorw(ctx, "memo length is too short", logc.Field("memo", memo), logc.Field("txHash", txHash))
		return ThorNodeOutBound{}, fmt.Errorf("memo length is too short: %s", memo)
	}

	nextHash := strings.Trim(memo[4:68], " ")

	if nextHash == "" {
		logc.Errorw(ctx, "nextHash extracted from memo is empty", logc.Field("memo", memo), logc.Field("txHash", txHash))
		return ThorNodeOutBound{}, fmt.Errorf("nextHash extracted from memo is empty")
	}

	url := fmt.Sprintf(thorNodeUrl, nextHash)
	resp := &TxDetailsResponse{}
	rawResp, err := client.R().SetContext(ctx).SetResult(resp).Get(url)
	if err != nil {
		logc.Errorw(ctx, "get next tx details error", logc.Field("txHash", txHash), logc.Field("err", err))
		return ThorNodeOutBound{}, err
	}

	logc.Infow(ctx, "response body", logc.Field("txHash", txHash), logc.Field("body", string(rawResp.Body())))

	outAction, outTx := findNonThorEntities(resp)
	if outAction == nil || outTx == nil {
		return ThorNodeOutBound{
			ToChainType:    "none btc/eth tx:",
			AssetChainCoin: "",
			ToChainAddr:    txHash,
			ToChainTxHash:  "",
			ChainAmount:    "",
		}, nil
	}

	amount, err := convertAmount(outAction.Coin.Amount)
	if err != nil {
		logc.Errorw(ctx, "convert amount error", logc.Field("txHash", txHash), logc.Field("err", err))
		return ThorNodeOutBound{}, err
	}

	return ThorNodeOutBound{
		ToChainType:    outAction.Chain,
		AssetChainCoin: outAction.Coin.Asset,
		ToChainAddr:    outAction.ToAddress,
		ToChainTxHash:  outTx.ID,
		ChainAmount:    amount,
	}, nil
}

func findNonThorEntities(details *TxDetailsResponse) (*Action, *OutTx) {
	var outAction *Action
	for i := range details.Actions {
		if details.Actions[i].Chain != "THOR" {
			outAction = &details.Actions[i]
			break
		}
	}

	var outTx *OutTx
	for i := range details.OutTxs {
		if details.OutTxs[i].Chain != "THOR" {
			outTx = &details.OutTxs[i]
			break
		}
	}

	return outAction, outTx
}

func convertAmount(amountStr string) (string, error) {
	amount := new(big.Int)
	if _, ok := amount.SetString(amountStr, 10); !ok {
		return "", fmt.Errorf("invalid amount: %s", amountStr)
	}

	divisor := big.NewInt(1e8)
	quotient := new(big.Int).Div(amount, divisor)
	remainder := new(big.Int).Mod(amount, divisor)

	if remainder.Sign() == 0 {
		return quotient.String(), nil
	}

	remainderStr := fmt.Sprintf("%08s", remainder.String())
	remainderStr = strings.TrimLeft(strings.Repeat("0", 8-len(remainderStr))+remainder.String(), "0")
	trimmed := strings.TrimRight(remainderStr, "0")

	if len(trimmed) == 0 {
		return quotient.String(), nil
	}

	return fmt.Sprintf("%s.%s", quotient.String(), trimmed), nil
}
