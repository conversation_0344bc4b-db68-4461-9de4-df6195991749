from flask import Flask, request

app = Flask(__name__)

@app.route('/api/callback', methods=['POST'])
def callback():
    data = request.json
    print("Received registry request:", data)
    return {"code": 200, "msg": None}, 200

@app.route('/api/registry', methods=['POST'])
def registry():
    data = request.json
    print("Received registry request:", data)
    return {"code": 200, "msg": None}, 200

@app.route('/api/registryRemove', methods=['POST']) 
def registry_remove():
    data = request.json
    print("Received registry remove request:", data)
    return {"code": 200, "msg": None}, 200

# Catch-all route handler for any unregistered paths
@app.route('/<path:path>', methods=['POST', 'GET', 'PUT', 'DELETE'])
@app.route('/', methods=['POST', 'GET', 'PUT', 'DELETE'])
def catch_all(path='/'):
    data = request.json
    print(f"Received {request.method} request for path /{path}, data:", data)
    return {"code": 200, "msg": None}, 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8090)

