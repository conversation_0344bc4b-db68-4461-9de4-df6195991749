package xxljob

import (
	"aml-insight/service/admin/internal/config"
	"context"
	"testing"

	"aml-insight/service/admin/internal/svc"
	"code.bydev.io/frameworks/byone/zcron"
	"github.com/stretchr/testify/assert"
)

func TestInit(t *testing.T) {

	cron := Init(&svc.ServiceContext{
		Config: config.Config{
			ZcronConf: zcron.CronConf{
				XxlJobConfig: zcron.XxlJobConf{
					AdminAddress: "test",
					AppName:      "aml-insight-admin",
					AccessToken:  "test",
					Namespace:    "pubilc",
					ExecutorIp:   "",
					ExecutorPort: "",
				},
				Jobs: []zcron.JobConf{{
					Name:     "addressLabelSyncTaskCreate",
					CronType: "xxljob",
					Spec:     "public",
				}},
			},
		}})

	// 确保 Cron 对象初始化成功
	assert.NotNil(t, cron)
}

// 测试 DoXxlJob 方法
func TestDoXxlJob(t *testing.T) {

	testFn := func(ctx context.Context, param *zcron.XxlJobParam) (result string, err error) {
		return "test", nil
	}
	err := DoXxlJob(context.Background(), "addressLabelSyncTaskCreate", testFn)
	assert.Error(t, err, "ctxValue is nil")

	ctx := context.WithValue(context.Background(), zcron.XxlJobRunParams, &zcron.XxlJobParam{})
	err = DoXxlJob(ctx, "addressLabelSyncTaskCreate", testFn)
	assert.NoError(t, err)
}
