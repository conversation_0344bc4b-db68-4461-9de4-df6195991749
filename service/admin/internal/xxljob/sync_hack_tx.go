package xxljob

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"code.bydev.io/frameworks/byone/zcron"

	"aml-insight/internal/model"
	"aml-insight/service/admin/internal/logic/txdata"

	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/core/stores/sqlc"
)

// 基本步骤：
//  1. 从 https://hackscan.hackbounty.io/public/hack-address.json 获取黑客地址的from_address
//  1.1 其中 to_adress 如下：
// ******************************************
// 0xd03d56ef7d11a1a5a0933c1d524ff0bc1e916c98
// ******************************************
// ******************************************
// ******************************************
// ******************************************
// ******************************************
// ******************************************
// ******************************************
// ******************************************
// 2. 根据from_address和to_address，查询大数据平台获取tx_hash
// select transaction_hash  from ods_security_risk.eth_event_v2 where topic2 in ('******************************************') and topic1 in ('******************************************') and dt >='2025-02-22'
// select transaction_hash from ods_security_risk.eth_transaction_v2 where  transaction_from in ('******************************************') and transaction_to in ('******************************************')  and dt >='2025-02-22'
// ------------------------ 先搞定这部分代码 -----------------
// 3. 根据tx_hash，查询区块链浏览器获取tx_detail (link: https://thorchain.net/tx/******************************************)
// 3.1 具体获取的tx_detail的具体逻辑: xxxx (js 文件内容)
// 4. 将相应的结果写入到数据库中

const (
	hackTxSyncTimestampKey = "aml_insight:hack_tx_sync_timestamp"

	defaultQueryTimeout = 5 * time.Minute
	defaultPollInterval = 5 * time.Second

	defaultLongQueryTimeout = 10 * time.Minute
	defaultLongPollInterval = 10 * time.Second
)

type TrackHackerTxParam struct {
	QueryTimeoutSec int64 `json:"query_timeout_sec"` // 查询超时时间
	PollIntervalSec int64 `json:"poll_interval_sec"` // 轮询间隔
	TxTimestampSec  int64 `json:"tx_timestamp_sec"`  // 交易时间戳
}

// TrackFullHackerTx 全量同步黑客交易数据
func (j *JobManager) TrackFullHackerTx(ctx context.Context, param *zcron.XxlJobParam) (result string, err error) {
	txParam := parseTrackHackerTxParam(ctx, param)
	logc.Infow(ctx, "track full hacker tx params",
		logc.Field("query_timeout_sec", txParam.QueryTimeoutSec),
		logc.Field("poll_interval_sec", txParam.PollIntervalSec),
		logc.Field("tx_timestamp_sec", txParam.TxTimestampSec))

	queryTimeout := defaultLongQueryTimeout
	pollInterval := defaultLongPollInterval
	if txParam.QueryTimeoutSec > 0 {
		queryTimeout = time.Duration(txParam.QueryTimeoutSec) * time.Second
	}
	if txParam.PollIntervalSec > 0 {
		pollInterval = time.Duration(txParam.PollIntervalSec) * time.Second
	}

	txs, err := j.svc.TxDataService.GetAllTxHashWithTimeout(ctx, queryTimeout, pollInterval)
	if err != nil {
		logc.Errorw(ctx, "get all tx hash error", logc.Field("error", err))
		return "failed", err
	}

	logc.Infow(ctx, "get all tx hash success", logc.Field("txs_size", len(txs)))

	// 处理交易数据
	for _, tx := range txs {
		if err := j.trackHackerTxHandler(ctx, &model.HackTx{
			TxId:     tx.TxId,
			TxTime:   time.Unix(tx.Timestamp, 0),
			FromAddr: tx.FromAddr,
			ToAddr:   tx.ToAddr,
			Amount:   tx.Amount,
			TxSymbol: tx.Symbol,
			State:    strconv.Itoa(tx.State),
			TxType:   tx.TxType,
		}); err != nil {
			logc.Warnw(ctx, "failed to track hacker tx", logc.Field("error", err), logc.Field("txId", tx.TxId),
				logc.Field("fromAddr", tx.FromAddr), logc.Field("toAddr", tx.ToAddr))
		}
	}

	logc.Infow(ctx, "full sync hacker tx completed", logc.Field("processed", len(txs)))
	return "success", nil
}

// TrackHackerTx 增量同步黑客交易数据
func (j *JobManager) TrackHackerTx(ctx context.Context, param *zcron.XxlJobParam) (result string, err error) {
	// 解析参数
	txParam := parseTrackHackerTxParam(ctx, param)
	logc.Infow(ctx, "track hacker tx params",
		logc.Field("query_timeout_sec", txParam.QueryTimeoutSec),
		logc.Field("poll_interval_sec", txParam.PollIntervalSec),
		logc.Field("tx_timestamp_sec", txParam.TxTimestampSec))

	// 获取时间戳
	timestamp := j.getHackTxSyncTimestamp(ctx, txParam.TxTimestampSec)
	logc.Infow(ctx, "get hack tx sync timestamp", logc.Field("timestamp", timestamp))

	// 设置查询超时和轮询间隔
	queryTimeout := defaultQueryTimeout
	pollInterval := defaultPollInterval
	if txParam.QueryTimeoutSec > 0 {
		queryTimeout = time.Duration(txParam.QueryTimeoutSec) * time.Second
	}
	if txParam.PollIntervalSec > 0 {
		pollInterval = time.Duration(txParam.PollIntervalSec) * time.Second
	}

	txs, err := j.svc.TxDataService.GetTxHashWithTimeout(ctx, timestamp, queryTimeout, pollInterval)
	if err != nil {
		logc.Errorw(ctx, "get tx hash error", logc.Field("error", err))
		return "failed", err
	}

	defer func() {
		maxTimestamp := getMaxTimestamp(txs)
		if maxTimestamp > timestamp {
			j.setHackTxSyncTimestamp(ctx, maxTimestamp)
		}
	}()

	logc.Infow(ctx, "get tx hash success", logc.Field("txs_size", len(txs)))

	for _, tx := range txs {
		// TODO: 并发处理下，但是需要进行限制 threading.TaskRunner
		if err := j.trackHackerTxHandler(ctx, &model.HackTx{
			TxId:     tx.TxId,
			TxTime:   time.Unix(tx.Timestamp, 0),
			FromAddr: tx.FromAddr,
			ToAddr:   tx.ToAddr,
			Amount:   tx.Amount,
			TxSymbol: tx.Symbol,
			State:    strconv.Itoa(tx.State),
			TxType:   tx.TxType,
		}); err != nil {
			logc.Warnw(ctx, "failed to track hacker tx", logc.Field("error", err), logc.Field("txId", tx.TxId),
				logc.Field("fromAddr", tx.FromAddr), logc.Field("toAddr", tx.ToAddr))
		}
	}

	return "success", nil
}

func (j *JobManager) trackHackerTxHandler(ctx context.Context, hxData *model.HackTx) error {
	logc.Infow(ctx, "track hacker tx", logc.Field("txId", hxData.TxId))
	// 1. 查询数据库，判断是否已经存在
	_, err := j.svc.HackTxModel.FindOne(ctx, hxData.TxId)
	switch err {
	case nil:
		logc.Warnw(ctx, "txId already exists", logc.Field("txId", hxData.TxId))
		return nil
	case sqlc.ErrNotFound:
	default:
		return err
	}

	v, err := ParseThorNode(ctx, j.svc.ThorNodeHttpClient, hxData.TxId)
	if err != nil {
		return err
	}

	hxData.ToChainType = v.ToChainType
	hxData.AssetChainCoin = v.AssetChainCoin
	hxData.ToChainAddr = v.ToChainAddr
	hxData.ToChainTxHash = v.ToChainTxHash
	hxData.ChainAmount = v.ChainAmount

	// 2. 写入数据库
	_, err = j.svc.HackTxModel.Insert(ctx, hxData)
	if err != nil {
		return err
	}

	return nil
}

// getHackTxSyncTimestamp 统一获取黑客交易同步时间戳的逻辑
func (j *JobManager) getHackTxSyncTimestamp(ctx context.Context, paramTimestamp int64) int64 {
	// 1. 如果参数中提供了时间戳，优先使用
	if paramTimestamp > 0 {
		logc.Infow(ctx, "using timestamp from param", logc.Field("timestamp", paramTimestamp))
		return paramTimestamp
	}

	// 2. 从Redis中获取上次保存的时间戳
	if timestampStr, err := j.svc.BizRedis.GetNillableCtx(ctx, hackTxSyncTimestampKey); err == nil && timestampStr != "" {
		parsedTimestamp, parseErr := strconv.ParseInt(timestampStr, 10, 64)
		if parseErr == nil {
			logc.Infow(ctx, "using timestamp from redis", logc.Field("timestamp", parsedTimestamp))
			return parsedTimestamp
		}
		logc.Warnw(ctx, "failed to parse timestamp", logc.Field("error", parseErr))
	}

	// 3. 使用默认时间戳
	defaultTimestamp := time.Date(2025, 2, 21, 0, 0, 0, 0, time.UTC).Unix() // 从2025-02-21开始
	logc.Infow(ctx, "using default timestamp", logc.Field("timestamp", defaultTimestamp))
	return defaultTimestamp
}

func (j *JobManager) setHackTxSyncTimestamp(ctx context.Context, timestamp int64) {
	j.svc.BizRedis.SetCtx(ctx, hackTxSyncTimestampKey, strconv.FormatInt(timestamp, 10))
}

func getMaxTimestamp(txs []*txdata.TxDetail) int64 {
	maxTimestamp := int64(0)
	for _, tx := range txs {
		if tx.Timestamp > maxTimestamp {
			maxTimestamp = tx.Timestamp
		}
	}
	return maxTimestamp
}

// parseTrackHackerTxParam 从xxl.RunReq中解析TrackHackerTxParam
func parseTrackHackerTxParam(ctx context.Context, param *zcron.XxlJobParam) *TrackHackerTxParam {
	if param == nil || param.ExecutorParams == "" {
		return &TrackHackerTxParam{}
	}

	var txParam TrackHackerTxParam
	err := json.Unmarshal([]byte(param.ExecutorParams), &txParam)
	if err != nil {
		logc.Warnw(ctx, "failed to parse track hacker tx param", logc.Field("error", err),
			logc.Field("param", param.ExecutorParams))
		return &TrackHackerTxParam{}
	}

	return &txParam
}
