package xxljob

import (
	"context"
	"testing"

	mockmodel "aml-insight/internal/mock/model"
	"aml-insight/internal/model"
	"aml-insight/internal/pkg/crawl"
	"aml-insight/internal/service"
	"aml-insight/pkg/lark"
	"aml-insight/service/admin/internal/svc"

	"code.bydev.io/frameworks/byone/zcron"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/mock/gomock"
)

// Mock interfaces for testing - simplified versions
type MockWalletExplorerCrawler struct {
	mock.Mock
}

func (m *MockWalletExplorerCrawler) GetWalletData(ctx context.Context, walletAddress string, params crawl.BaseLabelInfo) ([]crawl.AddressLabelInfo, error) {
	args := m.Called(ctx, walletAddress, params)
	return args.Get(0).([]crawl.AddressLabelInfo), args.Error(1)
}

type MockLarkAlerter struct {
	mock.Mock
}

func (m *MockLarkAlerter) SendLarkRichTextMsg(ctx context.Context, webhookUrl string, reqBody lark.LarkRichMsgBody) error {
	args := m.Called(ctx, webhookUrl, reqBody)
	return args.Error(0)
}

func createTestJobManager() (*JobManager, *mockmodel.MockAllAddressLabelModel, *mockmodel.MockAddressLabelModel, *mockmodel.MockEntityNameMappingModel) {
	ctrl := gomock.NewController(nil)

	mockAllAddressLabel := mockmodel.NewMockAllAddressLabelModel(ctrl)
	mockAddressLabel := mockmodel.NewMockAddressLabelModel(ctrl)
	mockEntityNameMapping := mockmodel.NewMockEntityNameMappingModel(ctrl)

	// Create a simplified job manager for testing
	jobManager := &JobManager{
		svc: &svc.ServiceContext{
			AllAddressLabelModel:   mockAllAddressLabel,
			AddressLabelModel:      mockAddressLabel,
			EntityNameMappingModel: mockEntityNameMapping,
		},
		larkAlert:        lark.NewLarkAlerter(), // Use real lark alerter for simplicity
		walletExplorer:   nil,                   // Set to nil, individual tests can override
		transformService: service.NewEntityMappingTransformService(mockEntityNameMapping),
	}

	return jobManager, mockAllAddressLabel, mockAddressLabel, mockEntityNameMapping
}

func TestWalletExplorerParam_GetChain(t *testing.T) {
	tests := []struct {
		name     string
		param    WalletExplorerParam
		expected string
	}{
		{
			name: "empty chain returns default",
			param: WalletExplorerParam{
				BaseLabelInfo: crawl.BaseLabelInfo{Chain: ""},
			},
			expected: defaultChain,
		},
		{
			name: "non-empty chain returns chain",
			param: WalletExplorerParam{
				BaseLabelInfo: crawl.BaseLabelInfo{Chain: "ETH"},
			},
			expected: "ETH",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.param.GetChain())
		})
	}
}

func TestWalletExplorerParam_GetEditor(t *testing.T) {
	tests := []struct {
		name     string
		param    WalletExplorerParam
		expected string
	}{
		{
			name: "empty editor returns default",
			param: WalletExplorerParam{
				BaseLabelInfo: crawl.BaseLabelInfo{Editor: ""},
			},
			expected: defaultEditor,
		},
		{
			name: "non-empty editor returns editor",
			param: WalletExplorerParam{
				BaseLabelInfo: crawl.BaseLabelInfo{Editor: "john.doe"},
			},
			expected: "john.doe",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.param.GetEditor())
		})
	}
}

func TestWalletExplorerParam_GetSource(t *testing.T) {
	tests := []struct {
		name     string
		param    WalletExplorerParam
		expected string
	}{
		{
			name: "empty source returns default",
			param: WalletExplorerParam{
				BaseLabelInfo: crawl.BaseLabelInfo{Source: ""},
			},
			expected: defaultSource,
		},
		{
			name: "non-empty source returns source",
			param: WalletExplorerParam{
				BaseLabelInfo: crawl.BaseLabelInfo{Source: "custom_source"},
			},
			expected: "custom_source",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.param.GetSource())
		})
	}
}

func TestWalletExplorerParam_GetRemark(t *testing.T) {
	tests := []struct {
		name     string
		param    WalletExplorerParam
		expected string
	}{
		{
			name: "empty remark returns default",
			param: WalletExplorerParam{
				BaseLabelInfo: crawl.BaseLabelInfo{Remark: ""},
			},
			expected: defaultRemark,
		},
		{
			name: "non-empty remark returns remark",
			param: WalletExplorerParam{
				BaseLabelInfo: crawl.BaseLabelInfo{Remark: "custom remark"},
			},
			expected: "custom remark",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.param.GetRemark())
		})
	}
}

func TestWalletExplorerParam_GetValid(t *testing.T) {
	tests := []struct {
		name     string
		param    WalletExplorerParam
		expected int32
	}{
		{
			name: "zero valid returns default",
			param: WalletExplorerParam{
				BaseLabelInfo: crawl.BaseLabelInfo{Valid: 0},
			},
			expected: defaultValid,
		},
		{
			name: "non-zero valid returns valid",
			param: WalletExplorerParam{
				BaseLabelInfo: crawl.BaseLabelInfo{Valid: 2},
			},
			expected: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.param.GetValid())
		})
	}
}

func TestWalletExplorerParam_GetDetailName(t *testing.T) {
	tests := []struct {
		name     string
		param    WalletExplorerParam
		expected string
	}{
		{
			name: "empty detail name returns entity_user",
			param: WalletExplorerParam{
				BaseLabelInfo: crawl.BaseLabelInfo{
					EntityName: "test_entity",
					DetailName: "",
				},
			},
			expected: "test_entity_user",
		},
		{
			name: "non-empty detail name returns detail name",
			param: WalletExplorerParam{
				BaseLabelInfo: crawl.BaseLabelInfo{
					EntityName: "test_entity",
					DetailName: "custom_detail",
				},
			},
			expected: "custom_detail",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.param.GetDetailName())
		})
	}
}

func TestWalletExplorerParam_Validate(t *testing.T) {
	tests := []struct {
		name    string
		param   WalletExplorerParam
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid parameters",
			param: WalletExplorerParam{
				WalletAddr: "**********************************",
				BaseLabelInfo: crawl.BaseLabelInfo{
					EntityName: "test_entity",
					Category:   "exchange",
				},
			},
			wantErr: false,
		},
		{
			name: "empty entity name",
			param: WalletExplorerParam{
				WalletAddr: "**********************************",
				BaseLabelInfo: crawl.BaseLabelInfo{
					EntityName: "",
					Category:   "exchange",
				},
			},
			wantErr: true,
			errMsg:  "entityName is required",
		},
		{
			name: "empty wallet address",
			param: WalletExplorerParam{
				WalletAddr: "",
				BaseLabelInfo: crawl.BaseLabelInfo{
					EntityName: "test_entity",
					Category:   "exchange",
				},
			},
			wantErr: true,
			errMsg:  "walletAddr is required",
		},
		{
			name: "empty category",
			param: WalletExplorerParam{
				WalletAddr: "**********************************",
				BaseLabelInfo: crawl.BaseLabelInfo{
					EntityName: "test_entity",
					Category:   "",
				},
			},
			wantErr: true,
			errMsg:  "category is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.param.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestJobManager_parseAndValidateParams(t *testing.T) {
	j, _, _, _ := createTestJobManager()
	ctx := context.Background()

	tests := []struct {
		name    string
		param   *zcron.XxlJobParam
		wantErr bool
		errMsg  string
	}{
		{
			name:    "nil param",
			param:   nil,
			wantErr: true,
			errMsg:  "param is nil",
		},
		{
			name: "empty executor params",
			param: &zcron.XxlJobParam{
				ExecutorParams: "",
			},
			wantErr: true,
			errMsg:  "executor params is empty",
		},
		{
			name: "invalid json",
			param: &zcron.XxlJobParam{
				ExecutorParams: "invalid json",
			},
			wantErr: true,
			errMsg:  "failed to unmarshal executor params",
		},
		{
			name: "valid parameters",
			param: &zcron.XxlJobParam{
				ExecutorParams: `{"wallet_addr":"**********************************","entity_name":"test_entity","category":"exchange"}`,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := j.parseAndValidateParams(ctx, tt.param)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

func TestJobManager_saveWalletAddressRecords(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name      string
		records   []crawl.AddressLabelInfo
		mockSetup func(*mockmodel.MockAllAddressLabelModel, *mockmodel.MockAddressLabelModel, *mockmodel.MockEntityNameMappingModel)
		wantErr   bool
		errMsg    string
	}{
		{
			name:    "empty records",
			records: []crawl.AddressLabelInfo{},
			wantErr: false,
		},
		{
			name: "successful save",
			records: []crawl.AddressLabelInfo{
				{
					Address: "**********************************",
					BaseLabelInfo: crawl.BaseLabelInfo{
						Chain:      "BTC",
						EntityName: "test_entity",
						Category:   "exchange",
					},
				},
			},
			mockSetup: func(m1 *mockmodel.MockAllAddressLabelModel, m2 *mockmodel.MockAddressLabelModel, m3 *mockmodel.MockEntityNameMappingModel) {
				m1.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(int64(1), nil)
				m2.EXPECT().InsertOrUpdate(gomock.Any(), gomock.Any()).Return(nil)
				// Set up EntityNameMappingModel expectations
				m3.EXPECT().ExistsByValue(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m3.EXPECT().FindOneByEntityNameKey(gomock.Any(), gomock.Any()).Return(nil, model.ErrNotFound).AnyTimes()
				m3.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			j, mockAllAddressLabel, mockAddressLabel, mockEntityNameMapping := createTestJobManager()

			if tt.mockSetup != nil {
				tt.mockSetup(mockAllAddressLabel, mockAddressLabel, mockEntityNameMapping)
			}

			err := j.saveWalletAddressRecords(ctx, tt.records)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
