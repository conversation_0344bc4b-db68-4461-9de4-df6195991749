package xxljob

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"testing"
	"time"

	mockmodel "aml-insight/internal/mock/model"
	mockoklink "aml-insight/internal/mock/oklink"
	"aml-insight/internal/model"
	"aml-insight/internal/pkg/oklink"
	"aml-insight/service/admin/internal/svc"

	"code.bydev.io/frameworks/byone/core/threading"
	"code.bydev.io/frameworks/byone/zcron"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

type testJobManager struct {
	*JobManager
}

func newTestJobManager(j *JobManager) *testJobManager {
	return &testJobManager{
		JobManager: j,
	}
}

func setupTest(t *testing.T) (*testJobManager, *mockoklink.MockTxGetter, *mockmodel.MockSyncAddressLabelModel,
	*mockmodel.MockNormalTransactionModel, *mockmodel.MockTokenTransactionModel, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockOklinkClient := mockoklink.NewMockTxGetter(ctrl)
	mockSyncAddressLabelModel := mockmodel.NewMockSyncAddressLabelModel(ctrl)
	mockNormalTransactionModel := mockmodel.NewMockNormalTransactionModel(ctrl)
	mockTokenTransactionModel := mockmodel.NewMockTokenTransactionModel(ctrl)

	j := &JobManager{
		svc: &svc.ServiceContext{
			OklinkClient:           mockOklinkClient,
			SyncAddressLabelModel:  mockSyncAddressLabelModel,
			NormalTransactionModel: mockNormalTransactionModel,
			TokenTransactionModel:  mockTokenTransactionModel,
		},
		workerPool: threading.NewTaskRunner(1),
	}

	return newTestJobManager(j), mockOklinkClient, mockSyncAddressLabelModel, mockNormalTransactionModel, mockTokenTransactionModel, ctrl
}

func TestJobManager_fetchRealtimeTokenTx(t *testing.T) {
	t.Run("success with no previous height", func(t *testing.T) {
		t.Parallel()
		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockTokenTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(0), nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), oklink.TokenTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
				},
				ProtocolType: oklink.DefaultProtocolType,
			}).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									TxId:            "0x1",
									BlockHash:       "0xblock1",
									Height:          "100",
									From:            "0xfrom",
									To:              "0xto",
									Amount:          "1.0",
									Symbol:          "USDT",
									TransactionTime: "1677649200000",
									IsFromContract:  false,
									IsToContract:    true,
								},
								TokenContractAddress: "0xtoken",
								TokenID:              "1",
							},
						},
					},
				},
			}, nil)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), &model.InsertBatchOption{
				IgnoreErrors:         true,
				OnDuplicateKeyUpdate: true,
			}).
			DoAndReturn(func(_ context.Context, txs []*model.TokenTransaction, _ *model.InsertBatchOption) (sql.Result, error) {
				assert.Len(t, txs, 1)
				tx := txs[0]
				assert.Equal(t, "0x1", tx.TransactionHash)
				assert.Equal(t, "0xblock1", tx.BlockHash)
				assert.Equal(t, int64(100), tx.Height)
				assert.Equal(t, "USDT", tx.TokenSymbol)
				assert.Equal(t, "0xtoken", tx.TokenContractAddress)
				assert.Equal(t, "1", tx.TokenId)
				return model.NewMockedResult(1, 1), nil
			})

		err := j.fetchRealtimeTokenTx(ctx, addr)
		assert.NoError(t, err)
	})

	t.Run("success with previous height", func(t *testing.T) {
		t.Parallel()
		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		lastHeight := int64(100)
		mockTokenTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(lastHeight, nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height: "100",
								},
							},
							{
								BaseTx: oklink.BaseTx{
									Height: "101",
								},
							},
						},
					},
				},
			}, nil)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, txs []*model.TokenTransaction, _ *model.InsertBatchOption) (sql.Result, error) {
				assert.Len(t, txs, 1)
				assert.Equal(t, int64(101), txs[0].Height)
				return model.NewMockedResult(1, 1), nil
			})

		err := j.fetchRealtimeTokenTx(ctx, addr)
		assert.NoError(t, err)
	})

	t.Run("error when GetTokenTx fails", func(t *testing.T) {
		t.Parallel()
		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockTokenTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(0), nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("api error"))

		err := j.fetchRealtimeTokenTx(ctx, addr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "api error")
	})

	t.Run("error when response code is not zero", func(t *testing.T) {
		t.Parallel()
		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockTokenTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(0), nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "1",
					Msg:  "api error",
				},
			}, nil)

		err := j.fetchRealtimeTokenTx(ctx, addr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get token transaction because of empty data, msg: api error")
	})

	t.Run("error when data is empty", func(t *testing.T) {
		t.Parallel()
		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockTokenTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(0), nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{},
			}, nil)

		err := j.fetchRealtimeTokenTx(ctx, addr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get token transaction because of empty data, msg: success")
	})

	t.Run("error when InsertBatch fails", func(t *testing.T) {
		t.Parallel()
		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockTokenTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(0), nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height: "100",
								},
							},
						},
					},
				},
			}, nil)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("insert error"))

		err := j.fetchRealtimeTokenTx(ctx, addr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to insert token transactions: insert error")
	})
}

func TestJobManager_getTxChain(t *testing.T) {
	j, _, mockSyncAddressLabelModel, _, _, ctrl := setupTest(t)
	defer ctrl.Finish()

	tests := []struct {
		name           string
		param          *zcron.XxlJobParam
		mockSetup      func()
		expectedChains []string
		expectedError  string
	}{
		{
			name: "success with valid executor params",
			param: &zcron.XxlJobParam{
				ExecutorParams: `{"chains":["eth","btc"]}`,
			},
			mockSetup:      func() {},
			expectedChains: []string{"eth", "btc"},
			expectedError:  "",
		},
		{
			name: "failure with invalid executor params",
			param: &zcron.XxlJobParam{
				ExecutorParams: `invalid json`,
			},
			mockSetup:      func() {},
			expectedChains: nil,
			expectedError:  "failed to unmarshal params error",
		},
		{
			name:  "success with empty executor params",
			param: &zcron.XxlJobParam{},
			mockSetup: func() {
				mockSyncAddressLabelModel.EXPECT().
					FindAllChain(gomock.Any()).
					Return([]string{"eth", "btc"}, nil)
			},
			expectedChains: []string{"eth", "btc"},
			expectedError:  "",
		},
		{
			name:  "failure when database query fails",
			param: &zcron.XxlJobParam{},
			mockSetup: func() {
				mockSyncAddressLabelModel.EXPECT().
					FindAllChain(gomock.Any()).
					Return(nil, fmt.Errorf("database error"))
			},
			expectedChains: nil,
			expectedError:  "failed to find all chain error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSetup()

			chains, err := j.getTxChain(context.Background(), tt.param)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedChains, chains)
			}
		})
	}
}

func TestJobManager_fetchRealtimeCoinTx(t *testing.T) {
	t.Run("success with no previous height", func(t *testing.T) {
		t.Parallel()
		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockNormalTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(0), nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), oklink.NormalTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
				},
			}).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									TxId:            "0x1",
									BlockHash:       "0xblock1",
									Height:          "100",
									From:            "0xfrom",
									To:              "0xto",
									Amount:          "1.0",
									Symbol:          "ETH",
									TransactionTime: "1677649200000",
									IsFromContract:  false,
									IsToContract:    true,
								},
								MethodID:        "0x123",
								Nonce:           "1",
								GasPrice:        "20000000000",
								GasLimit:        "21000",
								GasUsed:         "21000",
								TxFee:           "0.00042",
								State:           "success",
								TransactionType: "transfer",
							},
						},
					},
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), &model.InsertBatchOption{
				IgnoreErrors:         true,
				OnDuplicateKeyUpdate: true,
			}).
			DoAndReturn(func(_ context.Context, txs []*model.NormalTransaction, _ *model.InsertBatchOption) (sql.Result, error) {
				assert.Len(t, txs, 1)
				tx := txs[0]
				assert.Equal(t, "0x1", tx.TransactionHash)
				assert.Equal(t, "0xblock1", tx.BlockHash)
				assert.Equal(t, int64(100), tx.Height)
				assert.Equal(t, "ETH", tx.CoinSymbol)
				assert.Equal(t, "0x123", tx.MethodId)
				assert.Equal(t, "1", tx.Nonce)
				assert.Equal(t, "20000000000", tx.GasPrice)
				assert.Equal(t, "21000", tx.GasLimit)
				assert.Equal(t, "21000", tx.GasUsed)
				assert.Equal(t, "0.00042", tx.TxFee)
				assert.Equal(t, "success", tx.State)
				assert.Equal(t, "transfer", tx.TransactionType)
				return model.NewMockedResult(1, 1), nil
			})

		err := j.fetchRealtimeCoinTx(ctx, addr)
		assert.NoError(t, err)
	})

	t.Run("success with previous height", func(t *testing.T) {
		t.Parallel()
		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		lastHeight := int64(100)
		mockNormalTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(lastHeight, nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height: "100",
								},
							},
							{
								BaseTx: oklink.BaseTx{
									Height: "101",
								},
							},
						},
					},
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, txs []*model.NormalTransaction, _ *model.InsertBatchOption) (sql.Result, error) {
				assert.Len(t, txs, 1)
				assert.Equal(t, int64(101), txs[0].Height)
				return model.NewMockedResult(1, 1), nil
			})

		err := j.fetchRealtimeCoinTx(ctx, addr)
		assert.NoError(t, err)
	})

	t.Run("error when GetNormalTx fails", func(t *testing.T) {
		t.Parallel()
		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockNormalTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(0), nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("api error"))

		err := j.fetchRealtimeCoinTx(ctx, addr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get normal transaction")
	})

	t.Run("error when response code is not zero", func(t *testing.T) {
		t.Parallel()
		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockNormalTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(0), nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "1",
					Msg:  "api error",
				},
			}, nil)

		err := j.fetchRealtimeCoinTx(ctx, addr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get token transaction because of empty data")
	})

	t.Run("error when data is empty", func(t *testing.T) {
		t.Parallel()
		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockNormalTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(0), nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{},
			}, nil)

		err := j.fetchRealtimeCoinTx(ctx, addr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get token transaction because of empty data")
	})

	t.Run("error when InsertBatch fails", func(t *testing.T) {
		t.Parallel()
		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockNormalTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(0), nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height: "100",
								},
							},
						},
					},
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("insert error"))

		err := j.fetchRealtimeCoinTx(ctx, addr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to insert coin transactions")
	})
}

func TestJobManager_fetchBackCoinTx(t *testing.T) {
	t.Run("success with no end block height", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		txTime := int64(1677649200000)
		txHeight := int64(100)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), oklink.NormalTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
				},
			}).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									TxId:            "0x1",
									BlockHash:       "0xblock1",
									Height:          "100",
									From:            "0xfrom",
									To:              "0xto",
									Amount:          "1.0",
									Symbol:          "ETH",
									TransactionTime: strconv.FormatInt(txTime, 10),
									IsFromContract:  false,
									IsToContract:    true,
								},
								MethodID:        "0x123",
								Nonce:           "1",
								GasPrice:        "20000000000",
								GasLimit:        "21000",
								GasUsed:         "21000",
								TxFee:           "0.00042",
								State:           "success",
								TransactionType: "transfer",
							},
							{
								BaseTx: oklink.BaseTx{
									Height:          "101",
									TransactionTime: strconv.FormatInt(txTime+1000, 10),
								},
							},
						},
					},
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), &model.InsertBatchOption{
				IgnoreErrors:         true,
				OnDuplicateKeyUpdate: true,
			}).
			DoAndReturn(func(_ context.Context, txs []*model.NormalTransaction, _ *model.InsertBatchOption) (sql.Result, error) {
				assert.Len(t, txs, 2)
				tx := txs[0]
				assert.Equal(t, "0x1", tx.TransactionHash)
				assert.Equal(t, "0xblock1", tx.BlockHash)
				assert.Equal(t, int64(100), tx.Height)
				assert.Equal(t, "ETH", tx.CoinSymbol)
				assert.Equal(t, "0x123", tx.MethodId)
				assert.Equal(t, "1", tx.Nonce)
				assert.Equal(t, "20000000000", tx.GasPrice)
				assert.Equal(t, "21000", tx.GasLimit)
				assert.Equal(t, "21000", tx.GasUsed)
				assert.Equal(t, "0.00042", tx.TxFee)
				assert.Equal(t, "success", tx.State)
				assert.Equal(t, "transfer", tx.TransactionType)
				return model.NewMockedResult(1, 1), nil
			})

		startTime, startHeight, err := j.fetchBackCoinTx(ctx, addr, 0)
		assert.NoError(t, err)
		assert.Equal(t, txTime, startTime)
		assert.Equal(t, txHeight, startHeight)
	})

	t.Run("success with end block height", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		endBlockHeight := int64(200)
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		txTime := int64(1677649200000)
		txHeight := int64(100)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), oklink.NormalTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
					EndBlockHeight: "200",
				},
			}).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          strconv.FormatInt(txHeight, 10),
									TransactionTime: strconv.FormatInt(txTime, 10),
								},
							},
						},
					},
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil)

		startTime, startHeight, err := j.fetchBackCoinTx(ctx, addr, endBlockHeight)
		assert.NoError(t, err)
		assert.Equal(t, txTime, startTime)
		assert.Equal(t, txHeight, startHeight)
	})

	t.Run("error when GetNormalTx fails", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("api error"))

		startTime, startHeight, err := j.fetchBackCoinTx(ctx, addr, 0)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get normal transaction")
		assert.Equal(t, int64(0), startTime)
		assert.Equal(t, int64(0), startHeight)
	})

	t.Run("error when response code is not zero", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "1",
					Msg:  "api error",
				},
			}, nil)

		startTime, startHeight, err := j.fetchBackCoinTx(ctx, addr, 0)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get normal transaction")
		assert.Equal(t, int64(0), startTime)
		assert.Equal(t, int64(0), startHeight)
	})

	t.Run("error when data is empty", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{},
			}, nil)

		startTime, startHeight, err := j.fetchBackCoinTx(ctx, addr, 0)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get normal transaction")
		assert.Equal(t, int64(0), startTime)
		assert.Equal(t, int64(0), startHeight)
	})

	t.Run("error when InsertBatch fails", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: "1677649200000",
								},
							},
						},
					},
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("insert error"))

		startTime, startHeight, err := j.fetchBackCoinTx(ctx, addr, 0)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to insert coin transactions")
		assert.Equal(t, int64(0), startTime)
		assert.Equal(t, int64(0), startHeight)
	})
}

func TestJobManager_fetchBackTokenTx(t *testing.T) {
	t.Run("success with no end block height", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		txTime := int64(1677649200000)
		txHeight := int64(100)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), oklink.TokenTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
				},
				ProtocolType: oklink.DefaultProtocolType,
			}).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									TxId:            "0x1",
									BlockHash:       "0xblock1",
									Height:          "100",
									From:            "0xfrom",
									To:              "0xto",
									Amount:          "1.0",
									Symbol:          "USDT",
									TransactionTime: strconv.FormatInt(txTime, 10),
									IsFromContract:  false,
									IsToContract:    true,
								},
								TokenContractAddress: "0xtoken",
								TokenID:              "1",
							},
							{
								BaseTx: oklink.BaseTx{
									Height:          "101",
									TransactionTime: strconv.FormatInt(txTime+1000, 10),
								},
							},
						},
					},
				},
			}, nil)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), &model.InsertBatchOption{
				IgnoreErrors:         true,
				OnDuplicateKeyUpdate: true,
			}).
			DoAndReturn(func(_ context.Context, txs []*model.TokenTransaction, _ *model.InsertBatchOption) (sql.Result, error) {
				assert.Len(t, txs, 2)
				tx := txs[0]
				assert.Equal(t, "0x1", tx.TransactionHash)
				assert.Equal(t, "0xblock1", tx.BlockHash)
				assert.Equal(t, int64(100), tx.Height)
				assert.Equal(t, "USDT", tx.TokenSymbol)
				assert.Equal(t, "0xtoken", tx.TokenContractAddress)
				assert.Equal(t, "1", tx.TokenId)
				assert.Equal(t, "1.0", tx.Amount)
				assert.Equal(t, "0xfrom", tx.FromAddress)
				assert.Equal(t, "0xto", tx.ToAddress)
				return model.NewMockedResult(1, 1), nil
			})

		startTime, startHeight, err := j.fetchBackTokenTx(ctx, addr, 0)
		assert.NoError(t, err)
		assert.Equal(t, txTime, startTime)
		assert.Equal(t, txHeight, startHeight)
	})

	t.Run("success with end block height", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		endBlockHeight := int64(200)
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		txTime := int64(1677649200000)
		txHeight := int64(100)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), oklink.TokenTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
					EndBlockHeight: "200",
				},
				ProtocolType: oklink.DefaultProtocolType,
			}).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          strconv.FormatInt(txHeight, 10),
									TransactionTime: strconv.FormatInt(txTime, 10),
								},
							},
						},
					},
				},
			}, nil)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil)

		startTime, startHeight, err := j.fetchBackTokenTx(ctx, addr, endBlockHeight)
		assert.NoError(t, err)
		assert.Equal(t, txTime, startTime)
		assert.Equal(t, txHeight, startHeight)
	})

	t.Run("error when GetTokenTx fails", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("api error"))

		startTime, startHeight, err := j.fetchBackTokenTx(ctx, addr, 0)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get token transaction")
		assert.Equal(t, int64(0), startTime)
		assert.Equal(t, int64(0), startHeight)
	})

	t.Run("error when response code is not zero", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "1",
					Msg:  "api error",
				},
			}, nil)

		startTime, startHeight, err := j.fetchBackTokenTx(ctx, addr, 0)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get token transaction")
		assert.Equal(t, int64(0), startTime)
		assert.Equal(t, int64(0), startHeight)
	})

	t.Run("error when data is empty", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{},
			}, nil)

		startTime, startHeight, err := j.fetchBackTokenTx(ctx, addr, 0)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get token transaction")
		assert.Equal(t, int64(0), startTime)
		assert.Equal(t, int64(0), startHeight)
	})

	t.Run("error when InsertBatch fails", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: "1677649200000",
								},
							},
						},
					},
				},
			}, nil)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("insert error"))

		startTime, startHeight, err := j.fetchBackTokenTx(ctx, addr, 0)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to insert token transactions")
		assert.Equal(t, int64(0), startTime)
		assert.Equal(t, int64(0), startHeight)
	})
}

func TestJobManager_fetchHistoricalTokenTx(t *testing.T) {
	t.Run("success with single iteration", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}
		endTime := time.Unix(1677649200, 0)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), oklink.TokenTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
				},
				ProtocolType: oklink.DefaultProtocolType,
			}).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime.Add(-1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil)

		err := j.fetchHistoricalTokenTx(ctx, addr, endTime)
		assert.NoError(t, err)
	})

	t.Run("success with multiple iterations", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}
		endTime := time.Unix(1677649200, 0)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), oklink.TokenTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
				},
				ProtocolType: oklink.DefaultProtocolType,
			}).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "200",
									TransactionTime: strconv.FormatInt(endTime.Add(1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), oklink.TokenTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
					EndBlockHeight: "200",
				},
				ProtocolType: oklink.DefaultProtocolType,
			}).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime.Add(-1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil).
			Times(2)

		err := j.fetchHistoricalTokenTx(ctx, addr, endTime)
		assert.NoError(t, err)
	})

	t.Run("success with same height iteration", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}
		endTime := time.Unix(1677649200, 0)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), oklink.TokenTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
				},
				ProtocolType: oklink.DefaultProtocolType,
			}).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime.Add(1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), oklink.TokenTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
					EndBlockHeight: "100",
				},
				ProtocolType: oklink.DefaultProtocolType,
			}).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime.Add(1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil).
			Times(2)

		err := j.fetchHistoricalTokenTx(ctx, addr, endTime)
		assert.NoError(t, err)
	})

	t.Run("error when fetchBackTokenTx fails", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}
		endTime := time.Unix(1677649200, 0)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("api error"))

		err := j.fetchHistoricalTokenTx(ctx, addr, endTime)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to fetch token transactions")
	})

	t.Run("error when response code is not zero", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}
		endTime := time.Unix(1677649200, 0)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "1",
					Msg:  "api error",
				},
			}, nil)

		err := j.fetchHistoricalTokenTx(ctx, addr, endTime)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to fetch token transactions")
	})

	t.Run("error when InsertBatch fails", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}
		endTime := time.Unix(1677649200, 0)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime.Add(1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("insert error"))

		err := j.fetchHistoricalTokenTx(ctx, addr, endTime)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to fetch token transactions")
	})
}

func TestJobManager_fetchHistoricalCoinTx(t *testing.T) {
	t.Run("success with single iteration", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}
		endTime := time.Unix(1677649200, 0)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), oklink.NormalTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
				},
			}).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime.Add(-1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil)

		err := j.fetchHistoricalCoinTx(ctx, addr, endTime)
		assert.NoError(t, err)
	})

	t.Run("success with multiple iterations", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}
		endTime := time.Unix(1677649200, 0)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), oklink.NormalTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
				},
			}).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "200",
									TransactionTime: strconv.FormatInt(endTime.Add(1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), oklink.NormalTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
					EndBlockHeight: "200",
				},
			}).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime.Add(-1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil).
			Times(2)

		err := j.fetchHistoricalCoinTx(ctx, addr, endTime)
		assert.NoError(t, err)
	})

	t.Run("success with same height iteration", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}
		endTime := time.Unix(1677649200, 0)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), oklink.NormalTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
				},
			}).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime.Add(1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), oklink.NormalTxReq{
				BaseTxReq: oklink.BaseTxReq{
					ChainShortName: chain,
					Address:        address,
					Limit:          oklink.MaxLimit,
					EndBlockHeight: "100",
				},
			}).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime.Add(1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil).
			Times(2)

		err := j.fetchHistoricalCoinTx(ctx, addr, endTime)
		assert.NoError(t, err)
	})

	t.Run("error when fetchBackCoinTx fails", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}
		endTime := time.Unix(1677649200, 0)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("api error"))

		err := j.fetchHistoricalCoinTx(ctx, addr, endTime)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to fetch coin transactions")
	})

	t.Run("error when response code is not zero", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}
		endTime := time.Unix(1677649200, 0)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "1",
					Msg:  "api error",
				},
			}, nil)

		err := j.fetchHistoricalCoinTx(ctx, addr, endTime)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to fetch coin transactions")
	})

	t.Run("error when InsertBatch fails", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		addr := &model.SyncAddressLabel{
			ChainType: chain,
			Address:   address,
		}
		endTime := time.Unix(1677649200, 0)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime.Add(1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("insert error"))

		err := j.fetchHistoricalCoinTx(ctx, addr, endTime)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to fetch coin transactions")
	})
}

func TestJobManager_syncHistoricalCoinTx(t *testing.T) {
	t.Run("success with multiple addresses", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		endTime := time.Unix(1677649200, 0)

		addrs := []*model.SyncAddressLabel{
			{
				ChainType: "eth",
				Address:   "0x123",
			},
			{
				ChainType: "eth",
				Address:   "0x456",
			},
		}

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime.Add(-1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil).Times(2)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil).Times(2)

		err := j.syncHistoricalCoinTx(ctx, addrs, endTime)
		assert.NoError(t, err)
	})

	t.Run("error with empty address list", func(t *testing.T) {
		t.Parallel()

		j, _, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		endTime := time.Unix(1677649200, 0)

		addrs := []*model.SyncAddressLabel{}

		err := j.syncHistoricalCoinTx(ctx, addrs, endTime)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no coin address to sync historical tx")
	})
}

func TestJobManager_syncHistoricalTokenTx(t *testing.T) {
	t.Run("success with multiple addresses", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		endTime := time.Unix(1677649200, 0)

		addrs := []*model.SyncAddressLabel{
			{
				ChainType: "eth",
				Address:   "0x123",
			},
			{
				ChainType: "eth",
				Address:   "0x456",
			},
		}

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime.Add(-1*time.Hour).UnixMilli(), 10),
								},
							},
						},
					},
				},
			}, nil).Times(2)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil).Times(2)

		err := j.syncHistoricalTokenTx(ctx, addrs, endTime)
		assert.NoError(t, err)
	})

	t.Run("error with empty address list", func(t *testing.T) {
		t.Parallel()

		j, _, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		endTime := time.Unix(1677649200, 0)

		addrs := []*model.SyncAddressLabel{}

		err := j.syncHistoricalTokenTx(ctx, addrs, endTime)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no token address to sync historical tx")
	})
}

func TestJobManager_syncRealtimeCoinTx(t *testing.T) {
	t.Run("success with multiple addresses", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()

		addrs := []*model.SyncAddressLabel{
			{
				ChainType: "eth",
				Address:   "0x123",
			},
			{
				ChainType: "eth",
				Address:   "0x456",
			},
		}

		mockNormalTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), "eth", "0x123").
			Return(int64(100), nil)
		mockNormalTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), "eth", "0x456").
			Return(int64(100), nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									TxId:            "0x1",
									BlockHash:       "0xblock1",
									Height:          "101",
									From:            "0xfrom",
									To:              "0xto",
									Amount:          "1.0",
									Symbol:          "ETH",
									TransactionTime: "1677649200000",
									IsFromContract:  false,
									IsToContract:    true,
								},
								MethodID:        "0x123",
								Nonce:           "1",
								GasPrice:        "20000000000",
								GasLimit:        "21000",
								GasUsed:         "21000",
								TxFee:           "0.00042",
								State:           "success",
								TransactionType: "transfer",
							},
						},
					},
				},
			}, nil).Times(2)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), &model.InsertBatchOption{
				IgnoreErrors:         true,
				OnDuplicateKeyUpdate: true,
			}).
			Return(model.NewMockedResult(1, 1), nil).Times(2)

		err := j.syncRealtimeCoinTx(ctx, addrs)
		assert.NoError(t, err)
	})

	t.Run("success with one address success and one failure", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()

		addrs := []*model.SyncAddressLabel{
			{
				ChainType: "eth",
				Address:   "0x123",
			},
			{
				ChainType: "eth",
				Address:   "0x456",
			},
		}

		mockNormalTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), "eth", "0x123").
			Return(int64(100), nil)
		mockNormalTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), "eth", "0x456").
			Return(int64(100), nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "101",
									TransactionTime: "1677649200000",
								},
							},
						},
					},
				},
			}, nil).Times(1)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("api error")).Times(1)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil).Times(1)

		err := j.syncRealtimeCoinTx(ctx, addrs)
		assert.NoError(t, err)
	})

	t.Run("error with empty address list", func(t *testing.T) {
		t.Parallel()

		j, _, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()

		addrs := []*model.SyncAddressLabel{}

		err := j.syncRealtimeCoinTx(ctx, addrs)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no coin address to sync")
	})
}

func TestJobManager_syncRealtimeTokenTx(t *testing.T) {
	t.Run("success with multiple addresses", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()

		addrs := []*model.SyncAddressLabel{
			{
				ChainType: "eth",
				Address:   "0x123",
			},
			{
				ChainType: "eth",
				Address:   "0x456",
			},
		}

		mockTokenTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), "eth", "0x123").
			Return(int64(100), nil)
		mockTokenTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), "eth", "0x456").
			Return(int64(100), nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									TxId:            "0x1",
									BlockHash:       "0xblock1",
									Height:          "101",
									From:            "0xfrom",
									To:              "0xto",
									Amount:          "1.0",
									Symbol:          "USDT",
									TransactionTime: "1677649200000",
									IsFromContract:  false,
									IsToContract:    true,
								},
								TokenContractAddress: "0xtoken",
								TokenID:              "1",
							},
						},
					},
				},
			}, nil).Times(2)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), &model.InsertBatchOption{
				IgnoreErrors:         true,
				OnDuplicateKeyUpdate: true,
			}).
			Return(model.NewMockedResult(1, 1), nil).Times(2)

		err := j.syncRealtimeTokenTx(ctx, addrs)
		assert.NoError(t, err)
	})

	t.Run("success with one address success and one failure", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, _, _, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()

		addrs := []*model.SyncAddressLabel{
			{
				ChainType: "eth",
				Address:   "0x123",
			},
			{
				ChainType: "eth",
				Address:   "0x456",
			},
		}

		mockTokenTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), "eth", "0x123").
			Return(int64(100), nil)
		mockTokenTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), "eth", "0x456").
			Return(int64(100), nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "101",
									TransactionTime: "1677649200000",
								},
							},
						},
					},
				},
			}, nil).Times(1)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("api error")).Times(1)

		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil).Times(1)

		err := j.syncRealtimeTokenTx(ctx, addrs)
		assert.NoError(t, err)
	})

	t.Run("error with empty address list", func(t *testing.T) {
		t.Parallel()

		j, _, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()

		addrs := []*model.SyncAddressLabel{}

		err := j.syncRealtimeTokenTx(ctx, addrs)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no token address to sync")
	})
}

func TestJobManager_SyncRealtimeTx(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, mockSyncAddressLabelModel, mockNormalTransactionModel, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"

		mockSyncAddressLabelModel.EXPECT().
			FindAllByChain(gomock.Any(), chain).
			Return([]*model.SyncAddressLabel{
				{
					ChainType: chain,
					Address:   address,
					TxType:    model.TxTypeBoth,
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(100), nil)
		mockTokenTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(100), nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "101",
									TransactionTime: "1677649200000",
								},
							},
						},
					},
				},
			}, nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "101",
									TransactionTime: "1677649200000",
								},
							},
						},
					},
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil)
		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil)

		result, err := j.SyncRealtimeTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: fmt.Sprintf(`{"chains":["%s"]}`, chain),
		})

		assert.NoError(t, err)
		assert.Equal(t, "success", result)
	})

	t.Run("error when getTxChain fails", func(t *testing.T) {
		t.Parallel()

		j, _, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()

		result, err := j.SyncRealtimeTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: "invalid json",
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get tx chain")
		assert.Equal(t, "failed", result)
	})

	t.Run("error when FindAllByChain fails", func(t *testing.T) {
		t.Parallel()

		j, _, mockSyncAddressLabelModel, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"

		mockSyncAddressLabelModel.EXPECT().
			FindAllByChain(gomock.Any(), chain).
			Return(nil, fmt.Errorf("database error"))

		result, err := j.SyncRealtimeTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: fmt.Sprintf(`{"chains":["%s"]}`, chain),
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no address to sync")
		assert.Equal(t, "failed", result)
	})

	t.Run("error when no addresses found", func(t *testing.T) {
		t.Parallel()

		j, _, mockSyncAddressLabelModel, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"

		mockSyncAddressLabelModel.EXPECT().
			FindAllByChain(gomock.Any(), chain).
			Return([]*model.SyncAddressLabel{}, nil)

		result, err := j.SyncRealtimeTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: fmt.Sprintf(`{"chains":["%s"]}`, chain),
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no address to sync")
		assert.Equal(t, "failed", result)
	})

	t.Run("error when syncRealtimeCoinTx fails", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, mockSyncAddressLabelModel, mockNormalTransactionModel, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"

		mockSyncAddressLabelModel.EXPECT().
			FindAllByChain(gomock.Any(), chain).
			Return([]*model.SyncAddressLabel{
				{
					ChainType: chain,
					Address:   address,
					TxType:    model.TxTypeNormal,
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			FindLastHeight(gomock.Any(), chain, address).
			Return(int64(100), nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("api error"))

		result, err := j.SyncRealtimeTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: fmt.Sprintf(`{"chains":["%s"]}`, chain),
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to sync transactions")
		assert.Equal(t, "failed", result)
	})
}

func TestJobManager_SyncHistoricalTx(t *testing.T) {
	t.Run("success with both coin and token transactions", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, mockSyncAddressLabelModel, mockNormalTransactionModel, mockTokenTransactionModel, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		endTime := time.Now().Add(-24 * time.Hour).Unix()

		mockSyncAddressLabelModel.EXPECT().
			FindAllByChain(gomock.Any(), chain).
			Return([]*model.SyncAddressLabel{
				{
					ChainType: chain,
					Address:   address,
					TxType:    model.TxTypeBoth,
				},
			}, nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(&oklink.NormalTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.NormalTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.NormalTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime-3600, 10),
								},
							},
						},
					},
				},
			}, nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(&oklink.TokenTxResp{
				BaseResponse: oklink.BaseResponse{
					Code: "0",
					Msg:  "success",
				},
				Data: []struct {
					oklink.PaginationData
					TransactionList []oklink.TokenTx `json:"transactionList"`
				}{
					{
						TransactionList: []oklink.TokenTx{
							{
								BaseTx: oklink.BaseTx{
									Height:          "100",
									TransactionTime: strconv.FormatInt(endTime-3600, 10),
								},
							},
						},
					},
				},
			}, nil)

		mockNormalTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil)
		mockTokenTransactionModel.EXPECT().
			InsertBatch(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewMockedResult(1, 1), nil)

		result, err := j.SyncHistoricalTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: fmt.Sprintf(`{"chains":["%s"],"endTimestampSec":%d}`, chain, endTime),
		})

		assert.NoError(t, err)
		assert.Equal(t, "success", result)
	})

	t.Run("error when executor params is empty", func(t *testing.T) {
		t.Parallel()

		j, _, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()

		result, err := j.SyncHistoricalTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: "",
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "executor params is empty")
		assert.Equal(t, "failed", result)
	})

	t.Run("error when executor params is invalid json", func(t *testing.T) {
		t.Parallel()

		j, _, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()

		result, err := j.SyncHistoricalTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: "invalid json",
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to unmarshal executor params error")
		assert.Equal(t, "failed", result)
	})

	t.Run("error when endTimestampSec is invalid", func(t *testing.T) {
		t.Parallel()

		j, _, _, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		futureTime := time.Now().Add(24 * time.Hour).Unix()

		result, err := j.SyncHistoricalTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: fmt.Sprintf(`{"chains":["%s"],"endTimestampSec":%d}`, chain, futureTime),
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "endTimestampSec is not valid")
		assert.Equal(t, "failed", result)

		result, err = j.SyncHistoricalTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: fmt.Sprintf(`{"chains":["%s"],"endTimestampSec":%d}`, chain, -1),
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "endTimestampSec is not valid")
		assert.Equal(t, "failed", result)
	})

	t.Run("error when no addresses found", func(t *testing.T) {
		t.Parallel()

		j, _, mockSyncAddressLabelModel, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		endTime := time.Now().Add(-24 * time.Hour).Unix()

		mockSyncAddressLabelModel.EXPECT().
			FindAllByChain(gomock.Any(), chain).
			Return([]*model.SyncAddressLabel{}, nil)

		result, err := j.SyncHistoricalTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: fmt.Sprintf(`{"chains":["%s"],"endTimestampSec":%d}`, chain, endTime),
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to pull data from db")
		assert.Equal(t, "failed", result)
	})

	t.Run("error when syncHistoricalCoinTx fails", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, mockSyncAddressLabelModel, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		endTime := time.Now().Add(-24 * time.Hour).Unix()

		mockSyncAddressLabelModel.EXPECT().
			FindAllByChain(gomock.Any(), chain).
			Return([]*model.SyncAddressLabel{
				{
					ChainType: chain,
					Address:   address,
					TxType:    model.TxTypeNormal,
				},
			}, nil)

		mockOklinkClient.EXPECT().
			GetNormalTx(gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("api error"))

		result, err := j.SyncHistoricalTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: fmt.Sprintf(`{"chains":["%s"],"endTimestampSec":%d}`, chain, endTime),
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to sync historical transactions")
		assert.Equal(t, "failed", result)
	})

	t.Run("error when syncHistoricalTokenTx fails", func(t *testing.T) {
		t.Parallel()

		j, mockOklinkClient, mockSyncAddressLabelModel, _, _, ctrl := setupTest(t)
		defer ctrl.Finish()

		ctx := context.Background()
		chain := "eth"
		address := "0x123"
		endTime := time.Now().Add(-24 * time.Hour).Unix()

		mockSyncAddressLabelModel.EXPECT().
			FindAllByChain(gomock.Any(), chain).
			Return([]*model.SyncAddressLabel{
				{
					ChainType: chain,
					Address:   address,
					TxType:    model.TxTypeToken,
				},
			}, nil)

		mockOklinkClient.EXPECT().
			GetTokenTx(gomock.Any(), gomock.Any()).
			Return(nil, fmt.Errorf("api error"))

		result, err := j.SyncHistoricalTx(ctx, &zcron.XxlJobParam{
			ExecutorParams: fmt.Sprintf(`{"chains":["%s"],"endTimestampSec":%d}`, chain, endTime),
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to sync historical transactions")
		assert.Equal(t, "failed", result)
	})
}
