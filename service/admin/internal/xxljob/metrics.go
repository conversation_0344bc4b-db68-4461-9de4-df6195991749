package xxljob

import (
	"time"

	"code.bydev.io/frameworks/byone/core/metric"
)

const (
	StatusSuccess    = "success"
	StatusFailed     = "failed"
	StatusDBFailed   = "db_failed"
	StatusDBSuccess  = "db_success"
	StatusAPIFailed  = "api_failed"
	StatusAPISuccess = "api_success"

	ChainTypeNormal  = "normal"
	ChainTypeToken   = "token"
	ChainTypeUnknown = "unknown"

	TaskTypeRealtime = "realtime"
	TaskTypeHistory  = "history"
)

var (
	metricSyncTxChain = metric.NewCounterVec(
		&metric.CounterVecOpts{
			Namespace: "aml_insight",
			Subsystem: "xxljob",
			Name:      "sync_tx_chain_total",
			Help:      "count of sync tx chain",
			Labels:    []string{"chain", "status", "chain_type", "task_type"},
		},
	)

	metricSyncTxChainDuration = metric.NewHistogramVec(
		&metric.HistogramVecOpts{
			Namespace: "aml_insight",
			Subsystem: "xxljob",
			Name:      "sync_tx_chain_duration",
			Help:      "duration(ms) of sync tx chain",
			Labels:    []string{"task_type", "status"},
			Buckets:   []float64{100, 200, 500, 1000, 2000, 5000, 10000},
		},
	)
)

func incSyncTxChain(chain string, status string, chainType string, taskType string) {
	metricSyncTxChain.Inc(chain, status, chainType, taskType)
}

func observeSyncTxChainDuration(taskType string, status string, duration time.Duration) {
	metricSyncTxChainDuration.Observe(duration.Milliseconds(), taskType, status)
}
