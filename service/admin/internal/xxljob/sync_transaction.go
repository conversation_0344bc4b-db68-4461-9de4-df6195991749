package xxljob

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"sync"
	"time"

	"code.bydev.io/frameworks/byone/zcron"

	"aml-insight/internal/model"
	"aml-insight/internal/pkg/oklink"

	"code.bydev.io/frameworks/byone/core/logc"
	"golang.org/x/sync/errgroup"
)

type TxParam struct {
	Chains          []string `json:"chains"`
	Address         string   `json:"address"`
	EndTimestampSec int64    `json:"endTimestampSec"`
}

// SyncRealtimeTx 同步实时交易数据，周期性任务
func (j *JobManager) SyncRealtimeTx(ctx context.Context, param *zcron.XxlJobParam) (result string, err error) {
	logc.Infow(ctx, "start to SyncRealtimeTx", logc.Field("param", param))

	defer func(start time.Time) {
		if err != nil {
			observeSyncTxChainDuration(TaskTypeRealtime, StatusFailed, time.Since(start))
		} else {
			observeSyncTxChainDuration(TaskTypeRealtime, StatusSuccess, time.Since(start))
		}
	}(time.Now())

	// 获取需要同步的chain
	chains, err := j.getTxChain(ctx, param)
	if err != nil {
		logc.Errorw(ctx, "failed to get tx chain", logc.Field("error", err))
		return "failed", fmt.Errorf("failed to get tx chain: %w", err)
	}

	// 获取需要同步的地址并且进行分类
	coinAddrs := make([]*model.SyncAddressLabel, 0)
	tokenAddrs := make([]*model.SyncAddressLabel, 0)

	for _, chain := range chains {
		addressLabels, err := j.svc.SyncAddressLabelModel.FindAllByChain(ctx, chain)
		if err != nil {
			logc.Warnw(ctx, "failed to find chain error", logc.Field("chain", chain), logc.Field("error", err))
			incSyncTxChain(chain, StatusDBFailed, ChainTypeUnknown, TaskTypeRealtime)
			continue
		}
		incSyncTxChain(chain, StatusDBSuccess, ChainTypeNormal, TaskTypeRealtime)

		for _, addressLabel := range addressLabels {
			switch addressLabel.TxType {
			case model.TxTypeNormal:
				coinAddrs = append(coinAddrs, addressLabel)
			case model.TxTypeToken:
				tokenAddrs = append(tokenAddrs, addressLabel)
			case model.TxTypeBoth:
				coinAddrs = append(coinAddrs, addressLabel)
				tokenAddrs = append(tokenAddrs, addressLabel)
			default:
				logc.Warnw(ctx, "tx_type is not valid", logc.Field("tx_type", addressLabel.TxType))
			}
		}
	}

	logc.Infow(ctx, "SyncRealtimeTx, get address size", logc.Field("coinAddrs_size", len(coinAddrs)),
		logc.Field("tokenAddrs_size", len(tokenAddrs)))

	if len(coinAddrs) == 0 && len(tokenAddrs) == 0 {
		return "failed", fmt.Errorf("no address to sync, chains: %v", chains)
	}

	// 使用errgroup并发同步交易数据
	g, ctx := errgroup.WithContext(ctx)
	g.Go(func() error {
		if err := j.syncRealtimeCoinTx(ctx, coinAddrs); err != nil {
			logc.Warnw(ctx, "failed to sync coin transactions", logc.Field("error", err))
			return fmt.Errorf("failed to sync coin transactions: %w", err)
		}
		return nil
	})

	g.Go(func() error {
		if err := j.syncRealtimeTokenTx(ctx, tokenAddrs); err != nil {
			logc.Warnw(ctx, "failed to sync token transactions", logc.Field("error", err))
			return fmt.Errorf("failed to sync token transactions: %w", err)
		}
		return nil
	})

	if err := g.Wait(); err != nil {
		logc.Warnw(ctx, "failed to sync transactions", logc.Field("error", err))
		return "failed", fmt.Errorf("failed to sync transactions: %w", err)
	}

	logc.Infow(ctx, "SyncRealtimeTx, success")
	return "success", nil
}

func (j *JobManager) getTxChain(ctx context.Context, param *zcron.XxlJobParam) ([]string, error) {
	if param.ExecutorParams != "" {
		tParam := &TxParam{}
		err := json.Unmarshal([]byte(param.ExecutorParams), tParam)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal params error: %w", err)
		}
		return tParam.Chains, nil
	}

	chains, err := j.svc.SyncAddressLabelModel.FindAllChain(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to find all chain error: %w", err)
	}
	return chains, nil
}

func (j *JobManager) syncRealtimeTokenTx(ctx context.Context, addrs []*model.SyncAddressLabel) error {
	if len(addrs) == 0 {
		return fmt.Errorf("no token address to sync")
	}

	wg := sync.WaitGroup{}
	for _, addr := range addrs {
		addr_ := addr
		wg.Add(1)
		j.workerPool.Schedule(func() {
			defer wg.Done()
			if err := j.fetchRealtimeTokenTx(ctx, addr_); err != nil {
				logc.Warnw(ctx, "failed to fetch realtime token tx", logc.Field("chain", addr_.ChainType),
					logc.Field("address", addr_.Address), logc.Field("error", err))
				incSyncTxChain(addr_.ChainType, StatusAPIFailed, ChainTypeToken, TaskTypeRealtime)
				return
			}

			incSyncTxChain(addr_.ChainType, StatusAPISuccess, ChainTypeToken, TaskTypeRealtime)
			logc.Infow(ctx, "success to fetch realtime token tx",
				logc.Field("chain", addr_.ChainType), logc.Field("addr", addr_.Address))
		})
	}
	wg.Wait()

	return nil
}

func (j *JobManager) fetchRealtimeTokenTx(ctx context.Context, addr *model.SyncAddressLabel) error {
	lastHeight, err_ := j.svc.TokenTransactionModel.FindLastHeight(ctx, addr.ChainType, addr.Address)
	logc.Infow(ctx, "fetch realtime token tx", logc.Field("chain", addr.ChainType),
		logc.Field("addr", addr.Address), logc.Field("lastHeight", lastHeight), logc.Field("error", err_))

	req := oklink.TokenTxReq{
		BaseTxReq: oklink.BaseTxReq{
			ChainShortName: addr.ChainType,
			Address:        addr.Address,
			Limit:          oklink.MaxLimit,
		},
		ProtocolType: oklink.DefaultProtocolType,
	}

	if lastHeight > 0 {
		req.StartBlockHeight = strconv.FormatInt(lastHeight, 10)
	}

	resp, err := j.svc.OklinkClient.GetTokenTx(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to get token transaction, err: %w", err)
	}

	if resp.Code != "0" || len(resp.Data) == 0 || len(resp.Data[0].TransactionList) == 0 {
		return fmt.Errorf("failed to get token transaction because of empty data, msg: %s", resp.Msg)
	}

	// 按照height排序，从小到大排序
	sort.Slice(resp.Data[0].TransactionList, func(i, j int) bool {
		return resp.Data[0].TransactionList[i].Height < resp.Data[0].TransactionList[j].Height
	})

	// 检查第一项height是否与lastHeight相等，如果相等则移除
	if resp.Data[0].TransactionList[0].GetHeight() == lastHeight && lastHeight > 0 {
		resp.Data[0].TransactionList = resp.Data[0].TransactionList[1:]
	}

	// 开始插入db
	tokenTxs := make([]*model.TokenTransaction, 0)
	for _, tx := range resp.Data[0].TransactionList {
		tokenTxs = append(tokenTxs, &model.TokenTransaction{
			TransactionHash:      tx.TxId,
			Address:              addr.Address,
			BlockHash:            tx.BlockHash,
			ChainType:            addr.ChainType,
			Height:               tx.GetHeight(),
			TransactionTime:      tx.GetTransactionTime(),
			FromAddress:          tx.From,
			ToAddress:            tx.To,
			IsFromContract:       tx.GetIsFromContract(),
			IsToContract:         tx.GetIsToContract(),
			Amount:               tx.Amount,
			TokenSymbol:          tx.Symbol,
			TokenContractAddress: tx.TokenContractAddress,
			TokenId:              tx.TokenID,
			CreatedAt:            time.Now(),
			UpdatedAt:            time.Now(),
		})
	}

	if _, err = j.svc.TokenTransactionModel.InsertBatch(ctx, tokenTxs, &model.InsertBatchOption{
		IgnoreErrors: true, OnDuplicateKeyUpdate: true}); err != nil {
		return fmt.Errorf("failed to insert token transactions: %w", err)
	}

	return nil
}

func (j *JobManager) syncRealtimeCoinTx(ctx context.Context, addrs []*model.SyncAddressLabel) error {
	if len(addrs) == 0 {
		return fmt.Errorf("no coin address to sync")
	}

	wg := sync.WaitGroup{}
	for _, addr := range addrs {
		addr_ := addr
		wg.Add(1)
		j.workerPool.Schedule(func() {
			defer wg.Done()
			if err := j.fetchRealtimeCoinTx(ctx, addr_); err != nil {
				logc.Warnw(ctx, "failed to fetch realtime coin tx", logc.Field("chain", addr_.ChainType),
					logc.Field("address", addr_.Address), logc.Field("error", err))
				incSyncTxChain(addr_.ChainType, StatusAPIFailed, ChainTypeNormal, TaskTypeRealtime)
				return
			}
			incSyncTxChain(addr_.ChainType, StatusAPISuccess, ChainTypeNormal, TaskTypeRealtime)
			logc.Infow(ctx, "success to fetch realtime coin tx",
				logc.Field("chain", addr_.ChainType), logc.Field("addr", addr_.Address))
		})
	}
	wg.Wait()

	return nil
}

func (j *JobManager) fetchRealtimeCoinTx(ctx context.Context, addr *model.SyncAddressLabel) error {
	lastHeight, err := j.svc.NormalTransactionModel.FindLastHeight(ctx, addr.ChainType, addr.Address)
	logc.Infow(ctx, "fetch realtime coin tx", logc.Field("chain", addr.ChainType), logc.Field("addr", addr.Address),
		logc.Field("lastHeight", lastHeight), logc.Field("error", err))

	req := oklink.NormalTxReq{
		BaseTxReq: oklink.BaseTxReq{
			ChainShortName: addr.ChainType,
			Address:        addr.Address,
			Limit:          oklink.MaxLimit,
		},
	}

	if lastHeight > 0 {
		req.StartBlockHeight = strconv.FormatInt(lastHeight, 10)
	}

	resp, err := j.svc.OklinkClient.GetNormalTx(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to get normal transaction, err: %w", err)
	}

	if resp.Code != "0" || len(resp.Data) == 0 || len(resp.Data[0].TransactionList) == 0 {
		return fmt.Errorf("failed to get token transaction because of empty data, msg: %s", resp.Msg)
	}

	// 按照height排序，从小到大排序
	sort.Slice(resp.Data[0].TransactionList, func(i, j int) bool {
		return resp.Data[0].TransactionList[i].Height < resp.Data[0].TransactionList[j].Height
	})

	// 检查第一项height是否与lastHeight相等，如果相等则移除
	if resp.Data[0].TransactionList[0].GetHeight() == lastHeight && lastHeight > 0 {
		resp.Data[0].TransactionList = resp.Data[0].TransactionList[1:]
	}

	// 开始插入db
	coinTxs := make([]*model.NormalTransaction, 0)
	for _, tx := range resp.Data[0].TransactionList {
		coinTxs = append(coinTxs, &model.NormalTransaction{
			TransactionHash: tx.TxId,
			BlockHash:       tx.BlockHash,
			Address:         addr.Address,
			ChainType:       addr.ChainType,
			MethodId:        tx.MethodID,
			Nonce:           tx.Nonce,
			GasPrice:        tx.GasPrice,
			GasLimit:        tx.GasLimit,
			GasUsed:         tx.GasUsed,
			Height:          tx.GetHeight(),
			TransactionTime: tx.GetTransactionTime(),
			FromAddress:     tx.From,
			ToAddress:       tx.To,
			IsFromContract:  tx.GetIsFromContract(),
			IsToContract:    tx.GetIsToContract(),
			Amount:          tx.Amount,
			CoinSymbol:      tx.Symbol,
			TxFee:           tx.TxFee,
			State:           tx.State,
			TransactionType: tx.TransactionType,
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		})
	}

	if _, err = j.svc.NormalTransactionModel.InsertBatch(ctx, coinTxs,
		&model.InsertBatchOption{IgnoreErrors: true, OnDuplicateKeyUpdate: true}); err != nil {
		return fmt.Errorf("failed to insert coin transactions: %w", err)
	}

	return nil
}

// SyncHistoricalTx 同步历史交易数据 -- 临时任务
func (j *JobManager) SyncHistoricalTx(ctx context.Context, param *zcron.XxlJobParam) (result string, err error) {
	logc.Infow(ctx, "start to SyncHistoricalTx", logc.Field("param", param))
	defer func(start time.Time) {
		if err != nil {
			observeSyncTxChainDuration(TaskTypeHistory, StatusFailed, time.Since(start))
		} else {
			observeSyncTxChainDuration(TaskTypeHistory, StatusSuccess, time.Since(start))
		}
	}(time.Now())

	// 任务参数需要携带：1. 指定chain, 2. 拉取距离现在多久之前的数据，提供一个时间段（单位：s）
	exParams := param.ExecutorParams // '{"chain":"eth","endTimestampSec":1713878400}'
	if exParams == "" {
		logc.Errorw(ctx, "executor params is empty")
		return "failed", fmt.Errorf("executor params is empty")
	}
	tParam := &TxParam{}
	err = json.Unmarshal([]byte(exParams), tParam)
	if err != nil {
		logc.Errorw(ctx, "failed to unmarshal executor params error", logc.Field("params", exParams),
			logc.Field("error", err))
		return "failed", fmt.Errorf("failed to unmarshal executor params error: %w", err)
	}

	if tParam.EndTimestampSec <= 0 || tParam.EndTimestampSec > time.Now().Unix() {
		logc.Errorw(ctx, "endTimestampSec is not valid", logc.Field("endTimestampSec", tParam.EndTimestampSec))
		return "failed", fmt.Errorf("endTimestampSec is not valid")
	}

	// 通过tx_type 区分本币交易和代币交易
	coinAddrs := make([]*model.SyncAddressLabel, 0)
	tokenAddrs := make([]*model.SyncAddressLabel, 0)

	for _, chain := range tParam.Chains {
		addressLabels, err := j.svc.SyncAddressLabelModel.FindAllByChain(ctx, chain)
		if err != nil {
			logc.Warnw(ctx, "failed to find all by chain error", logc.Field("chain", chain), logc.Field("error", err))
			continue
		}

		for _, addressLabel := range addressLabels {
			switch addressLabel.TxType {
			case model.TxTypeNormal:
				coinAddrs = append(coinAddrs, addressLabel)
			case model.TxTypeToken:
				tokenAddrs = append(tokenAddrs, addressLabel)
			case model.TxTypeBoth:
				coinAddrs = append(coinAddrs, addressLabel)
				tokenAddrs = append(tokenAddrs, addressLabel)
			default:
				logc.Warnw(ctx, "tx_type is not valid", logc.Field("tx_type", addressLabel.TxType))
			}
		}
	}

	logc.Infow(ctx, "SyncHistoricalTx, get address size", logc.Field("coinAddrs_size", len(coinAddrs)),
		logc.Field("tokenAddrs_size", len(tokenAddrs)))

	if len(coinAddrs) == 0 && len(tokenAddrs) == 0 {
		return "failed", fmt.Errorf("failed to pull data from db, params: %s", exParams)
	}

	// 使用errgroup并发同步交易数据
	g, ctx := errgroup.WithContext(ctx)
	g.Go(func() error {
		if err := j.syncHistoricalCoinTx(ctx, coinAddrs, time.Unix(tParam.EndTimestampSec, 0)); err != nil {
			logc.Warnw(ctx, "failed to sync historical coin transactions", logc.Field("error", err))
			return fmt.Errorf("failed to sync historical coin transactions: %w", err)
		}
		return nil
	})

	g.Go(func() error {
		if err := j.syncHistoricalTokenTx(ctx, tokenAddrs, time.Unix(tParam.EndTimestampSec, 0)); err != nil {
			logc.Warnw(ctx, "failed to sync historical token transactions", logc.Field("error", err))
			return fmt.Errorf("failed to sync historical token transactions: %w", err)
		}
		return nil
	})

	if err := g.Wait(); err != nil {
		logc.Warnw(ctx, "failed to sync historical transactions", logc.Field("error", err))
		return "failed", fmt.Errorf("failed to sync historical transactions: %w", err)
	}

	logc.Infow(ctx, "SyncHistoricalTx, success")
	return "success", nil
}

// syncHistoricalCoinTx 同步本币交易数据
func (j *JobManager) syncHistoricalCoinTx(ctx context.Context, addrs []*model.SyncAddressLabel,
	endTime time.Time) error {
	if len(addrs) == 0 {
		return fmt.Errorf("no coin address to sync historical tx")
	}

	wg := sync.WaitGroup{}
	for _, addr := range addrs {
		addr_ := addr
		wg.Add(1)
		j.workerPool.Schedule(func() {
			defer wg.Done()
			if err := j.fetchHistoricalCoinTx(ctx, addr_, endTime); err != nil {
				logc.Warnw(ctx, "failed to fetch historical coin transactions", logc.Field("chain", addr_.ChainType),
					logc.Field("address", addr_.Address), logc.Field("error", err))
				return
			}
		})
	}
	wg.Wait()

	return nil
}

// syncHistoricalTokenTx 同步代币交易数据
func (j *JobManager) syncHistoricalTokenTx(ctx context.Context, addrs []*model.SyncAddressLabel,
	endTime time.Time) error {
	if len(addrs) == 0 {
		return fmt.Errorf("no token address to sync historical tx")
	}

	wg := sync.WaitGroup{}
	for _, addr := range addrs {
		addr_ := addr
		wg.Add(1)
		j.workerPool.Schedule(func() {
			defer wg.Done()
			if err := j.fetchHistoricalTokenTx(ctx, addr_, endTime); err != nil {
				logc.Warnw(ctx, "failed to fetch historical token transactions", logc.Field("chain", addr_.ChainType),
					logc.Field("address", addr_.Address), logc.Field("error", err))
				return
			}
		})
	}
	wg.Wait()

	return nil
}

// fetchHistoricalCoinTx 获取历史本币交易数据
func (j *JobManager) fetchHistoricalCoinTx(ctx context.Context, addr *model.SyncAddressLabel, endTime time.Time) error {
	endBlockHeight := int64(0)

	for {
		startTime, startHeight, err := j.fetchBackCoinTx(ctx, addr, endBlockHeight)
		if err != nil {
			return fmt.Errorf("failed to fetch coin transactions, addr:%s, endBlockHeight:%d, error: %w",
				addr.Address, endBlockHeight, err)
		}

		if startTime <= endTime.UnixMilli() {
			logc.Infow(ctx, "finished fetch historical coin tx, when startTime <= endTime",
				logc.Field("addr", addr.Address), logc.Field("endBlockHeight", endBlockHeight),
				logc.Field("startTime", startTime))
			break
		}

		if startHeight == endBlockHeight {
			logc.Infow(ctx, "finished fetch historical coin tx, when startHeight == endBlockHeight",
				logc.Field("addr", addr.Address), logc.Field("endBlockHeight", endBlockHeight),
				logc.Field("startTime", startTime))
			break
		}

		endBlockHeight = startHeight
	}
	return nil
}

// fetchBackCoinTx 获取本币交易数据
func (j *JobManager) fetchBackCoinTx(ctx context.Context, addr *model.SyncAddressLabel,
	endBlockHeight int64) (startTime, startHeight int64, err error) {
	req := oklink.NormalTxReq{
		BaseTxReq: oklink.BaseTxReq{
			ChainShortName: addr.ChainType,
			Address:        addr.Address,
			Limit:          oklink.MaxLimit,
		},
	}
	if endBlockHeight > 0 {
		req.EndBlockHeight = strconv.FormatInt(endBlockHeight, 10)
	}

	resp, err := j.svc.OklinkClient.GetNormalTx(ctx, req)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get normal transaction, chain:%s, addr:%s, endBlockHeight:%d, error: %w",
			addr.ChainType, addr.Address, endBlockHeight, err)
	}

	if resp.Code != "0" || len(resp.Data) == 0 || len(resp.Data[0].TransactionList) == 0 {
		return 0, 0, fmt.Errorf("failed to get normal transaction, chain:%s, addr:%s, endBlockHeight:%d, error: %s",
			addr.ChainType, addr.Address, endBlockHeight, resp.Msg)
	}

	// 按照height排序，从小到大排序
	sort.Slice(resp.Data[0].TransactionList, func(i, j int) bool {
		return resp.Data[0].TransactionList[i].Height < resp.Data[0].TransactionList[j].Height
	})

	// 开始插入db
	coinTxs := make([]*model.NormalTransaction, 0)
	for _, tx := range resp.Data[0].TransactionList {
		coinTxs = append(coinTxs, &model.NormalTransaction{
			TransactionHash: tx.TxId,
			BlockHash:       tx.BlockHash,
			Address:         addr.Address,
			ChainType:       addr.ChainType,
			MethodId:        tx.MethodID,
			Nonce:           tx.Nonce,
			GasPrice:        tx.GasPrice,
			GasLimit:        tx.GasLimit,
			GasUsed:         tx.GasUsed,
			Height:          tx.GetHeight(),
			TransactionTime: tx.GetTransactionTime(),
			FromAddress:     tx.From,
			ToAddress:       tx.To,
			IsFromContract:  tx.GetIsFromContract(),
			IsToContract:    tx.GetIsToContract(),
			Amount:          tx.Amount,
			CoinSymbol:      tx.Symbol,
			TxFee:           tx.TxFee,
			State:           tx.State,
			TransactionType: tx.TransactionType,
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		})
	}

	if _, err = j.svc.NormalTransactionModel.InsertBatch(ctx, coinTxs, &model.InsertBatchOption{IgnoreErrors: true,
		OnDuplicateKeyUpdate: true}); err != nil {
		return 0, 0, fmt.Errorf("failed to insert coin transactions, chain:%s, addr:%s, endBlockHeight:%d, error: %w",
			addr.ChainType, addr.Address, endBlockHeight, err)
	}

	return resp.Data[0].TransactionList[0].GetTransactionTime(),
		resp.Data[0].TransactionList[0].GetHeight(), nil
}

func (j *JobManager) fetchHistoricalTokenTx(ctx context.Context, addr *model.SyncAddressLabel, endTime time.Time) error {
	endBlockHeight := int64(0)

	for {
		startTime, startHeight, err := j.fetchBackTokenTx(ctx, addr, endBlockHeight)
		if err != nil {
			return fmt.Errorf("failed to fetch token transactions: %w", err)
		}

		if startTime <= endTime.UnixMilli() {
			logc.Infow(ctx, "finished fetch historical token tx, when startTime <= endTime",
				logc.Field("addr", addr.Address), logc.Field("endBlockHeight", endBlockHeight),
				logc.Field("startTime", startTime))
			break
		}

		if startHeight == endBlockHeight {
			logc.Infow(ctx, "finished fetch historical token tx, when startHeight == endBlockHeight",
				logc.Field("addr", addr.Address), logc.Field("endBlockHeight", endBlockHeight),
				logc.Field("startTime", startTime))
			break
		}

		endBlockHeight = startHeight
	}
	return nil
}

// fetchBackTokenTx 获取代币交易数据
func (j *JobManager) fetchBackTokenTx(ctx context.Context, addr *model.SyncAddressLabel,
	endBlockHeight int64) (startTime, startHeight int64, err error) {
	req := oklink.TokenTxReq{
		BaseTxReq: oklink.BaseTxReq{
			ChainShortName: addr.ChainType,
			Address:        addr.Address,
			Limit:          oklink.MaxLimit,
		},
		ProtocolType: oklink.DefaultProtocolType,
	}
	if endBlockHeight > 0 {
		req.EndBlockHeight = strconv.FormatInt(endBlockHeight, 10)
	}

	resp, err := j.svc.OklinkClient.GetTokenTx(ctx, req)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get token transaction: %w", err)
	}

	if resp.Code != "0" || len(resp.Data) == 0 || len(resp.Data[0].TransactionList) == 0 {
		return 0, 0, fmt.Errorf("failed to get token transaction: %s", resp.Msg)
	}

	// 按照height排序，从小到大排序
	sort.Slice(resp.Data[0].TransactionList, func(i, j int) bool {
		return resp.Data[0].TransactionList[i].Height < resp.Data[0].TransactionList[j].Height
	})

	// 开始插入db
	tokenTxs := make([]*model.TokenTransaction, 0)
	for _, tx := range resp.Data[0].TransactionList {
		tokenTxs = append(tokenTxs, &model.TokenTransaction{
			TransactionHash:      tx.TxId,
			BlockHash:            tx.BlockHash,
			Address:              addr.Address,
			ChainType:            addr.ChainType,
			Height:               tx.GetHeight(),
			TransactionTime:      tx.GetTransactionTime(),
			FromAddress:          tx.From,
			ToAddress:            tx.To,
			IsFromContract:       tx.GetIsFromContract(),
			IsToContract:         tx.GetIsToContract(),
			Amount:               tx.Amount,
			TokenSymbol:          tx.Symbol,
			TokenContractAddress: tx.TokenContractAddress,
			TokenId:              tx.TokenID,
			CreatedAt:            time.Now(),
			UpdatedAt:            time.Now(),
		})
	}

	if _, err = j.svc.TokenTransactionModel.InsertBatch(ctx, tokenTxs, &model.InsertBatchOption{
		IgnoreErrors:         true,
		OnDuplicateKeyUpdate: true,
	}); err != nil {
		logc.Errorw(ctx, "failed to insert token transactions", logc.Field("error", err))
		return 0, 0, fmt.Errorf("failed to insert token transactions: %w", err)
	}

	return resp.Data[0].TransactionList[0].GetTransactionTime(),
		resp.Data[0].TransactionList[0].GetHeight(), nil
}
