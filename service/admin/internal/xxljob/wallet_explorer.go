package xxljob

import (
	"context"
	"encoding/json"
	"fmt"

	"aml-insight/internal/model"
	"aml-insight/internal/pkg/crawl"
	"aml-insight/pkg/lark"

	"code.bydev.io/frameworks/byone/core/contextx"
	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/zcron"
)

const (
	// Address label constants
	defaultChain  = "BTC"
	defaultEditor = "alex.gao"
	defaultSource = "1" // 1 表示 bybit
	defaultRemark = "AI Expanding"
	defaultValid  = 1
)

// WalletExplorerParam xxljob parameter structure
type WalletExplorerParam struct {
	WalletAddr string `json:"wallet_addr"`
	crawl.BaseLabelInfo
}

func (p *WalletExplorerParam) GetChain() string {
	if p.Chain == "" {
		return defaultChain
	}
	return p.Chain
}

func (p *WalletExplorerParam) GetEditor() string {
	if p.Editor == "" {
		return defaultEditor
	}
	return p.Editor
}

func (p *WalletExplorerParam) GetSource() string {
	if p.Source == "" {
		return defaultSource
	}
	return p.Source
}

func (p *WalletExplorerParam) GetRemark() string {
	if p.Remark == "" {
		return defaultRemark
	}
	return p.Remark
}

func (p *WalletExplorerParam) GetValid() int32 {
	if p.Valid == 0 {
		return defaultValid
	}
	return p.Valid
}

func (p *WalletExplorerParam) GetDetailName() string {
	if p.DetailName == "" {
		return p.EntityName + "_user"
	}
	return p.DetailName
}

// Validate validates the wallet explorer parameters
func (p *WalletExplorerParam) Validate() error {
	if p.EntityName == "" {
		return fmt.Errorf("entityName is required")
	}
	if p.WalletAddr == "" {
		return fmt.Errorf("walletAddr is required")
	}
	if p.Category == "" {
		return fmt.Errorf("category is required")
	}
	return nil
}

// SyncWalletExplorerData sync wallet explorer data - xxljob entry point
func (j *JobManager) SyncWalletExplorerData(ctx context.Context, param *zcron.XxlJobParam) (result string, err error) {
	logc.Infow(ctx, "starting wallet explorer data synchronization", logc.Field("executor_params", param.ExecutorParams))

	recordCount := 0
	defer func() {
		if err != nil {
			j.larkAlert.SendLarkRichTextMsg(ctx, j.svc.Config.WalletExplorerCfg.WebhookURL, lark.LarkRichMsgBody{
				Title: "Wallet Explorer Data Synchronization Failed",
				Text:  fmt.Sprintf("Error: %v, params: %s", err, param.ExecutorParams),
				At:    j.svc.Config.WalletExplorerCfg.AtUsers,
			})
		} else {
			j.larkAlert.SendLarkRichTextMsg(ctx, j.svc.Config.WalletExplorerCfg.WebhookURL, lark.LarkRichMsgBody{
				Title: "Wallet Explorer Data Synchronization Success",
				Text:  fmt.Sprintf("Total addresses: %d, params: %s", recordCount, param.ExecutorParams),
				At:    j.svc.Config.WalletExplorerCfg.AtUsers,
			})
		}
	}()

	if j.walletExplorer == nil {
		return "failed", fmt.Errorf("wallet explorer is not initialized")
	}

	// Parse and validate parameters
	walletParam, err := j.parseAndValidateParams(ctx, param)
	if err != nil {
		return "failed", fmt.Errorf("parameter validation failed: %v", err)
	}

	labelParams := crawl.BaseLabelInfo{
		Chain:      walletParam.GetChain(),
		Category:   walletParam.Category,
		EntityName: walletParam.EntityName,
		DetailName: walletParam.GetDetailName(),
		Editor:     walletParam.GetEditor(),
		Source:     walletParam.GetSource(),
		Remark:     walletParam.GetRemark(),
		Valid:      walletParam.GetValid(),
	}
	// Use streaming crawler to get wallet data
	recordCount, err = j.walletExplorer.ProcessWalletDataWithCallback(ctx, walletParam.WalletAddr, labelParams,
		func(addresses []crawl.AddressLabelInfo) error {
			ctx_ := contextx.ValueOnlyFrom(ctx)
			return j.saveWalletAddressRecords(ctx_, addresses)
		}, 3)
	if err != nil {
		logc.Errorw(ctx, "failed to process wallet data",
			logc.Field("error", err),
			logc.Field("wallet_addr", walletParam.WalletAddr))
		return "failed", fmt.Errorf("failed to process wallet data: %v", err)
	}

	logc.Infow(ctx, "successfully retrieved wallet address data",
		logc.Field("records_count", recordCount),
		logc.Field("wallet_addr", walletParam.WalletAddr))
	return "success", nil
}

// parseAndValidateParams parse and validate xxljob parameters
func (j *JobManager) parseAndValidateParams(ctx context.Context, param *zcron.XxlJobParam) (*WalletExplorerParam, error) {
	if param == nil {
		return nil, fmt.Errorf("param is nil")
	}

	if param.ExecutorParams == "" {
		return nil, fmt.Errorf("executor params is empty")
	}

	var walletParam WalletExplorerParam
	if err := json.Unmarshal([]byte(param.ExecutorParams), &walletParam); err != nil {
		logc.Errorw(ctx, "failed to unmarshal executor params",
			logc.Field("params", param.ExecutorParams),
			logc.Field("error", err))
		return nil, fmt.Errorf("failed to unmarshal executor params: %w", err)
	}

	// Validate parameters
	if err := walletParam.Validate(); err != nil {
		logc.Errorw(ctx, "parameter validation failed",
			logc.Field("params", walletParam),
			logc.Field("error", err))
		return nil, fmt.Errorf("parameter validation failed: %w", err)
	}

	return &walletParam, nil
}

// saveWalletAddressRecords save wallet address records to database
func (j *JobManager) saveWalletAddressRecords(ctx context.Context, records []crawl.AddressLabelInfo) error {
	if len(records) == 0 {
		logc.Warnw(ctx, "no records to save")
		return nil
	}

	logc.Infow(ctx, "starting to save wallet address records",
		logc.Field("records_count", len(records)))

	var (
		lastErr           error
		tidbSuccessCount  int = 0
		mysqlSuccessCount int = 0
	)

	for _, record := range records {
		// Save to TiDB
		if _, err := j.upsert(ctx, &model.AllAddressLabel{
			Chain:      record.Chain,
			Address:    record.Address,
			Category:   record.Category,
			EntityName: record.EntityName,
			DetailName: record.DetailName,
			Editor:     record.Editor,
			Source:     record.Source,
			Remark:     record.Remark,
			Valid:      record.Valid,
		}); err != nil {
			logc.Errorw(ctx, "failed to upsert wallet address record to all_address_label(tidb)",
				logc.Field("error", err),
				logc.Field("record", record))
			lastErr = err
		} else {
			tidbSuccessCount++
		}

		if j.svc.Config.AddressLabelDBSwitch.DisableWriteMysql {
			continue
		}

		// TODO: 后续需要移除
		// Save to MySQL
		if err := j.svc.AddressLabelModel.InsertOrUpdate(ctx, &model.AddressLabel{
			Chain:      record.Chain,
			Address:    record.Address,
			Category:   record.Category,
			EntityName: record.EntityName,
			DetailName: record.DetailName,
			Editor:     record.Editor,
			Source:     record.Source,
			Remark:     record.Remark,
			Valid:      record.Valid,
		}); err != nil {
			logc.Errorw(ctx, "failed to insert or update wallet address record to address_label(mysql)",
				logc.Field("error", err),
				logc.Field("record", record))
			lastErr = err
		} else {
			mysqlSuccessCount++
		}
	}

	logc.Infow(ctx, "completed saving wallet address records",
		logc.Field("total_records", len(records)),
		logc.Field("tidb_success_count", tidbSuccessCount),
		logc.Field("mysql_success_count", mysqlSuccessCount))

	// Return error if any operation failed
	if lastErr != nil {
		return fmt.Errorf("some database operations failed, last error: %w", lastErr)
	}

	return nil
}

func (j *JobManager) upsert(ctx context.Context, data *model.AllAddressLabel) (int64, error) {
	if transformedEntityName, transformedDetailName, err := j.transformService.TransformEntityNameAndDetailName(ctx, data.EntityName, data.DetailName); err == nil {
		data.EntityName = transformedEntityName
		data.DetailName = transformedDetailName
	} else {
		logc.Warnw(ctx, "failed to transform entity name and detail name, using original", logc.Field("original", data.EntityName), logc.Field("error", err))
	}

	transformedSource, err := j.transformService.TransformSource(ctx, data.Source)
	if err == nil {
		data.Source = transformedSource
	} else {
		logc.Warnw(ctx, "failed to transform source, using original", logc.Field("original", data.Source), logc.Field("error", err))
	}
	data.Source = transformedSource
	return j.svc.AllAddressLabelModel.Upsert(ctx, data)
}
