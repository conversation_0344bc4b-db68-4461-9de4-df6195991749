package xxljob

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"code.bydev.io/frameworks/byone/zcron"

	"aml-insight/internal/model"
	"aml-insight/pkg/common"
	"aml-insight/pkg/lark"
	api "aml-insight/service/admin/internal/logic/amlinsightadminapi"

	"code.bydev.io/frameworks/byone/core/contextutils"
	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/core/logx"
)

type ByDataQueryStatus struct {
	Qid string `json:"qid"`
	SQL string `json:"sql"`
	DT  string `json:"dt"`
	HH  string `json:"hh"`
}

const (
	addressLabelQueryLimit = 10000
)

func (j *JobManager) AddressLabelSyncTaskCreate(ctx context.Context, param *zcron.XxlJobParam) (string, error) {
	ctx = contextutils.SetReferSiteId(ctx, contextutils.GetEnvSiteId())
	dataTime := time.Now().Add(-time.Hour)
	dt := dataTime.Format("2006-01-02")
	hh := dataTime.Add(-time.Hour).Format("15")
	//dt := "2025-01-15"
	//hh := "07"
	querySql := fmt.Sprintf("select chain,address,category,entity_name,detail_name,editor,source,remark,valid from %s where dt='%s' and hh='%s';", j.svc.Config.AddressLabelAutoSync.ByDataTableName, dt, hh)
	logc.Infow(ctx, fmt.Sprintf("开始创建查询任务，语句为：%s", querySql))
	qid, err := j.svc.ByDataService.CreateQueryHql(querySql)
	if err != nil {
		logx.Errorw("CreateQueryHql job error", logx.Field("err", err))
		return "failed", err
	}
	logc.Infow(ctx, fmt.Sprintf("成功创建查询任务，qid 为：%s", qid))
	queryStatus := &ByDataQueryStatus{
		Qid: qid,
		SQL: querySql,
		DT:  dt,
		HH:  hh,
	}
	queryStatusStr, err := json.Marshal(queryStatus)
	err = j.svc.AmlAsyncTaskStatusModel.InsertOrUpdate(ctx, &model.AmlAsyncTaskStatus{
		Key:          common.AmlByDataSyncJobKey,
		ExtendedInfo: string(queryStatusStr),
		Status:       model.AmlAsyncTaskStatusRunning,
		Valid:        1,
	})
	if err != nil {
		return "filed", err
	}
	return "success", nil
}

func (j *JobManager) AddressLabelSyncQuery(ctx context.Context, param *zcron.XxlJobParam) (string, error) {
	ctx = contextutils.SetReferSiteId(ctx, contextutils.GetEnvSiteId())
	// 查询redis中任务状态
	// 是空的话直接跳过
	asyncTaskStatus, err := j.svc.AmlAsyncTaskStatusModel.FindOneByKey(ctx, common.AmlByDataSyncJobKey)
	if err != nil {
		logx.Infow("没有找到对应同步任务id", logx.Field("err", err))
		return "", err
	}
	if asyncTaskStatus.Status != model.AmlAsyncTaskStatusRunning {
		logc.Warnw(ctx, "task not running or end, skip it", logc.Field("async_task_status", asyncTaskStatus))
		return "skip not running", nil
	}

	queryStatus := &ByDataQueryStatus{}
	if err = json.Unmarshal([]byte(asyncTaskStatus.ExtendedInfo), &queryStatus); err != nil {
		return "", err
	}
	if len(queryStatus.Qid) == 0 {
		logx.Infow("qid empty, skip it", logc.Field("async_task_status", asyncTaskStatus))
		return "skip no qid", nil
	}
	//return "", nil
	// 找到了，查询任务状态
	state, err := j.svc.ByDataService.GetQueryHqlState(queryStatus.Qid)
	if err != nil {
		logx.Errorw("GetQueryHqlState error", logx.Field("err", err), logx.Field("request_id", queryStatus.Qid))
		return "failed", err
	}
	if state != "succeeded" {
		logx.Infow(fmt.Sprintf("任务%s还未完成，当前状态%s，跳过", queryStatus.Qid, state))
		return fmt.Sprintf("skip by state:%s", state), nil
	}
	addressLabels := j.svc.ByDataService.GetLabelResultStream(queryStatus.Qid)
	if len(addressLabels) <= 0 {
		logx.Warnw(fmt.Sprintf("任务%s无数据条目需要同步，跳过", queryStatus.Qid))
		j.closeJob(ctx)
		return "skip no data update", nil
	}
	logx.Infow("准备批量新数据", logx.Field("", queryStatus.Qid))
	msg, err := j.storeAddressLabels(ctx, addressLabels, queryStatus)
	if err != nil {
		logx.Errorw("storeAddressLabels error", logx.Field("err", err))
		return msg, err
	}
	j.closeJob(ctx)
	return "success", nil
}

func (j *JobManager) closeJob(ctx context.Context) {
	// 更新redis任务
	if err := j.svc.AmlAsyncTaskStatusModel.InsertOrUpdate(ctx, &model.AmlAsyncTaskStatus{
		Key:          common.AmlByDataSyncJobKey,
		ExtendedInfo: "",
		Status:       model.AmlAsyncTaskStatusDone,
		Valid:        1,
	}); err != nil {
		logc.Errorw(ctx, "update async task status failed", logc.Field("err", err))
	}
}

func (j *JobManager) storeAddressLabels(ctx context.Context, addressLabel []*model.AddressLabel, queryStatus *ByDataQueryStatus) (string, error) {
	// 0. 过滤掉重复/非法的数据
	// 1. 过滤掉 db 中已有的数据
	// 2. 汇总信息来做 lark 告警
	// 3. 实际入库逻辑（过滤 valid = 0 和 DetailName 非 _user 结尾）
	// 4. 实际入库 lark 告警

	if len(addressLabel) > addressLabelQueryLimit {
		logc.Warnw(ctx, "address label too much", logc.Field("address_label_lenth", len(addressLabel)))
		return "failed address label too much", errors.New("failed address label too much")
	}

	// 过滤无效数据
	filteredInvalidAddressLabel := filterInvalidData(ctx, addressLabel)
	// 过滤 db 中已经存在的数据
	filteredAddressLabelByDb, existAddressLabel, err := j.filterByChainAndAddressFromDB(ctx, filteredInvalidAddressLabel)
	if err != nil {
		return "failed", err
	}
	if err := j.larkAlertReport(ctx, filteredAddressLabelByDb, "自建标签库地址预入库统计信息", j.svc.Config.AddressLabelAutoSync.ExtendedAddressLabelLarkAlertUrlAt, queryStatus); err != nil {
		logc.Errorw(ctx, "send lark alert report failed", logc.Field("err", err))
	}
	if err := j.larkAlertReport(ctx, existAddressLabel, "数据库中已存在的标签(diff by chain,address)", nil, queryStatus); err != nil {
		logc.Errorw(ctx, "send lark alert report failed", logc.Field("err", err))
	}

	addressLabelReadyImportToDB := filterAddressLabelForImport(filteredAddressLabelByDb)

	if len(addressLabelReadyImportToDB) == 0 {
		return "skip no new data", nil
	}

	success, err := api.NewRiskAmlGatewayAddressLabelAdminLogic(j.svc).BatchInsert(ctx, "auto:bydata", &api.BatchInsertReq{
		Force: 1,
		Rows:  addressLabelReadyImportToDB,
	})

	if err != nil {
		return "failed", err
	}

	if err := j.larkAlertReport(ctx, addressLabelReadyImportToDB, fmt.Sprintf("自建标签库地址实际入库统计信息(%d/%d)", success, len(addressLabelReadyImportToDB)), j.svc.Config.AddressLabelAutoSync.ExtendedAddressLabelLarkAlertUrlAt, queryStatus); err != nil {
		logc.Errorw(ctx, "send lark alert report failed", logc.Field("err", err))
	}
	return "success", nil
}

func filterInvalidData(ctx context.Context, addressLabel []*model.AddressLabel) []*model.AddressLabel {
	respAddressLabels := make([]*model.AddressLabel, 0)
	addressLabelSet := make(map[string]struct{})
	for _, label := range addressLabel {

		if len(label.Chain) == 0 || len(label.Address) == 0 || len(label.Category) == 0 || len(label.EntityName) == 0 || len(label.DetailName) == 0 || len(label.Editor) == 0 || len(label.Source) == 0 || len(label.Remark) == 0 {
			logc.Warnw(ctx, "invalid data", logc.Field("address_label", addressLabel))
			continue
		}
		if label.Valid != 0 && label.Valid != 1 {
			logc.Warnw(ctx, "invalid data", logc.Field("address_label", addressLabel))
			continue
		}
		uniqKey := fmt.Sprintf("%s_%s", label.Chain, label.Address)
		if _, ok := addressLabelSet[uniqKey]; ok {
			logc.Warnw(ctx, "duplicate data", logc.Field("address_label", addressLabel))
			continue
		}
		addressLabelSet[uniqKey] = struct{}{}
		respAddressLabels = append(respAddressLabels, label)
	}
	return respAddressLabels
}

func (j *JobManager) filterByChainAndAddressFromDB(ctx context.Context, addressLabel []*model.AddressLabel) ([]*model.AddressLabel, []*model.AddressLabel, error) {
	respAddressLabels := make([]*model.AddressLabel, 0)
	respExistAddressLabels := make([]*model.AddressLabel, 0)
	addressGroupByChainAddress := make(map[string][]string)

	// 用于过滤掉数据库中已存在的 case
	chainAndAddressMap := make(map[string]struct{})

	// group and unique by chain and address
	for _, label := range addressLabel {
		addressGroupByChainAddress[label.Chain] = append(addressGroupByChainAddress[label.Chain], label.Address)
	}

	for chain, addresses := range addressGroupByChainAddress {
		addressLabelList, err := j.svc.AddressLabelModel.QueryIdIn(ctx, nil, chain, addresses, 0, 0)
		if err != nil {
			return nil, nil, err
		}
		for _, label := range addressLabelList {
			chainAndAddressMap[fmt.Sprintf("%s_%s", label.Chain, label.Address)] = struct{}{}
		}
	}

	for _, label := range addressLabel {
		if _, ok := chainAndAddressMap[fmt.Sprintf("%s_%s", label.Chain, label.Address)]; ok {
			respExistAddressLabels = append(respExistAddressLabels, label)
		} else {
			respAddressLabels = append(respAddressLabels, label)
		}
	}
	return respAddressLabels, respExistAddressLabels, nil
}

func filterAddressLabelForImport(addressLabel []*model.AddressLabel) []*model.AddressLabel {
	respAddressLabel := make([]*model.AddressLabel, 0)
	for _, label := range addressLabel {
		if label.Valid != 1 {
			continue
		}
		if !strings.HasSuffix(label.DetailName, "_user") {
			continue
		}
		respAddressLabel = append(respAddressLabel, label)
	}
	return respAddressLabel
}

func (j *JobManager) larkAlertReport(ctx context.Context, addressLabels []*model.AddressLabel, title string, at []string, queryStatus *ByDataQueryStatus) error {

	// key: valid, entity_name
	groupByMap := make(map[int32]map[string][]*model.AddressLabel)
	for _, label := range addressLabels {
		if _, ok := groupByMap[label.Valid]; !ok {
			groupByMap[label.Valid] = make(map[string][]*model.AddressLabel)
		}
		if _, ok := groupByMap[label.Valid][label.EntityName]; !ok {
			groupByMap[label.Valid][label.EntityName] = make([]*model.AddressLabel, 0)
		}
		groupByMap[label.Valid][label.EntityName] = append(groupByMap[label.Valid][label.EntityName], label)
	}

	alertText := ""

	if addressLabelsGroupByEntityName, ok := groupByMap[1]; ok {
		alertText += "生效(valid = 1)\n\n"
		alertText += "group by entity_name:\n"

		statistics := make([]string, 0)
		for entityName, addressLabelsByEntityName := range addressLabelsGroupByEntityName {
			statistics = append(statistics, fmt.Sprintf("%s:	%d", entityName, len(addressLabelsByEntityName)))
		}
		sort.Slice(statistics, func(i, j int) bool {
			return statistics[i] < statistics[j]
		})
		alertText += strings.Join(statistics, "\n")
		alertText += "\n\n"
	}
	if addressLabelsGroupByEntityName, ok := groupByMap[0]; ok {
		alertText += "不生效(valid = 0)\n\n"
		alertText += "group by entity_name:\n"

		statistics := make([]string, 0)
		for entityName, addressLabelsByEntityName := range addressLabelsGroupByEntityName {
			statistics = append(statistics, fmt.Sprintf("%s:	%d", entityName, len(addressLabelsByEntityName)))
		}
		sort.Slice(statistics, func(i, j int) bool {
			return statistics[i] < statistics[j]
		})

		alertText += strings.Join(statistics, "\n")
		alertText += "\n\n"
	}

	if len(alertText) == 0 {
		logc.Warnw(ctx, "alert mst empty,skip it", logc.Field("address_labels_len", addressLabels))
		return nil
	}
	alertText += "alert time:	" + time.Now().String()
	alertText += "\nquery dt:	" + queryStatus.DT
	alertText += "\nquery hh:	" + queryStatus.HH
	alertText += "\n\n"
	larkAlertMsg := lark.LarkRichMsgBody{
		Title: title,
		Text:  alertText,
		At:    at,
	}

	return j.larkAlert.SendLarkRichTextMsg(ctx, j.svc.Config.AddressLabelAutoSync.ExtendedAddressLabelLarkAlertUrl, larkAlertMsg)
}
