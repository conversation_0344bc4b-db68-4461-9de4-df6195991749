// Code generated by byctl. DO NOT EDIT!
// Source: https://code.bydev.io/cht/fiat/backend/bufmodule/-/blob/feat/aml_additional_scan_edd/fiat/channel/aml_insight/v1/aml_insight_admin.proto

package server

import (
	"context"

	"aml-insight/service/admin/internal/handler"
	amlinsightadminapilogic "aml-insight/service/admin/internal/logic/amlinsightadminapi"
	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIServer struct {
	svcCtx *svc.ServiceContext
	aml_insightv1.UnimplementedAmlInsightAdminAPIServer

	// Handler instances (stateless, can be reused)
	entityNameMappingHandler *handler.EntityNameMappingHandler
}

func NewAmlInsightAdminAPIServer(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIServer {
	return &AmlInsightAdminAPIServer{
		svcCtx:                   svcCtx,
		entityNameMappingHandler: handler.NewEntityNameMappingHandler(svcCtx.EntityNameMappingModel),
	}
}

func (s *AmlInsightAdminAPIServer) QueryAddressLabel(ctx context.Context, in *aml_insightv1.QueryAddressLabelRequest) (*aml_insightv1.QueryAddressLabelResponse, error) {
	l := amlinsightadminapilogic.NewAmlInsightAdminAPIQueryAddressLabelLogic(s.svcCtx)
	return l.QueryAddressLabel(ctx, in)
}

func (s *AmlInsightAdminAPIServer) UpdateAddressLabel(ctx context.Context, in *aml_insightv1.UpdateAddressLabelRequest) (*aml_insightv1.UpdateAddressLabelResponse, error) {
	l := amlinsightadminapilogic.NewAmlInsightAdminAPIUpdateAddressLabelLogic(s.svcCtx)
	return l.UpdateAddressLabel(ctx, in)
}

func (s *AmlInsightAdminAPIServer) ImportAddressLabel(ctx context.Context, in *aml_insightv1.ImportAddressLabelRequest) (*aml_insightv1.ImportAddressLabelResponse, error) {
	l := amlinsightadminapilogic.NewAmlInsightAdminAPIImportAddressLabelLogic(s.svcCtx)
	return l.ImportAddressLabel(ctx, in)
}

func (s *AmlInsightAdminAPIServer) QueryAmlLabelTrigger(ctx context.Context, in *aml_insightv1.QueryAmlLabelTriggerRequest) (*aml_insightv1.QueryAmlLabelTriggerResponse, error) {
	l := amlinsightadminapilogic.NewAmlInsightAdminAPIQueryAmlLabelTriggerLogic(s.svcCtx)
	return l.QueryAmlLabelTrigger(ctx, in)
}

func (s *AmlInsightAdminAPIServer) UpsertAmlLabelTrigger(ctx context.Context, in *aml_insightv1.UpsertAmlLabelTriggerRequest) (*aml_insightv1.UpsertAmlLabelTriggerResponse, error) {
	l := amlinsightadminapilogic.NewAmlInsightAdminAPIUpsertAmlLabelTriggerLogic(s.svcCtx)
	return l.UpsertAmlLabelTrigger(ctx, in)
}

func (s *AmlInsightAdminAPIServer) QueryAmlHitCategory(ctx context.Context, in *aml_insightv1.QueryAmlHitCategoryRequest) (*aml_insightv1.QueryAmlHitCategoryResponse, error) {
	l := amlinsightadminapilogic.NewAmlInsightAdminAPIQueryAmlHitCategoryLogic(s.svcCtx)
	return l.QueryAmlHitCategory(ctx, in)
}

func (s *AmlInsightAdminAPIServer) UpsertAmlHitCategory(ctx context.Context, in *aml_insightv1.UpsertAmlHitCategoryRequest) (*aml_insightv1.UpsertAmlHitCategoryResponse, error) {
	l := amlinsightadminapilogic.NewAmlInsightAdminAPIUpsertAmlHitCategoryLogic(s.svcCtx)
	return l.UpsertAmlHitCategory(ctx, in)
}

func (s *AmlInsightAdminAPIServer) QueryVendorCategoryMapping(ctx context.Context, in *aml_insightv1.QueryVendorCategoryMappingRequest) (*aml_insightv1.QueryVendorCategoryMappingResponse, error) {
	l := amlinsightadminapilogic.NewAmlInsightAdminAPIQueryVendorCategoryMappingLogic(s.svcCtx)
	return l.QueryVendorCategoryMapping(ctx, in)
}

func (s *AmlInsightAdminAPIServer) UpsertVendorCategoryMapping(ctx context.Context, in *aml_insightv1.UpsertVendorCategoryMappingRequest) (*aml_insightv1.UpsertVendorCategoryMappingResponse, error) {
	l := amlinsightadminapilogic.NewAmlInsightAdminAPIUpsertVendorCategoryMappingLogic(s.svcCtx)
	return l.UpsertVendorCategoryMapping(ctx, in)
}

func (s *AmlInsightAdminAPIServer) QueryEntityNameMapping(ctx context.Context, in *aml_insightv1.QueryEntityNameMappingRequest) (*aml_insightv1.QueryEntityNameMappingResponse, error) {
	return s.entityNameMappingHandler.QueryEntityNameMapping(ctx, in)
}

func (s *AmlInsightAdminAPIServer) UpsertEntityNameMapping(ctx context.Context, in *aml_insightv1.UpsertEntityNameMappingRequest) (*aml_insightv1.UpsertEntityNameMappingResponse, error) {
	return s.entityNameMappingHandler.UpsertEntityNameMapping(ctx, in)
}

func (s *AmlInsightAdminAPIServer) ListEntityMappings(ctx context.Context, in *aml_insightv1.ListEntityMappingsRequest) (*aml_insightv1.ListEntityMappingsResponse, error) {
	return s.entityNameMappingHandler.ListEntityMappings(ctx, in)
}

func (s *AmlInsightAdminAPIServer) UpdateEntityMapping(ctx context.Context, in *aml_insightv1.UpdateEntityMappingRequest) (*aml_insightv1.UpdateEntityMappingResponse, error) {
	return s.entityNameMappingHandler.UpdateEntityMapping(ctx, in)
}

func (s *AmlInsightAdminAPIServer) BatchUpdateEntityMapping(ctx context.Context, in *aml_insightv1.BatchUpdateEntityMappingRequest) (*aml_insightv1.BatchUpdateEntityMappingResponse, error) {
	return s.entityNameMappingHandler.BatchUpdateEntityMapping(ctx, in)
}

func (s *AmlInsightAdminAPIServer) InsertEntityMapping(ctx context.Context, in *aml_insightv1.InsertEntityMappingRequest) (*aml_insightv1.InsertEntityMappingResponse, error) {
	return s.entityNameMappingHandler.InsertEntityMapping(ctx, in)
}

func (s *AmlInsightAdminAPIServer) BatchInsertEntityMapping(ctx context.Context, in *aml_insightv1.BatchInsertEntityMappingRequest) (*aml_insightv1.BatchInsertEntityMappingResponse, error) {
	return s.entityNameMappingHandler.BatchInsertEntityMapping(ctx, in)
}

func (s *AmlInsightAdminAPIServer) ImportRequestIdsAdditionalKyaScan(ctx context.Context, in *aml_insightv1.ImportRequestIdsAdditionalKyaScanRequest) (*aml_insightv1.ImportRequestIdsAdditionalKyaScanResponse, error) {
	l := amlinsightadminapilogic.NewAmlInsightAdminAPIImportRequestIdsAdditionalKyaScanLogic(s.svcCtx)
	return l.ImportRequestIdsAdditionalKyaScan(ctx, in)
}

func (s *AmlInsightAdminAPIServer) GetPreKyaScanHistory(ctx context.Context, in *aml_insightv1.GetPreKyaScanHistoryRequest) (*aml_insightv1.GetPreKyaScanHistoryResponse, error) {
	l := amlinsightadminapilogic.NewAmlInsightAdminAPIGetPreKyaScanHistoryLogic(s.svcCtx)
	return l.GetPreKyaScanHistory(ctx, in)
}

func (s *AmlInsightAdminAPIServer) ImportRequestForFiatScan(ctx context.Context, in *aml_insightv1.ImportRequestForFiatScanRequest) (*aml_insightv1.ImportRequestForFiatScanResponse, error) {
	l := amlinsightadminapilogic.NewAmlInsightAdminAPIImportRequestForFiatScanLogic(s.svcCtx)
	return l.ImportRequestForFiatScan(ctx, in)
}
