// Code generated by byctl. DO NOT EDIT!
// Source: https://code.bydev.io/cht/fiat/backend/bufmodule/-/blob/feature/gen_str_v2/fiat/channel/aml_insight/v1/aml_insight_str_admin.proto

package server

import (
	"context"

	"aml-insight/service/admin/internal/logic/amlinsightstradminapi"
	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightStrAdminAPIServer struct {
	svcCtx *svc.ServiceContext
	aml_insightv1.UnimplementedAmlInsightStrAdminAPIServer
}

func NewAmlInsightStrAdminAPIServer(svcCtx *svc.ServiceContext) *AmlInsightStrAdminAPIServer {
	return &AmlInsightStrAdminAPIServer{
		svcCtx: svcCtx,
	}
}

// GetUserParticular 获取用户的基本信息
func (s *AmlInsightStrAdminAPIServer) GetUserParticular(ctx context.Context, in *aml_insightv1.GetUserParticularRequest) (*aml_insightv1.GetUserParticularResponse, error) {
	l := amlinsightstradminapilogic.NewAmlInsightStrAdminAPIGetUserParticularLogic(s.svcCtx)
	return l.GetUserParticular(ctx, in)
}

// GetUserOnchainTransactions 获取用户的交易列表
func (s *AmlInsightStrAdminAPIServer) GetUserOnchainTransactions(ctx context.Context, in *aml_insightv1.GetUserOnchainTransactionsRequest) (*aml_insightv1.GetUserOnchainTransactionsResponse, error) {
	l := amlinsightstradminapilogic.NewAmlInsightStrAdminAPIGetUserOnchainTransactionsLogic(s.svcCtx)
	return l.GetUserOnchainTransactions(ctx, in)
}

// GetWriteupOfSuspiciousActivity 获取用户的可疑活动报告，返回的是base64编码的html文档
func (s *AmlInsightStrAdminAPIServer) GetWriteupOfSuspiciousActivity(ctx context.Context, in *aml_insightv1.GetWriteupOfSuspiciousActivityRequest) (*aml_insightv1.GetWriteupOfSuspiciousActivityResponse, error) {
	l := amlinsightstradminapilogic.NewAmlInsightStrAdminAPIGetWriteupOfSuspiciousActivityLogic(s.svcCtx)
	return l.GetWriteupOfSuspiciousActivity(ctx, in)
}

// DownloadOnchainTransactions 下载用户的交易列表，返回的是base64编码的xml文件
func (s *AmlInsightStrAdminAPIServer) DownloadOnchainTransactions(ctx context.Context, in *aml_insightv1.DownloadOnchainTransactionsRequest) (*aml_insightv1.DownloadOnchainTransactionsResponse, error) {
	l := amlinsightstradminapilogic.NewAmlInsightStrAdminAPIDownloadOnchainTransactionsLogic(s.svcCtx)
	return l.DownloadOnchainTransactions(ctx, in)
}

// GetUserKycDocs 获取用户的KYC资料，返回的是resource_id列表
func (s *AmlInsightStrAdminAPIServer) GetUserKycDocs(ctx context.Context, in *aml_insightv1.GetUserKycDocsRequest) (*aml_insightv1.GetUserKycDocsResponse, error) {
	l := amlinsightstradminapilogic.NewAmlInsightStrAdminAPIGetUserKycDocsLogic(s.svcCtx)
	return l.GetUserKycDocs(ctx, in)
}

// GetDocContent 获取图片内容，返回的是base64编码的图片内容
func (s *AmlInsightStrAdminAPIServer) GetDocContent(ctx context.Context, in *aml_insightv1.GetDocContentRequest) (*aml_insightv1.GetDocContentResponse, error) {
	l := amlinsightstradminapilogic.NewAmlInsightStrAdminAPIGetDocContentLogic(s.svcCtx)
	return l.GetDocContent(ctx, in)
}

// DownloadPoaZip 下载用户的POA（地址证明），打包成zip文件，返回的是base64编码的zip文件
func (s *AmlInsightStrAdminAPIServer) DownloadPoaZip(ctx context.Context, in *aml_insightv1.DownloadPoaZipRequest) (*aml_insightv1.DownloadPoaZipResponse, error) {
	l := amlinsightstradminapilogic.NewAmlInsightStrAdminAPIDownloadPoaZipLogic(s.svcCtx)
	return l.DownloadPoaZip(ctx, in)
}

// DownloadPoiZip 下载用户的POI（身份证明），打包成zip文件，返回的是base64编码的zip文件
func (s *AmlInsightStrAdminAPIServer) DownloadPoiZip(ctx context.Context, in *aml_insightv1.DownloadPoiZipRequest) (*aml_insightv1.DownloadPoiZipResponse, error) {
	l := amlinsightstradminapilogic.NewAmlInsightStrAdminAPIDownloadPoiZipLogic(s.svcCtx)
	return l.DownloadPoiZip(ctx, in)
}

// DownloadSelfieZip 下载用户的Selfie（自拍照），打包成zip文件，返回的是base64编码的zip文件
func (s *AmlInsightStrAdminAPIServer) DownloadSelfieZip(ctx context.Context, in *aml_insightv1.DownloadSelfieZipRequest) (*aml_insightv1.DownloadSelfieZipResponse, error) {
	l := amlinsightstradminapilogic.NewAmlInsightStrAdminAPIDownloadSelfieZipLogic(s.svcCtx)
	return l.DownloadSelfieZip(ctx, in)
}

// DownloadSupportingDocsZip 下载Case的补充材料，打包成zip文件，返回的是base64编码的zip文件
func (s *AmlInsightStrAdminAPIServer) DownloadSupportingDocsZip(ctx context.Context, in *aml_insightv1.DownloadSupportingDocsZipRequest) (*aml_insightv1.DownloadSupportingDocsZipResponse, error) {
	l := amlinsightstradminapilogic.NewAmlInsightStrAdminAPIDownloadSupportingDocsZipLogic(s.svcCtx)
	return l.DownloadSupportingDocsZip(ctx, in)
}

// DownloadStrWordDoc 下载str自动化生成的word文档，返回的是base64编码的word文档
func (s *AmlInsightStrAdminAPIServer) DownloadStrWordDoc(ctx context.Context, in *aml_insightv1.DownloadStrWordDocRequest) (*aml_insightv1.DownloadStrWordDocResponse, error) {
	l := amlinsightstradminapilogic.NewAmlInsightStrAdminAPIDownloadStrWordDocLogic(s.svcCtx)
	return l.DownloadStrWordDoc(ctx, in)
}
