// Code generated by byctl. DO NOT EDIT!
// Source: https://code.bydev.io/cht/fiat/backend/bufmodule/-/blob/master/fiat/channel/aml_insight/v1/aml_insight_tmr_admin.proto

package server

import (
	"context"

	"aml-insight/service/admin/internal/logic/amlinsighttmradminapi"
	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightTmrAdminAPIServer struct {
	svcCtx *svc.ServiceContext
	aml_insightv1.UnimplementedAmlInsightTmrAdminAPIServer
}

func NewAmlInsightTmrAdminAPIServer(svcCtx *svc.ServiceContext) *AmlInsightTmrAdminAPIServer {
	return &AmlInsightTmrAdminAPIServer{
		svcCtx: svcCtx,
	}
}

// GetSfCaseList Get all salesforce case list, just triggered case
func (s *AmlInsightTmrAdminAPIServer) GetOnchainSfCaseList(ctx context.Context, in *aml_insightv1.GetOnchainSfCaseListRequest) (*aml_insightv1.GetOnchainSfCaseListResponse, error) {
	l := amlinsighttmradminapilogic.NewAmlInsightTmrAdminAPIGetOnchainSfCaseListLogic(s.svcCtx)
	return l.GetOnchainSfCaseList(ctx, in)
}

// GetFiatTransactionList Get all fiat transaction
func (s *AmlInsightTmrAdminAPIServer) GetFiatTransactionList(ctx context.Context, in *aml_insightv1.GetFiatTransactionListRequest) (*aml_insightv1.GetFiatTransactionListResponse, error) {
	l := amlinsighttmradminapilogic.NewAmlInsightTmrAdminAPIGetFiatTransactionListLogic(s.svcCtx)
	return l.GetFiatTransactionList(ctx, in)
}

// GetUserInfoDetail Get user deatil info and transaction list
func (s *AmlInsightTmrAdminAPIServer) GetUserInfoDetail(ctx context.Context, in *aml_insightv1.GetUserInfoDetailRequest) (*aml_insightv1.GetUserInfoDetailResponse, error) {
	l := amlinsighttmradminapilogic.NewAmlInsightTmrAdminAPIGetUserInfoDetailLogic(s.svcCtx)
	return l.GetUserInfoDetail(ctx, in)
}

// GetUserTransactionList Get user transaction list
func (s *AmlInsightTmrAdminAPIServer) GetUserTransactionList(ctx context.Context, in *aml_insightv1.GetUserTransactionListRequest) (*aml_insightv1.GetUserTransactionListResponse, error) {
	l := amlinsighttmradminapilogic.NewAmlInsightTmrAdminAPIGetUserTransactionListLogic(s.svcCtx)
	return l.GetUserTransactionList(ctx, in)
}

func (s *AmlInsightTmrAdminAPIServer) DownloadTransaction(ctx context.Context, in *aml_insightv1.DownloadTransactionRequest) (*aml_insightv1.DownloadTransactionResponse, error) {
	l := amlinsighttmradminapilogic.NewAmlInsightTmrAdminAPIDownloadTransactionLogic(s.svcCtx)
	return l.DownloadTransaction(ctx, in)
}
