package amlinsightstradminapilogic

import (
	"context"
	"encoding/base64"
	"fmt"
	"time"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"code.bydev.io/frameworks/byone/core/logc"
	platformkyc "git.bybit.com/svc/stub/pkg/pb/api/kyc"
)

type AmlInsightStrAdminAPIDownloadPoiZipLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightStrAdminAPIDownloadPoiZipLogic(svcCtx *svc.ServiceContext) *AmlInsightStrAdminAPIDownloadPoiZipLogic {
	return &AmlInsightStrAdminAPIDownloadPoiZipLogic{
		svcCtx: svcCtx,
	}
}

// DownloadPoiZip 下载用户的POI（身份证明），打包成zip文件，返回的是base64编码的zip文件
func (l *AmlInsightStrAdminAPIDownloadPoiZipLogic) DownloadPoiZip(ctx context.Context, in *aml_insightv1.DownloadPoiZipRequest) (*aml_insightv1.DownloadPoiZipResponse, error) {
	resp := &aml_insightv1.DownloadPoiZipResponse{}
	if !CheckIfUidPermitted(ctx, l.svcCtx, in.MemberId) {
		resp.Code = 403
		resp.Msg = fmt.Sprintf("uid %d not permitted", in.MemberId)
		return resp, nil
	}
	return l.downloadPoiZip(ctx, in.MemberId)
}

func (l *AmlInsightStrAdminAPIDownloadPoiZipLogic) downloadPoiZip(ctx context.Context, memberId int64) (*aml_insightv1.DownloadPoiZipResponse, error) {
	var resp = &aml_insightv1.DownloadPoiZipResponse{
		Result: &aml_insightv1.DownloadPoiZipResponse_Result{
			Status: "success",
		},
	}
	memberKycResp, err := l.svcCtx.KycInternalClient.GetMemberKYC(ctx, &platformkyc.GetMemberKYCRequest{
		MemberId:                  memberId,
		ObtainOnboardingAuthInfos: true,
		ObtainSupplementAuthInfos: true,
	})
	if err != nil {
		logc.Error(ctx, "get kyc info failed", logc.Field("err", err))
		resp.Code = 500
		resp.Msg = err.Error()
		resp.Result = nil
		return resp, nil
	}
	authInfos := []*platformkyc.GetMemberKYCResponse_KYCAuthInfo{}
	authInfos = append(authInfos, memberKycResp.GetOnboardingAuthInfos()...)
	authInfos = append(authInfos, memberKycResp.GetSupplementAuthInfos()...)
	pois := make([]*aml_insightv1.GetUserKycDocsResponse_Result_Item, 0)
	for _, info := range authInfos {
		if info.Level != 1 { // 只处理kyc1的身份证明
			continue
		}
		img1, err1 := GetShareImage(ctx, l.svcCtx.KycInternalClient, memberId, info.IdempotentId, 1) // 证件正面
		img2, err2 := GetShareImage(ctx, l.svcCtx.KycInternalClient, memberId, info.IdempotentId, 2) // 证件反面
		if err1 == ErrNeedWait || err2 == ErrNeedWait {
			resp.Result.Status = "waiting"
			return resp, nil
		}
		if err1 != nil || err2 != nil {
			logc.Warn(ctx, "get sharing file url failed", logc.Field("err1", err1), logc.Field("err2", err2))
		}
		if img1 != nil {
			pois = append(pois, img1)
		}
		if img2 != nil {
			pois = append(pois, img2)
		}
	}
	if len(pois) == 0 {
		resp.Code = 500
		resp.Msg = "get sharing file url failed, no POI images found"
		return resp, nil
	}
	// zip the images
	zipData, err := zipImages(ctx, pois, l.svcCtx.PhotoSharingKey)
	if err != nil {
		logc.Error(ctx, "zip images failed", logc.Field("err", err))
		resp.Code = 500
		resp.Msg = "zip images failed: " + err.Error()
	}
	resp.Result.Status = "success"
	resp.Result.Body = base64.StdEncoding.EncodeToString(zipData) // base64 encoded zip file
	resp.Result.Filename = fmt.Sprintf("poi_%d_%s.zip", memberId, time.Now().Format("2006_01_02_15_04_05"))
	return resp, nil
}
