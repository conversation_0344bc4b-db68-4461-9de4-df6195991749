package amlinsightstradminapilogic

import (
	"bytes"
	"context"
	_ "embed" // for embedding files
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"code.bydev.io/frameworks/byone/core/logc"
	"github.com/nguyenthenguyen/docx"
)

//go:embed ISAR_template.docx
var templateBytes []byte

type AmlInsightStrAdminAPIDownloadStrWordDocLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightStrAdminAPIDownloadStrWordDocLogic(svcCtx *svc.ServiceContext) *AmlInsightStrAdminAPIDownloadStrWordDocLogic {
	return &AmlInsightStrAdminAPIDownloadStrWordDocLogic{
		svcCtx: svcCtx,
	}
}

// DownloadStrWordDoc 下载str自动化生成的word文档，返回的是base64编码的word文档
func (l *AmlInsightStrAdminAPIDownloadStrWordDocLogic) DownloadStrWordDoc(ctx context.Context, in *aml_insightv1.DownloadStrWordDocRequest) (*aml_insightv1.DownloadStrWordDocResponse, error) {
	if !CheckIfUidPermitted(ctx, l.svcCtx, in.MemberId) {
		return &aml_insightv1.DownloadStrWordDocResponse{
			Code: 403,
			Msg:  fmt.Sprintf("uid %d not permitted", in.MemberId),
		}, nil
	}
	if resp, err := l.generateStrDoc(ctx, in.MemberId); err != nil {
		logc.Error(ctx, "generate str doc failed", logc.Field("err", err))
		return &aml_insightv1.DownloadStrWordDocResponse{
			Code: 500,
			Msg:  err.Error(),
		}, nil
	} else {
		return resp, nil
	}
}

func (l *AmlInsightStrAdminAPIDownloadStrWordDocLogic) generateStrDoc(ctx context.Context, memberId int64) (*aml_insightv1.DownloadStrWordDocResponse, error) {
	var resp = &aml_insightv1.DownloadStrWordDocResponse{
		Result: &aml_insightv1.DownloadStrWordDocResponse_Result{},
	}

	info, err := getUserParticularInfo(ctx, l.svcCtx, memberId)
	if err != nil {
		return nil, fmt.Errorf("failed to get user particular info: %w", err)
	}

	activityDetail, err := generateSuspiciousActivityInfo(ctx, memberId, l.svcCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate suspicious activity info: %w", err)
	}

	buf := bytes.NewReader(templateBytes)
	r, err := docx.ReadDocxFromMemory(buf, int64(buf.Len()))
	if err != nil {
		return nil, fmt.Errorf("failed to read docx template: %w", err)
	}
	defer r.Close()
	docx1 := r.Editable()

	docx1.Replace("{{.FullName}}", info.NativeCharacterName, -1)
	docx1.Replace("{{.NativeCharacterName}}", info.NativeCharacterName, -1)
	docx1.Replace("{{.KYCLevel}}", info.KYCLevel, -1)
	docx1.Replace("{{.VIPLevel}}", info.VIPLevel, -1)
	docx1.Replace("{{.TypeOfIDDocument}}", mappingDocType(info.TypeOfIDDocument), -1)
	docx1.Replace("{{.IDNumber}}", info.IDNumber, -1)
	docx1.Replace("{{.CountryWhoIssuedDocument}}", info.CountryWhoIssuedDocument, -1)
	docx1.Replace("{{.IssueDateOfDocument}}", info.IssueDateOfDocument, -1)
	docx1.Replace("{{.ExpiryDateOfDocument}}", info.ExpiryDateOfDocument, -1)
	docx1.Replace("{{.DateOfBirth}}", info.DateOfBirth, -1)
	docx1.Replace("{{.Nationality}}", info.Nationality, -1)
	docx1.Replace("{{.KYCProviderInfo}}", strings.Join(info.KYCProviderInfo, "\n"), -1)
	docx1.Replace("{{.CommonKYCUIDs}}", strings.Join(info.CommonKYCUIDs, "\n"), -1)
	docx1.Replace("{{.UID}}", info.UID, -1)
	docx1.Replace("{{.SubaccountUID}}", strings.Join(info.SubaccountUID, "\n"), -1)
	docx1.Replace("{{.SalesforceCaseID}}", strings.Join(info.SalesforceCaseID, "\n"), -1)
	docx1.Replace("{{.ResidentialAddress}}", info.ResidentialAddress, -1)
	docx1.Replace("{{.MobileNumber}}", info.MobileNumber, -1)
	docx1.Replace("{{.Email}}", info.Email, -1)
	docx1.Replace("{{.IPAddress}}", info.IPAddress, -1)
	docx1.Replace("{{.WalletAddresses}}", strings.Join(info.WalletAddresses, "\n"), -1)
	docx1.Replace("{{.NameOfBank}}", info.NameOfBank, -1)
	docx1.Replace("{{.BankAccountNumber}}", info.BankAccountNumber, -1)

	docx1.Replace("{{.NumericalAge}}", activityDetail.NumericalAge, -1)
	// 注意这里是Nationality2，避免被上面Nationality覆盖
	docx1.Replace("{{.Nationality2}}", activityDetail.Nationality, -1)
	docx1.Replace("{{.WalletAddressNumber}}", activityDetail.WalletAddressNumber, -1)
	docx1.Replace("{{.RiskTag}}", activityDetail.RiskTag, -1)
	docx1.Replace("{{.RiskTagEntityName}}", activityDetail.RiskTagEntityName, -1)
	docx1.Replace("{{.BlockchainAnalyticsVendor}}", activityDetail.BlockchainAnalyticsVendor, -1)
	docx1.Replace("{{.Gender}}", activityDetail.Gender, -1)

	outBuf := bytes.NewBuffer(nil)
	docx1.Write(outBuf)

	resp.Result.Status = "success"
	resp.Result.Body = base64.StdEncoding.EncodeToString(outBuf.Bytes())
	resp.Result.Filename = fmt.Sprintf("ISAR-UID-%d-%d.docx", memberId, time.Now().UnixNano())
	return resp, nil
}
