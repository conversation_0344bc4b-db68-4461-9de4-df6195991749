package amlinsightstradminapilogic

import (
	"context"
	"fmt"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"code.bydev.io/frameworks/byone/core/logc"
	platformkyc "git.bybit.com/svc/stub/pkg/pb/api/kyc"
)

var ErrNeedWait = fmt.Errorf("need wait, kyc image is processing")

type AmlInsightStrAdminAPIGetUserKycDocsLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightStrAdminAPIGetUserKycDocsLogic(svcCtx *svc.ServiceContext) *AmlInsightStrAdminAPIGetUserKycDocsLogic {
	return &AmlInsightStrAdminAPIGetUserKycDocsLogic{
		svcCtx: svcCtx,
	}
}

// GetUserKycDocs Get user KYC documents
func (l *AmlInsightStrAdminAPIGetUserKycDocsLogic) GetUserKycDocs(ctx context.Context, in *aml_insightv1.GetUserKycDocsRequest) (*aml_insightv1.GetUserKycDocsResponse, error) {
	resp := &aml_insightv1.GetUserKycDocsResponse{}
	if !CheckIfUidPermitted(ctx, l.svcCtx, in.MemberId) {
		resp.Code = 403
		resp.Msg = fmt.Sprintf("uid %d not permitted", in.MemberId)
		return resp, nil
	}
	return l.GetKycImages(ctx, in.MemberId)
}

func (l *AmlInsightStrAdminAPIGetUserKycDocsLogic) GetKycImages(ctx context.Context, memberId int64) (*aml_insightv1.GetUserKycDocsResponse, error) {
	var resp = &aml_insightv1.GetUserKycDocsResponse{
		Result: &aml_insightv1.GetUserKycDocsResponse_Result{
			Status: "success",
		},
	}
	memberKycResp, err := l.svcCtx.KycInternalClient.GetMemberKYC(ctx, &platformkyc.GetMemberKYCRequest{
		MemberId:                  memberId,
		ObtainOnboardingAuthInfos: true,
		ObtainSupplementAuthInfos: true,
	})

	if err != nil {
		logc.Error(ctx, "get kyc info failed", logc.Field("err", err))
		resp.Code = 500
		resp.Msg = err.Error()
		resp.Result = nil
		return resp, nil
	}
	authInfos := []*platformkyc.GetMemberKYCResponse_KYCAuthInfo{}
	authInfos = append(authInfos, memberKycResp.GetOnboardingAuthInfos()...)
	authInfos = append(authInfos, memberKycResp.GetSupplementAuthInfos()...)
	for _, info := range authInfos {
		var img1, img2, img3, img4 *aml_insightv1.GetUserKycDocsResponse_Result_Item
		var err1, err2, err3, err4 error
		if info.Level == 1 { // kyc1
			img1, err1 = GetShareImage(ctx, l.svcCtx.KycInternalClient, memberId, info.IdempotentId, 1) // 证件正面
			img2, err2 = GetShareImage(ctx, l.svcCtx.KycInternalClient, memberId, info.IdempotentId, 2) // 证件反面
			img3, err3 = GetShareImage(ctx, l.svcCtx.KycInternalClient, memberId, info.IdempotentId, 3) // 自拍照
		}
		if info.Level == 2 { // kyc2
			img4, err4 = GetShareImage(ctx, l.svcCtx.KycInternalClient, memberId, info.IdempotentId, 1) // 地址证明
		}
		if err1 == ErrNeedWait || err2 == ErrNeedWait || err3 == ErrNeedWait || err4 == ErrNeedWait {
			resp.Result.Poa = nil
			resp.Result.Poi = nil
			resp.Result.Selfie = nil
			resp.Result.Status = "waiting"
			return resp, nil
		}
		// img2可能会报错，因为有些用户没有上传证件反面
		if err1 != nil || err2 != nil || err3 != nil || err4 != nil {
			logc.Warn(ctx, "get sharing file url failed", logc.Field("err", err1), logc.Field("err2", err2), logc.Field("err3", err3), logc.Field("err4", err4))
		}
		if img1 != nil {
			resp.Result.Poi = append(resp.Result.Poi, img1)
		}
		if img2 != nil {
			resp.Result.Poi = append(resp.Result.Poi, img2)
		}
		if img3 != nil {
			resp.Result.Selfie = append(resp.Result.Selfie, img3)
		}
		if img4 != nil {
			resp.Result.Poa = append(resp.Result.Poa, img4)
		}
	}

	resp.Result.SupportingDocs, err = l.GetSupportingDocs(ctx, memberId)
	if err != nil {
		logc.Error(ctx, "get supporting docs failed", logc.Field("err", err))
		resp.Code = 500
		resp.Msg = err.Error()
		resp.Result.SupportingDocs = nil
		return resp, nil
	}

	// 模拟处理中的状态，用于测试
	// if time.Now().Second()%3 == 0 {
	// 	resp.Result.Poa = nil
	// 	resp.Result.Poi = nil
	// 	resp.Result.Selfie = nil
	// 	resp.Result.Status = "waiting" // 模拟处理中的状态
	// }

	return resp, nil
}

func GetShareImage(ctx context.Context, kycInternalClient platformkyc.KycInternalClient, memberId int64, idempotentId string, bizType int32) (*aml_insightv1.GetUserKycDocsResponse_Result_Item, error) {
	resp, err := kycInternalClient.GetSharingFileURL(ctx, &platformkyc.GetSharingFileURLRequest{
		FileBizType:  platformkyc.FileBizType(bizType),
		MemberId:     memberId,
		IdempotentId: idempotentId,
		SceneCode:    3,
	})
	if err != nil {
		logc.Error(ctx, "get sharing file url failed", logc.Field("err", err))
		return nil, err
	}
	if resp.GetCode() != 0 {
		if resp.GetCode() == 1 { // 处理中
			return nil, ErrNeedWait
		}
		return nil, fmt.Errorf("get sharing file url failed, code: %d", resp.GetCode())
	}
	return &aml_insightv1.GetUserKycDocsResponse_Result_Item{
		ShortTimeUrl: resp.GetShortTimeUrl(),
		Sign:         resp.GetSign(),
	}, nil
}

func (l *AmlInsightStrAdminAPIGetUserKycDocsLogic) GetSupportingDocs(ctx context.Context, memberId int64) ([]*aml_insightv1.GetUserKycDocsResponse_Result_Resource, error) {
	output := []*aml_insightv1.GetUserKycDocsResponse_Result_Resource{}
	cases, err := l.svcCtx.SfCaseModel.FindStrListByMemberID(ctx, memberId)
	if err != nil {
		logc.Error(ctx, "get user sf cases failed", logc.Field("err", err))
		return nil, err
	}
	uniq := make(map[string]struct{})
	for _, c := range cases {
		if _, exists := uniq[c.CaseNumber]; exists {
			continue // 去重。因为有工单合并的逻辑
		}
		uniq[c.CaseNumber] = struct{}{}
		output = append(output, &aml_insightv1.GetUserKycDocsResponse_Result_Resource{
			Title:    fmt.Sprintf("%s case id %s", mappingCaseType(c.CaseType), c.CaseNumber),
			AppealId: c.GroupId,
		})
	}
	return output, nil
}

// func EddOrStr(caseType int32) string {
// 	switch caseType {
// 	case 7, 25:
// 		return "EDD"
// 	case 6, 8, 23, 24, 21, 22, 10, 26, 27:
// 		return "STR"
// 	default:
// 		return fmt.Sprintf("Unknown Case Type: %d", caseType)
// 	}
// }
