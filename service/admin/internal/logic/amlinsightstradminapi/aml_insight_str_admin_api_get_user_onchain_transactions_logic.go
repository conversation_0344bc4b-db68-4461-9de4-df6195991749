package amlinsightstradminapilogic

import (
	"context"
	"fmt"
	"strings"

	"aml-insight/internal/model"
	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightStrAdminAPIGetUserOnchainTransactionsLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightStrAdminAPIGetUserOnchainTransactionsLogic(svcCtx *svc.ServiceContext) *AmlInsightStrAdminAPIGetUserOnchainTransactionsLogic {
	return &AmlInsightStrAdminAPIGetUserOnchainTransactionsLogic{
		svcCtx: svcCtx,
	}
}

var headers = []string{
	"STR Transaction",
	"Date time (UTC)",
	"Transaction Type",
	"from",
	"category (from)",
	"entity (from)",
	"to",
	"category (to)",
	"entity (to)",
	"chain",
	"token",
	"amount",
	"TXID",
}

// GetUserOnchainTransactions Get user onchain transaction list
func (l *AmlInsightStrAdminAPIGetUserOnchainTransactionsLogic) GetUserOnchainTransactions(ctx context.Context, in *aml_insightv1.GetUserOnchainTransactionsRequest) (*aml_insightv1.GetUserOnchainTransactionsResponse, error) {
	resp := &aml_insightv1.GetUserOnchainTransactionsResponse{
		Result: &aml_insightv1.GetUserOnchainTransactionsResponse_Result{},
	}
	if !CheckIfUidPermitted(ctx, l.svcCtx, in.MemberId) {
		resp.Code = 403
		resp.Msg = fmt.Sprintf("uid %d not permitted", in.MemberId)
		return resp, nil
	}
	if in.Limit <= 0 || in.Limit > 100 {
		resp.Code = 400
		resp.Msg = "limit must be between 1 and 10"
		return resp, nil
	}
	if in.Page <= 0 {
		resp.Code = 400
		resp.Msg = "page must be greater than 0"
		return resp, nil
	}
	resp.Result.Headers = headers
	resp.Result.Page = in.Page

	transactions, total, err := l.svcCtx.AmlCaseModel.FindListByMemberID(ctx, in.MemberId, int(in.Page), int(in.Limit))
	if err != nil {
		resp.Code = 500
		resp.Msg = "failed to get user onchain transactions: " + err.Error()
		return resp, nil
	}

	// 获取地址的标签和实体信息
	addresses := make([]string, 0, len(transactions)*2)
	for _, tx := range transactions {
		if tx.FromAddress.String != "" {
			addresses = append(addresses, tx.FromAddress.String)
		}
		if tx.ToAddress.String != "" {
			addresses = append(addresses, tx.ToAddress.String)
		}
	}
	addressLabelMap, err := getAddressCategoryAndEntity(ctx, l.svcCtx, addresses)
	if err != nil {
		resp.Code = 500
		resp.Msg = "failed to get address labels: " + err.Error()
		return resp, nil
	}
	for _, tx := range transactions {
		resp.Result.List = append(resp.Result.List, amlCase2Transaction(tx, addressLabelMap))
	}
	resp.Result.Total = int32(total)

	return resp, nil
}

func getAddressCategoryAndEntity(ctx context.Context, svcCtx *svc.ServiceContext, addresses []string) (map[string]*model.AllAddressLabel, error) {
	// 每100个地址查询一次
	if len(addresses) == 0 {
		return map[string]*model.AllAddressLabel{}, nil
	}
	resp := make(map[string]*model.AllAddressLabel)
	times := (len(addresses) + 99) / 100 // 向上取整
	for i := 0; i < times; i++ {
		start := i * 100
		end := start + 100
		if end > len(addresses) {
			end = len(addresses)
		}
		addressBatch := addresses[start:end]
		labels, err := svcCtx.AllAddressLabelModel.BatchQuery(ctx, addressBatch)
		if err != nil {
			return nil, fmt.Errorf("failed to query address labels: %w", err)
		}
		for _, label := range labels {
			resp[label.Address] = label
		}
	}
	return resp, nil
}

func amlCase2Transaction(tx *model.AmlTransaction, addressLabelMap map[string]*model.AllAddressLabel) *aml_insightv1.GetUserOnchainTransactionsResponse_Transaction {
	var ret aml_insightv1.GetUserOnchainTransactionsResponse_Transaction
	ret.Data = append(ret.Data, strings.ToUpper(tx.SuggestedStr.String))
	ret.Data = append(ret.Data, tx.CreatedAt.Format("02/01/2006 15:04:05"))
	switch tx.ActionType {
	case 1:
		ret.Data = append(ret.Data, "Deposit")
	case 2:
		ret.Data = append(ret.Data, "Withdraw")
	default:
		ret.Data = append(ret.Data, "Unknown")
	}
	ret.Data = append(ret.Data, tx.FromAddress.String)
	if label, ok := addressLabelMap[tx.FromAddress.String]; ok {
		ret.Data = append(ret.Data, label.Category)
		ret.Data = append(ret.Data, label.EntityName)
	} else {
		ret.Data = append(ret.Data, "", "")
	}
	ret.Data = append(ret.Data, tx.ToAddress.String)
	if label, ok := addressLabelMap[tx.ToAddress.String]; ok {
		ret.Data = append(ret.Data, label.Category)
		ret.Data = append(ret.Data, label.EntityName)
	} else {
		ret.Data = append(ret.Data, "", "")
	}
	ret.Data = append(ret.Data, tx.Chain.String)
	ret.Data = append(ret.Data, tx.Coin.String)
	ret.Data = append(ret.Data, fmt.Sprintf("%v", tx.Amount.InexactFloat64()))
	ret.Data = append(ret.Data, tx.TxHash.String)
	return &ret
}
