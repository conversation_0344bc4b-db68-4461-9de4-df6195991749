package amlinsightstradminapilogic

import (
	"context"
	"encoding/base64"
	"fmt"
	"time"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"code.bydev.io/frameworks/byone/core/logc"
	platformkyc "git.bybit.com/svc/stub/pkg/pb/api/kyc"
)

type AmlInsightStrAdminAPIDownloadSelfieZipLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightStrAdminAPIDownloadSelfieZipLogic(svcCtx *svc.ServiceContext) *AmlInsightStrAdminAPIDownloadSelfieZipLogic {
	return &AmlInsightStrAdminAPIDownloadSelfieZipLogic{
		svcCtx: svcCtx,
	}
}

// DownloadSelfieZip 下载用户的Selfie（自拍照），打包成zip文件，返回的是base64编码的zip文件
func (l *AmlInsightStrAdminAPIDownloadSelfieZipLogic) DownloadSelfieZip(ctx context.Context, in *aml_insightv1.DownloadSelfieZipRequest) (*aml_insightv1.DownloadSelfieZipResponse, error) {
	resp := &aml_insightv1.DownloadSelfieZipResponse{}
	if !CheckIfUidPermitted(ctx, l.svcCtx, in.MemberId) {
		resp.Code = 403
		resp.Msg = fmt.Sprintf("uid %d not permitted", in.MemberId)
		return resp, nil
	}
	return l.downloadSelfieZip(ctx, in.MemberId)
}

func (l *AmlInsightStrAdminAPIDownloadSelfieZipLogic) downloadSelfieZip(ctx context.Context, memberId int64) (*aml_insightv1.DownloadSelfieZipResponse, error) {
	var resp = &aml_insightv1.DownloadSelfieZipResponse{
		Result: &aml_insightv1.DownloadSelfieZipResponse_Result{
			Status: "success",
		},
	}
	memberKycResp, err := l.svcCtx.KycInternalClient.GetMemberKYC(ctx, &platformkyc.GetMemberKYCRequest{
		MemberId:                  memberId,
		ObtainOnboardingAuthInfos: true,
		ObtainSupplementAuthInfos: true,
	})
	if err != nil {
		logc.Error(ctx, "get kyc info failed", logc.Field("err", err))
		resp.Code = 500
		resp.Msg = err.Error()
		resp.Result = nil
		return resp, nil
	}
	authInfos := []*platformkyc.GetMemberKYCResponse_KYCAuthInfo{}
	authInfos = append(authInfos, memberKycResp.GetOnboardingAuthInfos()...)
	authInfos = append(authInfos, memberKycResp.GetSupplementAuthInfos()...)
	selfies := make([]*aml_insightv1.GetUserKycDocsResponse_Result_Item, 0)
	for _, info := range authInfos {
		if info.Level != 1 { // 只处理kyc1的地址证明
			continue
		}
		img, err := GetShareImage(ctx, l.svcCtx.KycInternalClient, memberId, info.IdempotentId, 3) // 3 is the type for Selfie
		if err == ErrNeedWait {
			resp.Result.Status = "waiting"
			return resp, nil
		}
		if err != nil {
			logc.Warn(ctx, "get sharing file url failed", logc.Field("err", err))
		}
		if img != nil {
			selfies = append(selfies, img)
		}
	}
	if len(selfies) == 0 {
		resp.Code = 500
		resp.Msg = "get sharing file url failed, no Selfie images found"
		return resp, nil
	}
	// zip the images
	zipData, err := zipImages(ctx, selfies, l.svcCtx.PhotoSharingKey)
	if err != nil {
		logc.Error(ctx, "zip images failed", logc.Field("err", err))
		resp.Code = 500
		resp.Msg = "zip images failed: " + err.Error()
	}
	resp.Result.Status = "success"
	resp.Result.Body = base64.StdEncoding.EncodeToString(zipData) // base64 encoded zip file
	resp.Result.Filename = fmt.Sprintf("selfie_%d_%s.zip", memberId, time.Now().Format("2006_01_02_15_04_05"))
	return resp, nil
}
