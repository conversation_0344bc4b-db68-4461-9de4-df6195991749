package amlinsightstradminapilogic

import (
	"context"
	"strconv"

	"code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/user"
	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/sechub-go/api/secrypto"
	euser "git.bybit.com/svc/stub/pkg/pb/api/consts/euser"
	platformkyc "git.bybit.com/svc/stub/pkg/pb/api/kyc"
	memberinternal "git.bybit.com/svc/stub/pkg/pb/api/user"
	"git.bybit.com/svc/stub/pkg/pb/enums/kyc"

	"aml-insight/service/admin/internal/svc"
)

type UserInfoCtx struct {
	svcCtx   *svc.ServiceContext
	memberId int64
	kycLevel int64
	vipLevel string

	// load data
	memberKyc    *platformkyc.GetMemberKYCResponse
	fiatUserInfo *user.RiskUserResponse
	SubUids      []string // 子账号列表
}

func NewUserInfoCtx(ctx context.Context, svcCtx *svc.ServiceContext, memberId int64) (*UserInfoCtx, error) {
	memberKycResp, err := svcCtx.KycInternalClient.GetMemberKYC(ctx, &platformkyc.GetMemberKYCRequest{
		MemberId:                  memberId,
		ObtainBaseInfo:            true,
		ObtainOnboardingAuthInfos: true,
		ObtainSupplementAuthInfos: true,
		ObtainCustodyInfo:         true,
		ObtainDuplicateFaceInfo:   true,
		ObtainAuthWorkflowInfo:    true,
		ObtainVerificationProcess: false,
		SupplementBusinessId:      "",
	})
	if err != nil {
		logc.Error(ctx, "get kyc info failed", logc.Field("err", err))
		return nil, err
	}

	fiatUserInfo, err := svcCtx.FiatUserClient.GetRiskUserInfoByRealTime(ctx, &user.GetRiskUserInfoByRealTimeRequest{UserId: memberId})
	if err != nil {
		logc.Error(ctx, "get fiat user info failed", logc.Field("err", err))
		return nil, err
	}

	vipInfo, err := svcCtx.FiatUserClient.GetUserVipInfo(ctx, &user.GetUserVipInfoRequest{
		UserId:   strconv.Itoa(int(memberId)),
		BrokerId: "0",
	})
	if err != nil {
		logc.Error(ctx, "get user vip info failed", logc.Field("err", err))
		return nil, err
	}

	subUids := []string{}
	relationInfo, err := svcCtx.MemberClient.QueryRelationByMember(ctx, &memberinternal.QueryRelationByMemberRequest{
		MemberIds:          []int64{memberId},
		MemberRelationType: euser.MemberRelationType_MEMBER_RELATION_TYPE_UNSPECIFIED,
	})
	if err != nil {
		logc.Error(ctx, "get member relation failed", logc.Field("err", err))
		return nil, err
	}

	for k, v := range relationInfo.GetResult() {
		if k == memberId {
			for _, subMember := range v.MemberRelations {
				if subMember.GetMemberId() == memberId {
					// 只返回子账号的memberId
					subUids = append(subUids, strconv.FormatInt(subMember.GetTargetMemberId(), 10))
				}
			}
		}
	}
	kycLevel := kyc.MemberKYCLevel(0)
	for _, info := range memberKycResp.GetOnboardingAuthInfos() {
		if info.GetLevel() > kycLevel {
			kycLevel = info.GetLevel()
		}
	}
	for _, info := range memberKycResp.GetSupplementAuthInfos() {
		if info.GetLevel() > kycLevel {
			kycLevel = info.GetLevel()
		}
	}

	return &UserInfoCtx{
		svcCtx:       svcCtx,
		memberId:     memberId,
		kycLevel:     int64(kycLevel),
		vipLevel:     strconv.Itoa(int(vipInfo.GetVipLevel())),
		memberKyc:    memberKycResp,
		fiatUserInfo: fiatUserInfo,
		SubUids:      subUids,
	}, nil
}

func (u *UserInfoCtx) GetMemberKyc() *platformkyc.GetMemberKYCResponse {
	return u.memberKyc
}

func (u *UserInfoCtx) GetKyc1AuthInfo() *platformkyc.GetMemberKYCResponse_KYCAuthInfo {
	if len(u.memberKyc.GetOnboardingAuthInfos()) > 0 {
		return u.memberKyc.GetOnboardingAuthInfos()[0]
	}
	return nil
}

func (u *UserInfoCtx) GetFiatUserInfo() *user.RiskUserResponse {
	return u.fiatUserInfo
}

func (u *UserInfoCtx) Decrypt(dest string) (string, error) {
	res, err := secrypto.Decrypt("ase256gcm", u.svcCtx.StoreSechubEncryptKey, dest)
	if err != nil {
		return "", err
	}

	return res, nil
}
