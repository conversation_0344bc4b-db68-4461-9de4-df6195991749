package amlinsightstradminapilogic

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path"
	"time"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	appealv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/appeal/v1"
	"code.bydev.io/frameworks/byone/core/logc"
)

type AmlInsightStrAdminAPIDownloadSupportingDocsZipLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightStrAdminAPIDownloadSupportingDocsZipLogic(svcCtx *svc.ServiceContext) *AmlInsightStrAdminAPIDownloadSupportingDocsZipLogic {
	return &AmlInsightStrAdminAPIDownloadSupportingDocsZipLogic{
		svcCtx: svcCtx,
	}
}

// DownloadSupportingDocsZip 下载Case的补充材料，打包成zip文件，返回的是base64编码的zip文件
func (l *AmlInsightStrAdminAPIDownloadSupportingDocsZipLogic) DownloadSupportingDocsZip(ctx context.Context, in *aml_insightv1.DownloadSupportingDocsZipRequest) (*aml_insightv1.DownloadSupportingDocsZipResponse, error) {
	if !CheckIfUidPermitted(ctx, l.svcCtx, in.MemberId) {
		return &aml_insightv1.DownloadSupportingDocsZipResponse{
			Code: 403,
			Msg:  fmt.Sprintf("uid %d not permitted", in.MemberId),
		}, nil
	}
	if resp, err := l.packSupportingDocs(ctx, in.MemberId, in.AppealId); err != nil {
		logc.Error(ctx, "pack supporting docs failed", logc.Field("err", err))
		return &aml_insightv1.DownloadSupportingDocsZipResponse{
			Code: 500,
			Msg:  err.Error(),
		}, nil
	} else {
		return resp, nil
	}
}

func (l *AmlInsightStrAdminAPIDownloadSupportingDocsZipLogic) packSupportingDocs(ctx context.Context, memberId int64, appealId string) (*aml_insightv1.DownloadSupportingDocsZipResponse, error) {
	var resp = &aml_insightv1.DownloadSupportingDocsZipResponse{
		Result: &aml_insightv1.DownloadSupportingDocsZipResponse_Result{},
	}

	appealDetail, err := l.svcCtx.AppealAdminClient.GetAppealInfo(ctx, &appealv1.GetAppealInfoRequest{
		Uid:      memberId,
		AppealId: appealId,
	})
	if err != nil {
		logc.Error(ctx, "get appeal detail failed", logc.Field("err", err))
		return nil, fmt.Errorf("get appeal detail failed: %w", err)
	}

	var resources []*Resource
	for _, v := range appealDetail.GetResult().GetCommitRecords() {
		for _, d := range v.GetCommitDataList() {
			for _, r := range d.GetResources() {
				resources = append(resources, &Resource{Resource: r})
			}
		}
	}

	content, err := zipResources(ctx, l.svcCtx.AppealAdminClient, appealDetail.GetResult().GetUserInfo().GetUid(), resources)
	if err != nil {
		logc.Error(ctx, "zip files failed", logc.Field("err", err))
		return nil, err
	}
	resp.Result.Status = "success"
	resp.Result.Body = base64.StdEncoding.EncodeToString(content)
	resp.Result.Filename = fmt.Sprintf("supporting-documents-UID-%d-%d.zip", memberId, time.Now().UnixNano())
	return resp, nil
}

type Resource struct {
	*appealv1.Resource
	d []byte // 存储下载的文件内容
}

func zipResources(ctx context.Context, appealClient appealv1.SecurityAppealAdminAPIClient, memberId int64, resources []*Resource) (output []byte, err error) {
	var buf = bytes.NewBuffer(nil)
	zipWriter := zip.NewWriter(buf)
	defer func() {
		if err1 := zipWriter.Close(); err1 != nil {
			logc.Error(ctx, "close zip writer failed", logc.Field("err", err1))
			err = fmt.Errorf("close zip writer failed: %w", err1)
		}
		if err == nil {
			output = buf.Bytes() // 获取zip文件的字节内容
		}
	}()

	if len(resources) == 0 {
		logc.Warn(ctx, "no resources to zip")
		return nil, errors.New("no documents to download")
	}

	// TODO mapreduce并发下载
	for _, r := range resources {
		if r.GetIsEncrypted() == 0 { // 未加密，直接下载
			req, _ := http.NewRequestWithContext(ctx, http.MethodGet, r.GetUrl(), nil)
			if req == nil {
				continue
			}
			resp, err := http.DefaultClient.Do(req)
			if err != nil {
				logc.Error(ctx, "download file failed", logc.Field("url", r.GetUrl()), logc.Field("err", err))
				continue
			}
			defer resp.Body.Close()
			r.d, _ = io.ReadAll(resp.Body)
		} else {
			resp, err := appealClient.DecryptDocument(ctx, &appealv1.DecryptDocumentRequest{
				ResourceId: r.GetResourceId(),
				Uid:        memberId,
			})
			if err != nil {
				logc.Error(ctx, "decrypt document failed", logc.Field("resource_id", r.GetResourceId()), logc.Field("uid", memberId), logc.Field("err", err))
				continue
			}
			r.d, err = base64.StdEncoding.DecodeString(resp.GetResult().GetData())
			if err != nil {
				logc.Error(ctx, "decode base64 document failed", logc.Field("resource_id", r.GetResourceId()), logc.Field("uid", memberId), logc.Field("err", err))
				continue
			}
		}
	}

	for _, r := range resources {
		if len(r.d) == 0 {
			continue
		}
		fileName := r.GetFileName()
		if fileName == "" {
			u, _ := url.Parse(r.GetUrl())
			if u != nil && path.Base(u.Path) != "" {
				fileName = path.Base(u.Path)
			} else {
				fileName = fmt.Sprintf("file-%d", time.Now().UnixNano()) // 如果没有文件名，则使用时间戳作为文件名
			}
		}
		h := zip.FileHeader{
			Name:               fileName,
			UncompressedSize64: uint64(len(r.d)),
		}
		h.Modified = time.Now()
		h.SetMode(0644)        // 设置文件权限为644
		h.Method = zip.Deflate // 使用Deflate压缩方法

		fwriter, err := zipWriter.CreateHeader(&h)
		if err != nil {
			return nil, fmt.Errorf("create zip header failed: %w", err)
		}
		_, err = fwriter.Write(r.d)
		if err != nil {
			return nil, fmt.Errorf("write image to zip failed: %w", err)
		}
	}

	return
}
