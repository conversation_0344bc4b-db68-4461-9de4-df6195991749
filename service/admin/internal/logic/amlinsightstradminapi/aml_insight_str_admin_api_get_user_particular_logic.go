package amlinsightstradminapilogic

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	bizconfigv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/bizconfig/v1"
	"code.bydev.io/frameworks/byone/core/collection"
	"code.bydev.io/frameworks/byone/core/logc"
	"git.bybit.com/svc/stub/pkg/pb/enums/kyc"
	"github.com/biter777/countries"
)

type AmlInsightStrAdminAPIGetUserParticularLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightStrAdminAPIGetUserParticularLogic(svcCtx *svc.ServiceContext) *AmlInsightStrAdminAPIGetUserParticularLogic {
	return &AmlInsightStrAdminAPIGetUserParticularLogic{
		svcCtx: svcCtx,
	}
}

type UserParticularInfo struct {
	FullName                 string
	NativeCharacterName      string
	KYCLevel                 string
	VIPLevel                 string
	TypeOfIDDocument         string
	IDNumber                 string
	CountryWhoIssuedDocument string
	IssueDateOfDocument      string
	ExpiryDateOfDocument     string
	KYCProviderInfo          []string
	DateOfBirth              string
	Nationality              string
	UID                      string
	SubaccountUID            []string
	CommonKYCUIDs            []string
	SalesforceCaseID         []string
	ResidentialAddress       string
	MobileNumber             string
	Email                    string
	IPAddress                string
	WalletAddresses          []string
	NameOfBank               string
	BankAccountNumber        string
}

// GetUserParticular Get user deatil info
func (l *AmlInsightStrAdminAPIGetUserParticularLogic) GetUserParticular(ctx context.Context, in *aml_insightv1.GetUserParticularRequest) (*aml_insightv1.GetUserParticularResponse, error) {
	resp := &aml_insightv1.GetUserParticularResponse{
		Result: &aml_insightv1.GetUserParticularResponse_Result{},
	}
	if !CheckIfUidPermitted(ctx, l.svcCtx, in.MemberId) {
		resp.Code = 403
		resp.Msg = fmt.Sprintf("uid %d not permitted", in.MemberId)
		return resp, nil
	}
	info, err := getUserParticularInfo(ctx, l.svcCtx, in.MemberId)
	if err != nil {
		logc.Error(ctx, "get user particular info failed", logc.Field("err", err))
		resp.Code = 500
		resp.Msg = err.Error()
		return resp, nil
	}

	var items []*aml_insightv1.GetUserParticularResponse_Item
	items = appendItems(items, "Full Name/Entity Name", info.NativeCharacterName)
	items = appendItems(items, "World-Check ID", "-")
	items = appendItems(items, "KYC Level", info.KYCLevel)
	items = appendItems(items, "VIP Level", info.VIPLevel)
	items = appendItems(items, "Type of ID Document", mappingDocType(info.TypeOfIDDocument))
	items = appendItems(items, "ID Number", info.IDNumber)
	items = appendItems(items, "Country of Issuance", info.CountryWhoIssuedDocument)
	items = appendItems(items, "Date of ID Document Issuance", info.IssueDateOfDocument)
	items = appendItems(items, "Date of ID Document Expiry", info.ExpiryDateOfDocument)
	items = appendItems(items, "Date of Birth/Incorporation", info.DateOfBirth)
	items = appendItems(items, "Nationality/Country of Incorporation", info.Nationality)
	items = appendItems(items, "Verification ID", strings.Join(info.KYCProviderInfo, "\n"))
	items = appendItems(items, "Duplicates Detected", every5ItemsNewLine(info.CommonKYCUIDs))
	items = appendItems(items, "UID", info.UID)
	items = appendItems(items, "Subaccount UID", every5ItemsNewLine(info.SubaccountUID))
	items = appendItems(items, "Salesforce Case ID (if any)", strings.Join(info.SalesforceCaseID, "\n"))
	items = appendItems(items, "Residential/Operating Address (If available)", info.ResidentialAddress)
	items = appendItems(items, "Mobile Number (If available)", info.MobileNumber)
	items = appendItems(items, "Email Address", info.Email)
	items = appendItems(items, "IP Address (If available)", info.IPAddress)
	items = appendItems(items, "Wallet Address(es) on Bybit", strings.Join(info.WalletAddresses, "\n"))
	items = appendItems(items, "Name of the Bank", info.NameOfBank)
	items = appendItems(items, "Bank Account Number", info.BankAccountNumber)
	resp.Result.List = items
	return resp, nil
}

func mappingDocType(input string) string {
	switch strings.ToLower(input) {
	case "id_card":
		return "Identification Card"
	case "passport":
		return "Passport"
	case "driver":
		return "Driver's License"
	case "recidence":
		return "Residence Permit"
	case "bill":
		return "Bill"
	case "bvn":
		return "Nigerian Bank Verification Number"
	case "nga_nin":
		return "Nigerian National Identification Number"
	case "idn_nik":
		return "Indonesian Nomor Induk Kependudukan (Identification Number)"
	case "ind_aadhaar":
		return "Indian Aadhaar Number (Identification Number)"
	default:
		return input
	}
}

func getUserParticularInfo(ctx context.Context, svcCtx *svc.ServiceContext, memberId int64) (*UserParticularInfo, error) {
	var output UserParticularInfo
	userInfoCtx, err := NewUserInfoCtx(ctx, svcCtx, memberId)
	if err != nil {
		logc.Error(ctx, "get user info failed", logc.Field("err", err))
		return nil, err
	}
	kyc1AuthInfo := userInfoCtx.GetKyc1AuthInfo()
	memberKyc := userInfoCtx.GetMemberKyc()
	fiatUserInfo := userInfoCtx.GetFiatUserInfo()

	output.FullName = joinFields(kyc1AuthInfo.GetFirstnameEn(), kyc1AuthInfo.GetLastnameEn())
	output.NativeCharacterName = joinFields(kyc1AuthInfo.GetFirstname(), kyc1AuthInfo.GetLastname())
	output.KYCLevel = strconv.Itoa(int(userInfoCtx.kycLevel))
	output.VIPLevel = userInfoCtx.vipLevel
	output.TypeOfIDDocument = kyc1AuthInfo.GetType()
	output.IDNumber = kyc1AuthInfo.GetNumber()
	output.CountryWhoIssuedDocument = kyc1AuthInfo.GetIssueCountry()
	output.IssueDateOfDocument = Date2DDMMYYYY(kyc1AuthInfo.GetIssuedDate())
	output.ExpiryDateOfDocument = Date2DDMMYYYY(kyc1AuthInfo.GetExpiryDate())
	for _, auth := range userInfoCtx.memberKyc.OnboardingAuthInfos {
		info := kycProviderName(auth.GetProvider()) + " " + auth.GetApplicantId()
		output.KYCProviderInfo = append(output.KYCProviderInfo, info)
	}
	for _, auth := range userInfoCtx.memberKyc.SupplementAuthInfos {
		info := kycProviderName(auth.GetProvider()) + " " + auth.GetApplicantId()
		output.KYCProviderInfo = append(output.KYCProviderInfo, info)
	}
	output.KYCProviderInfo = removeDupStrings(output.KYCProviderInfo)
	output.DateOfBirth = Date2DDMMYYYY(kyc1AuthInfo.GetDob())
	output.Nationality = memberKyc.GetBaseInfo().GetNationality()
	output.UID = strconv.FormatInt(memberId, 10)
	output.SubaccountUID = userInfoCtx.SubUids
	output.CommonKYCUIDs = numbersToStrings(memberKyc.GetDuplicateFaceInfo().GetMemberIds())
	output.SalesforceCaseID = getUserSfCases(ctx, svcCtx, memberId)
	output.ResidentialAddress = memberKyc.GetBaseInfo().GetAddress().GetFullAddress()

	if fiatUserInfo.GetData().GetMobile() != "" {
		mobile, err := userInfoCtx.Decrypt(fiatUserInfo.GetData().GetMobile())
		if err != nil {
			logc.Errorw(ctx, "decrypt mobile failed", logc.Field("err", err))
			output.MobileNumber = "query mobile failed"
		} else {
			combined := combineCodeAndMobile(fiatUserInfo.GetData().GetMobileCountryCode(), mobile)
			output.MobileNumber = combined
		}
	} else {
		output.MobileNumber = "-"
	}
	if fiatUserInfo.GetData().GetEmail() != "" {
		email, err := userInfoCtx.Decrypt(fiatUserInfo.GetData().GetEmail())
		if err != nil {
			logc.Errorw(ctx, "decrypt email failed", logc.Field("err", err))
			output.Email = "query email failed"
		} else {
			output.Email = email
		}
	} else {
		output.Email = "-"
	}
	output.IPAddress = fiatUserInfo.GetData().GetRegIpCountryCode()
	output.WalletAddresses = getMemberDepositAddress(ctx, svcCtx, memberId)
	output.NameOfBank = "-"
	output.BankAccountNumber = "-"
	return &output, nil
}

func kycProviderName(p kyc.KYCProvider) string {
	switch p {
	case kyc.KYCProvider_PROVIDER_SUMSUB:
		return "sumsub"
	case kyc.KYCProvider_PROVIDER_JUMIO:
		return "jumio"
	case kyc.KYCProvider_PROVIDER_ONFIDO:
		return "Onfido"
	case kyc.KYCProvider_PROVIDER_AAI:
		return "AAI"
	default:
		return "-"
	}
}

var C, _ = collection.NewCache(time.Minute)

func removeDupStrings(items []string) []string {
	uniqueItems := make(map[string]struct{})
	for _, item := range items {
		uniqueItems[item] = struct{}{}
	}
	result := make([]string, 0, len(uniqueItems))
	for item := range uniqueItems {
		result = append(result, item)
	}
	return result
}

func CheckIfUidPermitted(ctx context.Context, svcCtx *svc.ServiceContext, memberId int64) bool {
	if memberId <= 0 {
		return false
	}
	permittedUids, err := C.Take(ctx, "only_one_key", func() (any, error) {
		// 获取tag的业务描述的配置
		confResp, err := svcCtx.BizConfigClient.GetBizConfig(ctx, &bizconfigv1.GetBizConfigRequest{
			Scenario: []string{
				"str_proved_uids",
			},
		})
		if err != nil || len(confResp.Result) != 1 {
			return nil, fmt.Errorf("get biz config failed: %v", err)
		}
		var uidsMap = map[string]bool{}
		for _, item := range confResp.Result {
			if item.Scenario == "str_proved_uids" {
				strValue := item.GetStrValue()
				elems := strings.Fields(strValue)
				for _, elem := range elems {
					uidsMap[elem] = true
				}
			}
		}
		return uidsMap, nil
	})
	if err != nil {
		logc.Error(ctx, "get permitted uids failed", logc.Field("err", err))
		return false
	}
	return permittedUids.(map[string]bool)[strconv.FormatInt(memberId, 10)]
}

func combineCodeAndMobile(countryCode string, mobile string) string {
	if countryCode == "" {
		return mobile
	}
	countryByName := countries.ByName(countryCode)
	if !countryByName.IsValid() {
		return fmt.Sprintf("(Invalid country code: %s) %s", countryCode, mobile)
	}
	codes := countryByName.CallCodes()
	if len(codes) == 0 {
		return mobile
	}
	if len(codes) == 1 { // 大多数国家只有一个code
		return fmt.Sprintf("%s %s", codes[0], mobile)
	}
	return fmt.Sprintf("%s %v %s", countryCode, countryByName.CallCodes(), mobile) // 如DOM，有多个code
}

func getMemberDepositAddress(ctx context.Context, svcCtx *svc.ServiceContext, memberId int64) []string {
	depositAddresses, err := svcCtx.AmlCaseModel.FindDepositAddressByMemberID(ctx, memberId)
	if err != nil {
		logc.Error(ctx, "get deposit address failed", logc.Field("err", err))
		return []string{"-"}
	}

	var items []string
	for _, addr := range depositAddresses {
		items = append(items, addr.Chain+" "+addr.ToAddress)
	}
	if len(items) == 0 {
		return []string{"-"}
	}
	return items
}

func Date2DDMMYYYY(date string) string {
	if date == "" {
		return "-"
	}
	// Assuming date is in the format "YYYY-MM-DD"
	parts := strings.Split(date, "-")
	if len(parts) != 3 {
		return date // return original if format is unexpected
	}
	return parts[2] + "/" + parts[1] + "/" + parts[0]
}

func appendItems(items []*aml_insightv1.GetUserParticularResponse_Item, title, value string) []*aml_insightv1.GetUserParticularResponse_Item {
	if value == "" {
		value = "-"
	}
	return append(items, &aml_insightv1.GetUserParticularResponse_Item{
		Title: title,
		Value: value,
	})
}

func joinFields(field1, field2 string) string {
	if field1 == "" && field2 == "" {
		return "-"
	}
	if field1 == "" {
		return field2
	}
	if field2 == "" {
		return field1
	}
	return field1 + " " + field2
}

func getUserSfCases(ctx context.Context, svcCtx *svc.ServiceContext, memberId int64) []string {
	cases, err := svcCtx.SfCaseModel.FindStrListByMemberID(ctx, memberId)
	if err != nil {
		logc.Error(ctx, "get user sf cases failed", logc.Field("err", err))
		return []string{"-"}
	}
	var caseNumbers []string
	for _, item := range cases {
		if item.CaseNumber != "" {
			caseNumbers = append(caseNumbers, mappingCaseType(item.CaseType)+" "+item.CaseNumber)
		}
	}
	if len(caseNumbers) == 0 {
		return []string{"-"}
	}
	return caseNumbers
}

func numbersToStrings[T ~int64](numbers []T) []string {
	strs := make([]string, len(numbers))
	for i, num := range numbers {
		strs[i] = strconv.FormatInt(int64(num), 10)
	}
	return strs
}

func joinCommonKYCMemberIds(memberIds []int64) string {
	if len(memberIds) == 0 {
		return "-"
	}
	var ids []string
	for _, id := range memberIds {
		ids = append(ids, strconv.FormatInt(id, 10))
	}
	return every5ItemsNewLine(ids)
}

// 每5个uid换行。为了在前端展示时更好看
func every5ItemsNewLine(items []string) string {
	if len(items) == 0 {
		return "-"
	}
	var sb strings.Builder
	for i, id := range items {
		if i > 0 && i%5 == 0 {
			sb.WriteString("\n")
		} else if i > 0 {
			sb.WriteString(" ")
		}
		sb.WriteString(id)
	}
	return sb.String()
}

func mappingCaseType(caseType int32) string {
	switch caseType {
	case 7:
		return "Manual EDD"
	case 25:
		return "Manual EDD (NEW)"
	case 6:
		return "On Chain AML"
	case 8:
		return "On Chain AML(KYT)"
	case 23:
		return "On Chain AML(KYT) (New)"
	case 24:
		return "On Chain AML(KYT) (New) - Empty"
	case 21:
		return "On Chain AML (New)"
	case 22:
		return "On Chain AML (New) - Empty"
	case 10:
		return "STR/SAR"
	case 26:
		return "STR/SAR(ESCALATE) (New)"
	case 27:
		return "STR/SAR (New)"
	default:
		return fmt.Sprintf("Unknown Case Type: %d", caseType)
	}
}
