package amlinsightstradminapilogic

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"path"
	"time"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"code.bydev.io/frameworks/byone/core/logc"
	platformkyc "git.bybit.com/svc/stub/pkg/pb/api/kyc"
)

type AmlInsightStrAdminAPIDownloadPoaZipLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightStrAdminAPIDownloadPoaZipLogic(svcCtx *svc.ServiceContext) *AmlInsightStrAdminAPIDownloadPoaZipLogic {
	return &AmlInsightStrAdminAPIDownloadPoaZipLogic{
		svcCtx: svcCtx,
	}
}

// DownloadPoaZip 下载用户的POA（地址证明），打包成zip文件，返回的是base64编码的zip文件
func (l *AmlInsightStrAdminAPIDownloadPoaZipLogic) DownloadPoaZip(ctx context.Context, in *aml_insightv1.DownloadPoaZipRequest) (*aml_insightv1.DownloadPoaZipResponse, error) {
	resp := &aml_insightv1.DownloadPoaZipResponse{}
	if !CheckIfUidPermitted(ctx, l.svcCtx, in.MemberId) {
		resp.Code = 403
		resp.Msg = fmt.Sprintf("uid %d not permitted", in.MemberId)
		return resp, nil
	}
	return l.downloadPoaZip(ctx, in.MemberId)
}

func (l *AmlInsightStrAdminAPIDownloadPoaZipLogic) downloadPoaZip(ctx context.Context, memberId int64) (*aml_insightv1.DownloadPoaZipResponse, error) {
	var resp = &aml_insightv1.DownloadPoaZipResponse{
		Result: &aml_insightv1.DownloadPoaZipResponse_Result{
			Status: "success",
		},
	}
	memberKycResp, err := l.svcCtx.KycInternalClient.GetMemberKYC(ctx, &platformkyc.GetMemberKYCRequest{
		MemberId:                  memberId,
		ObtainOnboardingAuthInfos: true,
		ObtainSupplementAuthInfos: true,
	})
	if err != nil {
		logc.Error(ctx, "get kyc info failed", logc.Field("err", err))
		resp.Code = 500
		resp.Msg = err.Error()
		resp.Result = nil
		return resp, nil
	}
	authInfos := []*platformkyc.GetMemberKYCResponse_KYCAuthInfo{}
	authInfos = append(authInfos, memberKycResp.GetOnboardingAuthInfos()...)
	authInfos = append(authInfos, memberKycResp.GetSupplementAuthInfos()...)
	poas := make([]*aml_insightv1.GetUserKycDocsResponse_Result_Item, 0)
	for _, info := range authInfos {
		if info.Level != 2 { // 只处理kyc2的地址证明
			continue
		}
		img, err := GetShareImage(ctx, l.svcCtx.KycInternalClient, memberId, info.IdempotentId, 1)
		if err == ErrNeedWait {
			resp.Result.Status = "waiting"
			return resp, nil
		}
		if err != nil {
			logc.Warn(ctx, "get sharing file url failed", logc.Field("err", err))
		}
		if img != nil {
			poas = append(poas, img)
		}
	}
	if len(poas) == 0 {
		resp.Code = 500
		resp.Msg = "get sharing file url failed, no POA images found"
		return resp, nil
	}
	// zip the images
	zipData, err := zipImages(ctx, poas, l.svcCtx.PhotoSharingKey)
	if err != nil {
		logc.Error(ctx, "zip images failed", logc.Field("err", err))
		resp.Code = 500
		resp.Msg = "zip images failed: " + err.Error()
	}
	resp.Result.Status = "success"
	resp.Result.Body = base64.StdEncoding.EncodeToString(zipData) // base64 encoded zip file
	resp.Result.Filename = fmt.Sprintf("poa_%d_%s.zip", memberId, time.Now().Format("2006_01_02_15_04_05"))
	return resp, nil
}

func zipImages(ctx context.Context, images []*aml_insightv1.GetUserKycDocsResponse_Result_Item, aesKey string) (output []byte, err error) {
	var buf = bytes.NewBuffer(nil)
	zipWriter := zip.NewWriter(buf)
	defer func() {
		if err1 := zipWriter.Close(); err1 != nil {
			logc.Error(ctx, "close zip writer failed", logc.Field("err", err1))
			err = fmt.Errorf("close zip writer failed: %w", err1)
		}
		if err == nil {
			output = buf.Bytes() // 获取zip文件的字节内容
		}
	}()
	for _, img := range images {
		d, _, err := DecryptImg(ctx, img.ShortTimeUrl, img.Sign, aesKey)
		if err != nil {
			return nil, fmt.Errorf("decrypt image failed: %w", err)
		}
		baseName := path.Base(img.ShortTimeUrl)
		h := zip.FileHeader{
			Name:               fmt.Sprintf("%d_%s", time.Now().UnixNano(), baseName),
			UncompressedSize64: uint64(len(d)),
		}
		h.Modified = time.Now()
		h.SetMode(0644)        // 设置文件权限为644
		h.Method = zip.Deflate // 使用Deflate压缩方法

		fwriter, err := zipWriter.CreateHeader(&h)
		if err != nil {
			return nil, fmt.Errorf("create zip header failed: %w", err)
		}
		_, err = fwriter.Write(d)
		if err != nil {
			return nil, fmt.Errorf("write image to zip failed: %w", err)
		}
	}
	return
}
