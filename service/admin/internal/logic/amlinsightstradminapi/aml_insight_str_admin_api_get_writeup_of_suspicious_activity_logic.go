package amlinsightstradminapilogic

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"html/template"
	"strconv"
	"strings"
	"time"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"code.bydev.io/frameworks/byone/core/logc"
	platformkyc "git.bybit.com/svc/stub/pkg/pb/api/kyc"
)

type AmlInsightStrAdminAPIGetWriteupOfSuspiciousActivityLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightStrAdminAPIGetWriteupOfSuspiciousActivityLogic(svcCtx *svc.ServiceContext) *AmlInsightStrAdminAPIGetWriteupOfSuspiciousActivityLogic {
	return &AmlInsightStrAdminAPIGetWriteupOfSuspiciousActivityLogic{
		svcCtx: svcCtx,
	}
}

type ActivityDetail struct {
	NumericalAge              string `json:"numerical_age"`
	Nationality               string `json:"nationality"`
	WalletAddressNumber       string `json:"wallet_address_number"`
	RiskTag                   string `json:"risk_tag"`
	RiskTagEntityName         string `json:"risk_tag_entity_name"`
	BlockchainAnalyticsVendor string `json:"blockchain_analytics_vendor"`
	NameOfTheLaw              string `json:"name_of_the_law"`
	DescriptionOfTheRiskTag   string `json:"description_of_the_risk_tag"`
	Gender                    string `json:"gender"`
}

// GetWriteupOfSuspiciousActivity Get user writeup of suspicious activity
func (l *AmlInsightStrAdminAPIGetWriteupOfSuspiciousActivityLogic) GetWriteupOfSuspiciousActivity(ctx context.Context, in *aml_insightv1.GetWriteupOfSuspiciousActivityRequest) (*aml_insightv1.GetWriteupOfSuspiciousActivityResponse, error) {
	resp := &aml_insightv1.GetWriteupOfSuspiciousActivityResponse{
		Result: &aml_insightv1.GetWriteupOfSuspiciousActivityResponse_Result{},
	}

	if !CheckIfUidPermitted(ctx, l.svcCtx, in.MemberId) {
		resp.Code = 403
		resp.Msg = fmt.Sprintf("uid %d not permitted", in.MemberId)
		return resp, nil
	}

	activityDetail, err := generateSuspiciousActivityInfo(ctx, in.MemberId, l.svcCtx)
	if err != nil {
		logc.Error(ctx, "generate suspicious activity info failed", logc.Field("err", err))
		resp.Code = 500
		resp.Msg = err.Error()
		return resp, nil
	}

	buf := bytes.NewBuffer(nil)
	if err := ActivityTemplate.Execute(buf, activityDetail); err != nil {
		logc.Error(ctx, "execute activity template failed", logc.Field("err", err))
		resp.Code = 500
		resp.Msg = "failed to render activity template: " + err.Error()
		return resp, nil
	}

	resp.Result.Body = base64.StdEncoding.EncodeToString(buf.Bytes())
	return resp, nil
}

func generateSuspiciousActivityInfo(ctx context.Context, memberId int64, svcCtx *svc.ServiceContext) (*ActivityDetail, error) {
	var activityDetail ActivityDetail
	memberKycResp, err := svcCtx.KycInternalClient.GetMemberKYC(ctx, &platformkyc.GetMemberKYCRequest{
		MemberId:                  memberId,
		ObtainBaseInfo:            true,
		ObtainOnboardingAuthInfos: true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get member kyc: %w", err)
	}
	var kyc1AuthInfo *platformkyc.GetMemberKYCResponse_KYCAuthInfo
	if len(memberKycResp.GetOnboardingAuthInfos()) > 0 {
		kyc1AuthInfo = memberKycResp.GetOnboardingAuthInfos()[0]
	}
	if kyc1AuthInfo != nil {
		dob, _ := time.Parse("2006-01-02", kyc1AuthInfo.GetDob())
		if !dob.IsZero() {
			// Calculate numerical age
			years := time.Now().Year() - dob.Year()
			months := time.Now().Month() - dob.Month()
			days := time.Now().Day() - dob.Day()
			if months < 0 || (months == 0 && days < 0) {
				years--
			}
			activityDetail.NumericalAge = strconv.Itoa(years)
		}
		activityDetail.Gender = normalizeGender(kyc1AuthInfo.GetGender().String())
	}
	activityDetail.Nationality = memberKycResp.GetBaseInfo().GetNationality()

	strCase, _ := svcCtx.AmlCaseModel.FindFirstStrByMemberID(ctx, memberId)
	if strCase != nil {
		if strCase.ActionType == 1 { // deposit
			activityDetail.WalletAddressNumber = strCase.FromAddress.String
		}
		if strCase.ActionType == 2 { // withdraw
			activityDetail.WalletAddressNumber = strCase.ToAddress.String
		}

		label, _ := svcCtx.AllAddressLabelModel.FindOneByAddressChain(ctx, activityDetail.WalletAddressNumber, strCase.Chain.String)
		if label != nil {
			activityDetail.RiskTag = label.Category
			activityDetail.RiskTagEntityName = label.EntityName
			activityDetail.BlockchainAnalyticsVendor = mappingSource2Vendor(label.Source)
		}
	}

	fillDefaultActivityDetail(&activityDetail)
	return &activityDetail, nil
}

func fillDefaultActivityDetail(detail *ActivityDetail) {
	if detail.NumericalAge == "" {
		detail.NumericalAge = "[age not available]"
	}
	if detail.Nationality == "" {
		detail.Nationality = "[nationality not available]"
	}
	if detail.WalletAddressNumber == "" {
		detail.WalletAddressNumber = "[-]"
	}
	if detail.RiskTag == "" {
		detail.RiskTag = "[-]"
	}
	if detail.RiskTagEntityName == "" {
		detail.RiskTagEntityName = "[-]"
	}
	if detail.BlockchainAnalyticsVendor == "" {
		detail.BlockchainAnalyticsVendor = "[-]"
	}
	if detail.NameOfTheLaw == "" {
		detail.NameOfTheLaw = "[-]"
	}
	if detail.DescriptionOfTheRiskTag == "" {
		detail.DescriptionOfTheRiskTag = "[-]"
	}
	if detail.Gender == "" {
		detail.Gender = "male/female"
	}
}

func normalizeGender(gender string) string {
	switch strings.ToLower(gender) {
	case "male":
		return "male"
	case "female":
		return "female"
	default: // unknown or other
		return ""
	}
}

func mappingSource2Vendor(source string) string {
	lowerSource := strings.ToLower(source)
	switch lowerSource {
	case "ch", "chainalysis", "ca":
		return "Chainalysis"
	case "et", "elliptic":
		return "Elliptic"
	case "trm", "trm labs":
		return "TRM"
	case "bybit":
		return "Bybit AML"
	default:
		return source
	}
}

var ActivityTemplate = template.Must(template.New("activityTemplate").Parse(ActivityTemplateHtml))

const ActivityTemplateHtml = `
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Writeup of Suspicious Activity</title>
</head>

<body>
    <h2>Writeup of Suspicious Activity</h2>
    <h3>1 Background of the case</h3>
    <ul>
        <li>
            The client is a <b>{{.NumericalAge}}</b>-year-old <b>{{.Nationality}}</b> {{.Gender}}.
        </li>
        <li>
            The client was flagged for making a withdrawal to/receiving a deposit from the wallet address: <b>{{.WalletAddressNumber}}</b>
        </li>
        <li>
            <b>{{.WalletAddressNumber}}</b> is noted to be a wallet address associated with <b>{{.RiskTag}}</b> -related entity,
            <b>{{.RiskTagEntityName}}</b> as per <b>{{.BlockchainAnalyticsVendor}}</b>.
            </li>
    </ul>
    <h3>2 Reasons for Filing</h3>
    <ul>
        <li>
            <b>{{.RiskTag}}</b> is illegal in <b>{{.Nationality}}</b>. The <b>{{.NameOfTheLaw}}</b> criminalizes the <b>{{.DescriptionOfTheRiskTag}}</b> of <b>{{.RiskTag}}</b>
        </li>
        <li>
            The client's engagement with such high-risk entities has raised significant compliance and regulatory concerns. These dealings may potentially breach international AML/CFT standards, regulatory requirements and the institution's internal policies and guidelines, raising substantial concerns about the rationale and legitimacy of the transactions. ISAR filing is thus warranted, given these concerns.
        </li>
        <li>
            The above suspicious transaction(s) were flagged as part of a comprehensive 2025 re-scanning of all historical transactions carried out on the platform, with counterparty wallet addresses matched against an internal database containing an up-to-date registry of wallet addresses and their linked risk entities. This remediative measure was implemented as part of the company’s broader governance and compliance framework, aimed at maintaining adherence to regulatory standards and minimizing exposure to financial crime risks.
        </li>
    </ul>
    <h3>3 Case Conclusion</h3>
    <ul>
        <li>
            Analysis shows that the client made a direct withdrawal to a wallet address associated with <b>{{.RiskTag}}</b>.
        </li>
        <li>
            The client's direct involvement with such a high-risk entity raises suspicions as to their intent and potential deliberate involvement in illicit activities. As such, this calls for immediate escalation to the relevant authorities and offboarding of the client.
        </li>
    </ul>

</body>

</html>
`
