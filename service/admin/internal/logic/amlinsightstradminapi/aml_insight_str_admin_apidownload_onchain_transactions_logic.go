package amlinsightstradminapilogic

import (
	"context"
	"encoding/base64"
	"encoding/xml"
	"fmt"
	"strings"
	"time"

	"aml-insight/internal/model"
	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightStrAdminAPIDownloadOnchainTransactionsLogic struct {
	svcCtx *svc.ServiceContext
}

type TransactionDetail struct {
	StrTransaction  string `xml:"STRTransaction"`
	DateTime        string `xml:"DateTime"`
	TransactionType string `xml:"TransactionType"`
	FromAddress     string `xml:"FromAddress"`
	FromCategory    string `xml:"FromCategory"`
	FromEntity      string `xml:"FromEntity"`
	ToAddress       string `xml:"ToAddress"`
	ToCategory      string `xml:"ToCategory"`
	ToEntity        string `xml:"ToEntity"`
	Chain           string `xml:"Chain"`
	Token           string `xml:"Token"`
	Amount          string `xml:"Amount"`
	TxID            string `xml:"TxID"`
}

// 用于xml输出
type TransactionDetails struct {
	Items []*TransactionDetail `xml:"TransactionDetail"`
}

func NewAmlInsightStrAdminAPIDownloadOnchainTransactionsLogic(svcCtx *svc.ServiceContext) *AmlInsightStrAdminAPIDownloadOnchainTransactionsLogic {
	return &AmlInsightStrAdminAPIDownloadOnchainTransactionsLogic{
		svcCtx: svcCtx,
	}
}

// DownloadOnchainTransactions download user onchain transaction
func (l *AmlInsightStrAdminAPIDownloadOnchainTransactionsLogic) DownloadOnchainTransactions(ctx context.Context, in *aml_insightv1.DownloadOnchainTransactionsRequest) (*aml_insightv1.DownloadOnchainTransactionsResponse, error) {
	resp := &aml_insightv1.DownloadOnchainTransactionsResponse{
		Result: &aml_insightv1.DownloadOnchainTransactionsResponse_Result{},
	}
	if !CheckIfUidPermitted(ctx, l.svcCtx, in.MemberId) {
		resp.Code = 403
		resp.Msg = fmt.Sprintf("uid %d not permitted", in.MemberId)
		return resp, nil
	}

	// TODO 异步？？
	transactions, err := l.svcCtx.AmlCaseModel.FindAllByMemberID(ctx, in.MemberId)
	if err != nil {
		resp.Code = 500
		resp.Msg = "failed to get user onchain transactions: " + err.Error()
		return resp, nil
	}

	// 获取地址的标签和实体信息
	addresses := make([]string, 0, len(transactions)*2)
	for _, tx := range transactions {
		if tx.FromAddress.String != "" {
			addresses = append(addresses, tx.FromAddress.String)
		}
		if tx.ToAddress.String != "" {
			addresses = append(addresses, tx.ToAddress.String)
		}
	}
	addressLabelMap, err := getAddressCategoryAndEntity(ctx, l.svcCtx, addresses)
	if err != nil {
		resp.Code = 500
		resp.Msg = "failed to get address labels: " + err.Error()
		return resp, nil
	}

	items := transactions2XmlItems(transactions, addressLabelMap)

	content, err := xml.MarshalIndent(TransactionDetails{
		Items: items,
	}, "", "  ")
	if err != nil {
		resp.Code = 500
		resp.Msg = "failed to marshal xml: " + err.Error()
		return resp, nil
	}

	xmlContent := fmt.Sprintf(`<?xml version="1.0" encoding="UTF-8"?>
%s`, string(content))

	resp.Result.Body = base64.StdEncoding.EncodeToString([]byte(xmlContent))
	resp.Result.Filename = fmt.Sprintf("onchain_transactions_%d_%s.xml", in.MemberId, time.Now().Format("2006_01_02_15_04_05"))

	return resp, nil
}

func transactions2XmlItems(transactions []*model.AmlTransaction, addressLabelMap map[string]*model.AllAddressLabel) []*TransactionDetail {
	items := make([]*TransactionDetail, 0, len(transactions))
	for _, tx := range transactions {
		item := &TransactionDetail{
			StrTransaction:  strings.ToUpper(tx.SuggestedStr.String),
			DateTime:        tx.CreatedAt.Format("02/01/2006 15:04:05"),
			TransactionType: "",
			FromAddress:     tx.FromAddress.String,
			FromCategory:    "",
			FromEntity:      "",
			ToAddress:       tx.ToAddress.String,
			ToCategory:      "",
			ToEntity:        "",
			Chain:           tx.Chain.String,
			Token:           tx.Coin.String,
			Amount:          fmt.Sprintf("%v", tx.Amount.InexactFloat64()),
			TxID:            tx.TxHash.String,
		}
		switch tx.ActionType {
		case 1:
			item.TransactionType = "Deposit"
		case 2:
			item.TransactionType = "Withdraw"
		default:
			item.TransactionType = "Unknown"
		}

		if label, ok := addressLabelMap[tx.FromAddress.String]; ok {
			item.FromCategory = label.Category
			item.FromEntity = label.EntityName
		}
		if label, ok := addressLabelMap[tx.ToAddress.String]; ok {
			item.ToCategory = label.Category
			item.ToEntity = label.EntityName
		}

		items = append(items, item)
	}
	return items
}
