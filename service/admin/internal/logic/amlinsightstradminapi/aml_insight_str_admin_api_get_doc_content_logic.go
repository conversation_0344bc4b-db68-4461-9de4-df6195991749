package amlinsightstradminapilogic

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"path"
	"strings"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"code.bydev.io/frameworks/byone/core/logc"
)

type AmlInsightStrAdminAPIGetDocContentLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightStrAdminAPIGetDocContentLogic(svcCtx *svc.ServiceContext) *AmlInsightStrAdminAPIGetDocContentLogic {
	return &AmlInsightStrAdminAPIGetDocContentLogic{
		svcCtx: svcCtx,
	}
}

// GetDocContent 获取图片内容，返回的是base64编码的图片内容
func (l *AmlInsightStrAdminAPIGetDocContentLogic) GetDocContent(ctx context.Context, in *aml_insightv1.GetDocContentRequest) (*aml_insightv1.GetDocContentResponse, error) {

	resp := &aml_insightv1.GetDocContentResponse{
		Result: &aml_insightv1.GetDocContentResponse_Result{},
	}
	if !CheckIfUidPermitted(ctx, l.svcCtx, in.MemberId) {
		resp.Code = 403
		resp.Msg = fmt.Sprintf("uid %d not permitted", in.MemberId)
		return resp, nil
	}
	content, dataType, err := DecryptImg(ctx, in.ShortTimeUrl, in.Sign, l.svcCtx.PhotoSharingKey)
	if err != nil {
		logc.Error(ctx, "decrypt image failed", logc.Field("err", err))
		resp.Code = 500
		resp.Msg = fmt.Sprintf("decrypt image failed: %v", err)
		return resp, nil
	}
	resp.Result.Data = base64.StdEncoding.EncodeToString(content)
	resp.Result.DataType = dataType
	return resp, nil
}

func DecryptImg(ctx context.Context, imageUrl string, sign string, aesKey string) ([]byte, string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", imageUrl, nil)
	if err != nil {
		return nil, "", err
	}
	req.Header.Add("Content-Sign", sign)
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, "", err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		logc.Warnf(ctx, "download image failed, status code: %d, body: %s", resp.StatusCode, body)
		return nil, "", err
	}

	buf := bytes.NewBuffer(nil)
	if err := DecryptStream(resp.Body, aesKey, buf); err != nil {
		logc.Error(ctx, "decrypt stream failed", logc.Field("err", err))
		return nil, "", err
	}
	return buf.Bytes(), convertDataType(req.URL.Path), nil
}

func convertDataType(pathName string) string {
	switch strings.ToLower(path.Ext(pathName)) {
	case ".jpg", ".jpeg", ".png", ".gif", ".bmp":
		return "fiat-image"
	case ".pdf":
		return "fiat-pdf"
	case ".mp4", ".avi", ".mov", ".wmv":
		return "fiat-video"
	default:
		return "other"
	}
}

func DecryptStream(cipherText io.Reader, aeskey string, output io.Writer) error {
	block, err := aes.NewCipher([]byte(aeskey))
	if err != nil {
		return err
	}
	// 读取IV
	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(cipherText, iv); err != nil {
		return err
	}
	stream := cipher.NewCFBDecrypter(block, iv)
	reader := &cipher.StreamReader{
		S: stream,
		R: cipherText}
	if _, err := io.Copy(output, reader); err != nil {
		return err
	}
	return nil
}
