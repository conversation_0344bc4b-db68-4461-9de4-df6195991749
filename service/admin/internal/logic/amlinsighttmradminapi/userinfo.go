package amlinsighttmradminapilogic

import (
	"context"

	quotev1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/quote/v1"
	"code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/user"
	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/sechub-go/api/secrypto"
	platformkyc "git.bybit.com/svc/stub/pkg/pb/api/kyc"
	"github.com/shopspring/decimal"

	"aml-insight/service/admin/internal/svc"
)

type UserInfoCtx struct {
	svcCtx   *svc.ServiceContext
	memberId int64

	// load data
	memberKyc    *platformkyc.GetMemberKYCResponse
	fiatUserInfo *user.RiskUserResponse
	TRYQuote     decimal.Decimal
}

func NewUserInfoCtx(ctx context.Context, svcCtx *svc.ServiceContext, memberId int64) *UserInfoCtx {
	memberKycResp, err := svcCtx.KycInternalClient.GetMemberKYC(ctx, &platformkyc.GetMemberKYCRequest{
		MemberId:                  memberId,
		ObtainBaseInfo:            true,
		ObtainOnboardingAuthInfos: true,
		ObtainSupplementAuthInfos: true,
		ObtainCustodyInfo:         true,
		ObtainDuplicateFaceInfo:   true,
		ObtainAuthWorkflowInfo:    true,
		ObtainVerificationProcess: false,
		SupplementBusinessId:      "",
	})
	if err != nil {
		logc.Error(ctx, "get kyc info failed", logc.Field("err", err))
	}

	fiatUserInfo, err := svcCtx.FiatUserClient.GetRiskUserInfoByRealTime(ctx, &user.GetRiskUserInfoByRealTimeRequest{UserId: memberId})
	if err != nil {
		logc.Error(ctx, "get fiat user info failed", logc.Field("err", err))
	}

	var TRYQuote decimal.Decimal
	quoteResp, err := svcCtx.QuoteClient.ExchangeRate(ctx, &quotev1.ExchangeRateRequest{
		Source: "USD",
		Target: "TRY",
	})
	if err != nil {
		logc.Error(ctx, "get quote failed", logc.Field("err", err))
	} else {
		TRYQuote, _ = decimal.NewFromString(quoteResp.GetExchangeRate())
	}

	return &UserInfoCtx{
		svcCtx:   svcCtx,
		memberId: memberId,

		memberKyc:    memberKycResp,
		fiatUserInfo: fiatUserInfo,
		TRYQuote:     TRYQuote,
	}
}

func (u *UserInfoCtx) GetMemberKyc() *platformkyc.GetMemberKYCResponse {
	return u.memberKyc
}

func (u *UserInfoCtx) GetKyc1AuthInfo() *platformkyc.GetMemberKYCResponse_KYCAuthInfo {
	if len(u.memberKyc.GetOnboardingAuthInfos()) > 0 {
		return u.memberKyc.GetOnboardingAuthInfos()[0]
	}
	return nil
}

func (u *UserInfoCtx) GetFiatUserInfo() *user.RiskUserResponse {
	return u.fiatUserInfo
}

func (u *UserInfoCtx) Decrypt(dest string) (string, error) {
	res, err := secrypto.Decrypt("ase256gcm", u.svcCtx.StoreSechubEncryptKey, dest)
	if err != nil {
		return "", err
	}

	return res, nil
}
