package amlinsighttmradminapilogic

import (
	"context"
	"encoding/base64"
	"strconv"
	"sync"

	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/core/mr"
	"github.com/360EntSecGroup-Skylar/excelize"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightTmrAdminAPIDownloadTransactionLogic struct {
	svcCtx           *svc.ServiceContext
	transactionLogic *AmlInsightTmrAdminAPIGetUserTransactionListLogic
}

func NewAmlInsightTmrAdminAPIDownloadTransactionLogic(svcCtx *svc.ServiceContext) *AmlInsightTmrAdminAPIDownloadTransactionLogic {
	return &AmlInsightTmrAdminAPIDownloadTransactionLogic{
		svcCtx:           svcCtx,
		transactionLogic: NewAmlInsightTmrAdminAPIGetUserTransactionListLogic(svcCtx),
	}
}

func (l *AmlInsightTmrAdminAPIDownloadTransactionLogic) DownloadTransaction(ctx context.Context, in *aml_insightv1.DownloadTransactionRequest) (*aml_insightv1.DownloadTransactionResponse, error) {
	resp := &aml_insightv1.DownloadTransactionResponse{
		Result: &aml_insightv1.DownloadTransactionResponse_Result{},
	}
	file := excelize.NewFile()
	var mu sync.Mutex
	m := make(map[string]*aml_insightv1.GetUserTransactionListResponse_Result)

	typeList := []string{"onchain_deposit", "onchain_withdrawal",
		"fiat_deposit", "fiat_withdrawal"}
	mr.ForEach(func(source chan<- string) {
		for _, each := range typeList {
			source <- each
		}
	}, func(item string) {
		resp, err := l.transactionLogic.GetUserTransactionList(ctx, &aml_insightv1.GetUserTransactionListRequest{
			MemberId: in.MemberId,
			Type:     item,
			Limit:    300,
			Page:     1,
		})
		if err != nil {
			logc.Errorw(ctx, "get user transaction list failed", logc.Field("err", err))
			return
		}
		mu.Lock()
		defer mu.Unlock()
		m[item] = resp.GetResult()
	})
	for _, each := range typeList {
		l.fillDatToSheet(ctx, file, each, m[each])
	}
	file.DeleteSheet("Sheet1")
	buf, err := file.WriteToBuffer()
	if err != nil {
		resp.Code = 500
		resp.Msge = err.Error()
		return resp, nil
	}

	resp.Result.Body = base64.StdEncoding.EncodeToString(buf.Bytes())
	return resp, nil
}

func (l *AmlInsightTmrAdminAPIDownloadTransactionLogic) fillDatToSheet(ctx context.Context,
	f *excelize.File,
	sheet string,
	data *aml_insightv1.GetUserTransactionListResponse_Result) {
	f.NewSheet(sheet)
	f.SetSheetRow(sheet, CoordinatesToCellName(1, 1), &data.Headers)

	for i, each := range data.GetList() {
		arr := each.GetData()
		f.SetSheetRow(sheet, CoordinatesToCellName(1, i+2), &arr)
	}
}

// ColumnNumberToName provides a function to convert the integer to Excel
// sheet column title.
//
// Example:
//
//	ColumnNumberToName(37) // returns "AK", nil
func columnNumberToName(num int) string {
	estimatedLength := 0
	for n := num; n > 0; n = (n - 1) / 26 {
		estimatedLength++
	}

	result := make([]byte, estimatedLength)
	for num > 0 {
		estimatedLength--
		result[estimatedLength] = byte((num-1)%26 + 'A')
		num = (num - 1) / 26
	}
	return string(result)
}

// CoordinatesToCellName converts [X, Y] coordinates to alpha-numeric cell
// name or returns an error.
//
// Example:
//
//	excelize.CoordinatesToCellName(1, 1) // returns "A1", nil
//	excelize.CoordinatesToCellName(1, 1, true) // returns "$A$1", nil
func CoordinatesToCellName(col, row int) string {
	colName := columnNumberToName(col)
	return colName + strconv.Itoa(row)
}
