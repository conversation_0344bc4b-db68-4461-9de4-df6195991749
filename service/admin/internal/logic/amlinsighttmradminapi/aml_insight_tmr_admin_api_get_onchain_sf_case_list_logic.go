package amlinsighttmradminapilogic

import (
	"context"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightTmrAdminAPIGetOnchainSfCaseListLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightTmrAdminAPIGetOnchainSfCaseListLogic(svcCtx *svc.ServiceContext) *AmlInsightTmrAdminAPIGetOnchainSfCaseListLogic {
	return &AmlInsightTmrAdminAPIGetOnchainSfCaseListLogic{
		svcCtx: svcCtx,
	}
}

// GetSfCaseList Get all salesforce case list, just triggered case
func (l *AmlInsightTmrAdminAPIGetOnchainSfCaseListLogic) GetOnchainSfCaseList(ctx context.Context, in *aml_insightv1.GetOnchainSfCaseListRequest) (*aml_insightv1.GetOnchainSfCaseListResponse, error) {
	resp := &aml_insightv1.GetOnchainSfCaseListResponse{
		Result: &aml_insightv1.GetOnchainSfCaseListResponse_Result{},
	}

	cases, err := l.svcCtx.SfCaseModel.FindListByRequestIdOrMemberId(ctx, in.RequestId, in.MemberId, in.Limit, in.Page)
	if err != nil {
		resp.Code = 500
		resp.Msge = err.Error()
		return resp, nil
	}

	for _, each := range cases {
		resp.Result.List = append(resp.Result.List, &aml_insightv1.GetOnchainSfCaseListResponse_SfCase{
			Id:                        each.Id,
			RequestId:                 each.RequestId,
			MemberId:                  int64(each.MemberId),
			CaseType:                  each.CaseType,
			SfStatus:                  each.SfStatus,
			AmlFlow:                   each.AmlFlow,
			DepositWithdrawal:         each.DepositWithdrawal,
			DepositWithdrawalAddress:  each.DepositWithdrawalAddress,
			DepositWithdrawalValue:    each.DepositWithdrawalValue,
			DepositWithdrawalUsdValue: each.DepositWithdrawalUsdValue,
			TriggerLabel:              each.TriggerLabel,
			TransactionTime:           each.CreatedAt.Unix(),
		})
	}
	resp.Result.Page = in.Page
	if len(cases) > 0 {
		resp.Result.Total = (in.Page + 1) * in.Limit
	}

	return resp, nil
}
