package amlinsighttmradminapilogic

import (
	"context"
	"fmt"
	"time"

	assetshow "code.bydev.io/cht/asset/asset-show-stub.git/pkg/bybit/assetshow/inner/v1"
	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/card"
	"code.bydev.io/frameworks/byone/core/logc"
	"git.bybit.com/svc/stub/pkg/pb/enums/kyc"
	"github.com/tidwall/gjson"

	"aml-insight/service/admin/internal/svc"
)

type AmlInsightTmrAdminAPIGetUserInfoDetailLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightTmrAdminAPIGetUserInfoDetailLogic(svcCtx *svc.ServiceContext) *AmlInsightTmrAdminAPIGetUserInfoDetailLogic {
	return &AmlInsightTmrAdminAPIGetUserInfoDetailLogic{
		svcCtx: svcCtx,
	}
}

// GetUserInfoDetail Get user detail info and transaction list
func (l *AmlInsightTmrAdminAPIGetUserInfoDetailLogic) GetUserInfoDetail(ctx context.Context, in *aml_insightv1.GetUserInfoDetailRequest) (*aml_insightv1.GetUserInfoDetailResponse, error) {
	resp := &aml_insightv1.GetUserInfoDetailResponse{
		Result: &aml_insightv1.GetUserInfoDetailResponse_Result{},
	}
	userInfoCtx := NewUserInfoCtx(ctx, l.svcCtx, in.MemberId)
	kyc1AuthInfo := userInfoCtx.GetKyc1AuthInfo()
	memberKyc := userInfoCtx.GetMemberKyc()
	fiatUserInfo := userInfoCtx.GetFiatUserInfo()

	// identify information
	identifySection := &aml_insightv1.GetUserInfoDetailResponse_Section{
		Title: "Identify Information",
	}
	resp.Result.List = append(resp.Result.List, identifySection)
	var items []*aml_insightv1.GetUserInfoDetailResponse_Item
	items = appendItems(items, "UID", fmt.Sprintf("%d", in.MemberId))
	items = appendItems(items, "National ID number", kyc1AuthInfo.GetAdditionalNumber())
	items = appendItems(items, "Name", kyc1AuthInfo.GetFirstname())
	items = appendItems(items, "Surname", kyc1AuthInfo.GetLastname())
	items = appendItems(items, "Gender", kyc1AuthInfo.GetGender().String())
	items = appendItems(items, "Date of Birth", kyc1AuthInfo.GetDob())
	items = appendItems(items, "Bybit account opening date", kyc1AuthInfo.GetReviewTime())
	items = appendItems(items, "Relationship with transaction", "Customer doing transaction on behalf of himself")
	items = appendItems(items, "Nation", memberKyc.GetBaseInfo().GetNationality())
	items = appendItems(items, "Type of ID card", kyc1AuthInfo.GetType())
	switch kyc1AuthInfo.GetType() {
	case "recidence":
		items = appendItems(items, "Residence permit number", kyc1AuthInfo.GetNumber())
	default:
		items = appendItems(items, "ID serial number", kyc1AuthInfo.GetNumber())
	}
	items = appendItems(items, "Address", memberKyc.GetBaseInfo().GetAddress().GetFullAddress())
	if fiatUserInfo.GetData().GetEmail() != "" {
		email, err := userInfoCtx.Decrypt(fiatUserInfo.GetData().GetEmail())
		if err != nil {
			logc.Errorw(ctx, "decrypt email failed", logc.Field("err", err))
		} else {
			items = appendItems(items, "Email", email)
		}
	}
	if fiatUserInfo.GetData().GetMobile() != "" {
		mobile, err := userInfoCtx.Decrypt(fiatUserInfo.GetData().GetMobile())
		if err != nil {
			logc.Errorw(ctx, "decrypt mobile failed", logc.Field("err", err))
		} else {
			items = appendItems(items, "Mobile", mobile)
		}
	}
	switch kyc1AuthInfo.GetProvider() {
	case kyc.KYCProvider_PROVIDER_SUMSUB:
		items = appendItems(items, "Document Url", fmt.Sprintf("https://cockpit.sumsub.com/checkus#/applicant/%s/basicInfo?clientId=yijinin",
			kyc1AuthInfo.GetApplicantId()))
	case kyc.KYCProvider_PROVIDER_ONFIDO:
		items = appendItems(items, "Document Url", fmt.Sprintf("https://dashboard.onfido.com/results?p=1&search=%s&sort=desc&sort_by=updated_at",
			kyc1AuthInfo.GetApplicantId()))
	}
	identifySection.Items = items

	// transaction
	if in.GetFiatRequestId() != "" || in.GetOnchainRequestId() != "" {
		transactionSection := l.getTransactionSection(ctx, userInfoCtx, in)
		if len(transactionSection.GetItems()) != 0 {
			resp.Result.List = append(resp.Result.List, transactionSection)
		}
	}

	return resp, nil
}

func appendItems(items []*aml_insightv1.GetUserInfoDetailResponse_Item, title, value string) []*aml_insightv1.GetUserInfoDetailResponse_Item {
	return append(items, &aml_insightv1.GetUserInfoDetailResponse_Item{
		Title: title,
		Value: value,
	})
}
func (l *AmlInsightTmrAdminAPIGetUserInfoDetailLogic) getTransactionSection(ctx context.Context, info *UserInfoCtx, in *aml_insightv1.GetUserInfoDetailRequest) *aml_insightv1.GetUserInfoDetailResponse_Section {
	transactionSection := &aml_insightv1.GetUserInfoDetailResponse_Section{
		Title: "Transaction",
		//Detail: transactionMap,
	}
	var items []*aml_insightv1.GetUserInfoDetailResponse_Item
	switch {
	case in.GetFiatRequestId() != "":
		transactions, err := l.svcCtx.FiatAmlTransactionModel.FindListByUidAndRequestIdAndOrderId(ctx, in.MemberId, "", in.GetFiatRequestId(), "", 1, 1)
		if err != nil {
			logc.Errorw(ctx, "get fiat transaction failed", logc.Field("err", err))
			return nil
		}
		if len(transactions) == 0 {
			return nil
		}
		transaction := transactions[0]
		items = appendItems(items, "Transaction Type", "Fiat "+transaction.TransactionType)
		if transaction.FiatCurrencySymbol == "TRY" {
			items = appendItems(items, "Transaction Amount in TRY", transaction.FiatCurrencyAmount.String())
		} else {
			items = appendItems(items, "Transaction Amount in TRY", transaction.FiatConvertedUsdAmount.Mul(info.TRYQuote).String()+" "+"TRY")
		}
		items = appendItems(items, "Transaction Amount in USD", transaction.FiatConvertedUsdAmount.String()+" "+"USD")
		items = appendItems(items, "Fiat Currency", transaction.FiatCurrencySymbol)
		items = appendItems(items, "Fiat amount", transaction.FiatCurrencyAmount.String())
		items = appendItems(items, "Transaction Channel", transaction.ChannelType)
		items = appendItems(items, "By whom the suspicious activity was detected", "compliance unit")
		//items = appendItems(items, "Which financial institution was an intermediary", "")
		items = appendItems(items, "Transaction ID", transaction.OrderNo)

		if cardID := gjson.Get(transaction.ExtInfo.String, "card_token_id").String(); cardID != "" {
			cardResp, err := l.svcCtx.CardManagerFacadeServiceClient.GetCardDetail(ctx, &card.CardDetailReq{
				AppId:       "pay-security",
				CardTokenId: cardID,
			})
			if err != nil {
				logc.Errorw(ctx, "GetCardDetail failed", logc.Field("err", err))
			} else {
				items = appendItems(items, "Card Type", cardResp.GetCardType())
				switch cardResp.GetCardType() {
				//CREDITCARD-信用卡 APM-三方钱包 BANK_ACCOUNT-银行账号
				case "CREDITCARD":
					detail := cardResp.GetCredit()
					items = appendItems(items, "Card No", detail.GetCardNo())
					items = appendItems(items, "Card Bin Num", detail.GetCardBin().GetCardBinNum())
					items = appendItems(items, "Card Country", detail.GetCardBin().GetCountry())
					items = appendItems(items, "Card Brand", detail.GetCardBin().GetBrand())

				case "APM":
				case "BANK_ACCOUNT":
					account := cardResp.GetAccount()
					if account != nil && account.GetBankInfo() != nil {
						if account.GetBankInfo().GetIban() != "" {
							iban, err := info.Decrypt(account.GetBankInfo().GetIban())
							if err != nil {
								logc.Errorw(ctx, "IbanDecryptError", logc.Field("error", err), logc.Field("Iban", account.GetBankInfo().GetIban()))
							} else {
								items = appendItems(items, "IBAN", iban)
							}
							items = appendItems(items, "Card Country", account.GetBankInfo().GetCountry())
							items = appendItems(items, "Card Brand", account.GetBankInfo().GetBranch())
						}
					}
				}
			}
		}

	case in.GetOnchainRequestId() != "":
		transaction, err := l.svcCtx.AmlCaseModel.FindOneByMemberIDAndRequestID(ctx, in.MemberId, in.GetOnchainRequestId())
		if err != nil {
			logc.Errorw(ctx, "get onchain transaction failed", logc.Field("err", err))
			return nil
		}
		if transaction == nil {
			return nil
		}
		var address string
		switch transaction.ActionType {
		case 1:
			items = appendItems(items, "Transaction Type", "Onchain Deposit")
			items = appendItems(items, "Crypto from wallet address", transaction.FromAddress)
			items = appendItems(items, "Crypto to wallet address", transaction.ToAddress)
			address = transaction.FromAddress
		case 2:
			items = appendItems(items, "Transaction Type", "Onchain Withdraw")
			items = appendItems(items, "Crypto from wallet address", transaction.FromAddress)
			items = appendItems(items, "Crypto to wallet address", transaction.ToAddress)
			address = transaction.ToAddress
		}

		items = appendItems(items, "Transaction Amount in TRY", transaction.AmountUsd.Mul(info.TRYQuote).String()+" "+"TRY")
		items = appendItems(items, "Transaction Amount in USD", transaction.AmountUsd.String()+" "+"USD")
		items = appendItems(items, "Crypto Type", transaction.Coin)
		items = appendItems(items, "Crypto Chain", transaction.Chain)
		items = appendItems(items, "Transaction Amount in crypto", transaction.Amount.String())
		items = appendItems(items, "Transaction ID", transaction.TxHash)
		items = appendItems(items, "Transaction Channel", "Internet")

		if addressLabel, err := l.svcCtx.AddressLabelModel.FindOneByAddressChain(ctx, address, transaction.Chain); err != nil {
			logc.Errorw(ctx, "Get address label failed", logc.Field("err", err))
		} else {
			items = appendItems(items, "Which financial institution was an intermediary", addressLabel.DetailName)
		}
		items = appendItems(items, "Label", transaction.Label)
	}

	if balance, err := l.svcCtx.AssetShowInternalClient.GetUserTotalBalance(ctx, &assetshow.GetUserTotalBalanceRequest{
		UserId:              in.MemberId,
		QuoteCoin:           "TRY",
		DemotionCheckEnable: false,
		BalanceType:         0,
		BalanceBizType:      0,
	}); err != nil {
		logc.Errorw(ctx, "get user total balance failed", logc.Field("err", err))
	} else {
		items = appendItems(items, "Account Balance", balance.GetOriginTotalBalance())
	}
	if t := l.getLastTransactionTime(ctx, in); t != "" {
		items = appendItems(items, "Last transaction date", t)
	}
	transactionSection.Items = items
	return transactionSection
}

func (l *AmlInsightTmrAdminAPIGetUserInfoDetailLogic) getLastTransactionTime(ctx context.Context, in *aml_insightv1.GetUserInfoDetailRequest) string {
	var t time.Time
	if lastOnchainCase, err := l.svcCtx.AmlCaseModel.FindListByMemberIDAndActionType(ctx, in.MemberId, 0, 1, 1); err != nil {

	} else {
		if len(lastOnchainCase) > 0 {
			t = lastOnchainCase[0].CreatedAt
		}
	}
	if lastFiatTransaction, err := l.svcCtx.FiatAmlTransactionModel.FindListByUidAndRequestIdAndOrderId(ctx, in.MemberId, "", "", "", 1, 1); err != nil {
	} else {
		if len(lastFiatTransaction) > 0 {
			if t.Before(lastFiatTransaction[0].AtTime) {
				t = lastFiatTransaction[0].AtTime
			}
		}
	}
	if t.IsZero() {
		return ""
	} else {
		return t.Format("2006-01-02 15:04:05")
	}
}
