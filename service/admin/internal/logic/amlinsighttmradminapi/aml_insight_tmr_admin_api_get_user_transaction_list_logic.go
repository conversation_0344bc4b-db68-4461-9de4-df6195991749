package amlinsighttmradminapilogic

import (
	"context"
	"fmt"

	"github.com/tidwall/gjson"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

var (
	onchainHeaders = []string{"requestID", "type", "tXHash", "memberID", "fromAddress", "toAddress", "coin", "chain", "amount", "amountUsd", "orderTime"}
	fiatHeaders    = []string{"requestID", "type", "orderID", "memberID", "fiat", "amount", "amountUsd", "channel", "IP", "device", "orderTime"}
)

type AmlInsightTmrAdminAPIGetUserTransactionListLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightTmrAdminAPIGetUserTransactionListLogic(svcCtx *svc.ServiceContext) *AmlInsightTmrAdminAPIGetUserTransactionListLogic {
	return &AmlInsightTmrAdminAPIGetUserTransactionListLogic{
		svcCtx: svcCtx,
	}
}

// GetUserTransactionList Get user transaction list
func (l *AmlInsightTmrAdminAPIGetUserTransactionListLogic) GetUserTransactionList(ctx context.Context, in *aml_insightv1.GetUserTransactionListRequest) (*aml_insightv1.GetUserTransactionListResponse, error) {
	resp := &aml_insightv1.GetUserTransactionListResponse{
		Result: &aml_insightv1.GetUserTransactionListResponse_Result{},
	}

	// onchain_deposit, onchain_withdrawal, fiat_deposit, fiat_withdrawal
	switch in.Type {
	case "onchain_deposit", "onchain_withdrawal":
		var ty string
		var actionType int
		if in.Type == "onchain_deposit" {
			ty = "Deposit"
			actionType = 1
		} else {
			ty = "Withdraw"
			actionType = 2
		}
		resp.Result.Headers = onchainHeaders
		transactions, err := l.svcCtx.AmlCaseModel.FindListByMemberIDAndActionType(ctx, in.MemberId, actionType, int(in.Limit), int(in.Page))
		if err != nil {
			resp.Code = 500
			resp.Msge = err.Error()
			return resp, nil
		}

		for _, each := range transactions {
			resp.Result.List = append(resp.Result.List, &aml_insightv1.GetUserTransactionListResponse_Transaction{
				Data: []string{
					each.RequestId,
					ty,
					each.TxHash,
					fmt.Sprintf("%d", each.MemberId),
					each.FromAddress,
					each.ToAddress,
					each.Coin,
					each.Chain,
					each.Amount.String(),
					each.AmountUsd.String(),
					each.CreatedAt.Format("2006-01-02 15:04:05"),
				},
			})
		}

	case "fiat_deposit", "fiat_withdrawal":
		var ty string
		if in.Type == "fiat_deposit" {
			ty = "deposit"
		} else {
			ty = "withdraw"
		}
		resp.Result.Headers = fiatHeaders
		transactions, err := l.svcCtx.FiatAmlTransactionModel.FindListByUidAndRequestIdAndOrderId(ctx, in.MemberId, ty, "", "", int(in.Limit), int(in.Page))
		if err != nil {
			resp.Code = 500
			resp.Msge = err.Error()
			return resp, nil
		}
		for _, each := range transactions {
			resp.Result.List = append(resp.Result.List, &aml_insightv1.GetUserTransactionListResponse_Transaction{
				Data: []string{
					each.RequestId,
					each.TransactionType,
					each.OrderNo,
					fmt.Sprintf("%d", each.MemberId),
					each.FiatCurrencySymbol,
					each.FiatCurrencyAmount.String(),
					each.FiatConvertedUsdAmount.String(),
					each.ChannelType,
					gjson.Get(each.ExtInfo.String, "general_request.srcip").String(),
					gjson.Get(each.ExtInfo.String, "general_request.platform").String(),
					each.CreatedAt.Format("2006-01-02 15:04:05"),
				},
			})
		}

	default:
		// TODO: nothing
	}

	resp.Result.Page = in.Page
	if len(resp.Result.List) > 0 {
		resp.Result.Total = (in.Page + 1) * in.Limit
	}
	return resp, nil
}
