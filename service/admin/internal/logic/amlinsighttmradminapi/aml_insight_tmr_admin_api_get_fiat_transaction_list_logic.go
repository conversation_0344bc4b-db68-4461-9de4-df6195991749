package amlinsighttmradminapilogic

import (
	"context"

	"code.bydev.io/frameworks/byone/core/logc"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightTmrAdminAPIGetFiatTransactionListLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightTmrAdminAPIGetFiatTransactionListLogic(svcCtx *svc.ServiceContext) *AmlInsightTmrAdminAPIGetFiatTransactionListLogic {
	return &AmlInsightTmrAdminAPIGetFiatTransactionListLogic{
		svcCtx: svcCtx,
	}
}

// GetFiatTransactionList Get all fiat transaction
func (l *AmlInsightTmrAdminAPIGetFiatTransactionListLogic) GetFiatTransactionList(ctx context.Context, in *aml_insightv1.GetFiatTransactionListRequest) (*aml_insightv1.GetFiatTransactionListResponse, error) {
	resp := &aml_insightv1.GetFiatTransactionListResponse{
		Result: &aml_insightv1.GetFiatTransactionListResponse_Result{},
	}

	transactions, err := l.svcCtx.FiatAmlTransactionModel.FindListByUidAndRequestIdAndOrderId(ctx, in.MemberId, "", in.RequestId, in.OrderNo, int(in.Limit), int(in.Page))
	if err != nil {
		logc.Errorw(ctx, "get fiat transaction list failed", logc.Field("err", err))
		resp.Code = 400
		resp.Msge = err.Error()
		return resp, nil
	}

	for _, each := range transactions {
		resp.Result.List = append(resp.Result.List, &aml_insightv1.GetFiatTransactionListResponse_FiatTransaction{
			Id:                  each.Id,
			RequestId:           each.RequestId,
			OrderNo:             each.OrderNo,
			MemberId:            int64(each.MemberId),
			TransactionType:     each.TransactionType,
			TransactionCategory: each.TransactionCategory,
			FiatUsdAmount:       each.FiatConvertedUsdAmount.String(),
			Currency:            each.FiatCurrencySymbol,
			CurrencyAmount:      each.FiatCurrencyAmount.String(),
			Crypto:              each.DigitalCurrencySymbol,
			CryptoAmount:        each.DigitalCurrencyAmount.String(),
			ChannelType:         each.ChannelType,
			RnDecision:          each.Decision,
			TransactionTime:     each.AtTime.Unix(),
		})
	}
	resp.Result.Page = in.Page
	if len(transactions) > 0 {
		resp.Result.Total = (in.Page + 1) * in.Limit
	}

	return resp, nil
}
