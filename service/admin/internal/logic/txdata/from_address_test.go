package txdata

//
//func TestClient_GetHackAddresses(t *testing.T) {
//	t.Run("SuccessfulResponse", func(t *testing.T) {
//		sampleResponse := FromAddressResponse{
//			"0221": {
//				ETH: []string{
//					"******************************************",
//					"******************************************",
//				},
//			},
//		}
//
//		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
//			w.Header().Set("Content-Type", "application/json")
//			json.NewEncoder(w).Encode(sampleResponse)
//		}))
//		defer server.Close()
//
//		client := NewClient(Config{
//			HackAddressURI: server.URL,
//		})
//
//		addresses, err := client.GetFromAddresses(context.Background())
//
//		assert.NoError(t, err)
//		assert.Len(t, addresses, 2)
//		assert.Contains(t, addresses, "******************************************")
//		assert.Contains(t, addresses, "******************************************")
//	})
//
//	t.Run("HTTPError", func(t *testing.T) {
//		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
//			w.WriteHeader(http.StatusInternalServerError)
//		}))
//		defer server.Close()
//
//		client := NewClient(Config{
//			HackAddressURI: server.URL,
//		})
//
//		addresses, err := client.GetFromAddresses(context.Background())
//
//		assert.Error(t, err)
//		assert.Nil(t, addresses)
//		assert.Contains(t, err.Error(), "unexpected status code: 500")
//	})
//
//	t.Run("InvalidJSON", func(t *testing.T) {
//		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
//			w.Header().Set("Content-Type", "application/json")
//			w.Write([]byte("invalid json"))
//		}))
//		defer server.Close()
//
//		client := NewClient(Config{
//			HackAddressURI: server.URL,
//		})
//
//		addresses, err := client.GetFromAddresses(context.Background())
//
//		assert.Error(t, err)
//		assert.Nil(t, addresses)
//		assert.Contains(t, err.Error(), "failed to unmarshal response")
//	})
//
//	t.Run("RealEndpoint", func(t *testing.T) {
//		// Skip this test in normal runs to avoid external dependencies
//		// t.Skip("This test makes a real HTTP request to the external endpoint")
//
//		client := NewClient(Config{
//			HackAddressURI: "https://hackscan.hackbounty.io/public/hack-address.json",
//		})
//
//		addresses, err := client.GetFromAddresses(context.Background())
//
//		assert.NoError(t, err)
//		assert.NotEmpty(t, addresses)
//	})
//}
