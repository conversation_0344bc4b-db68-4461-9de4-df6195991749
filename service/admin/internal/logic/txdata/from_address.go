package txdata

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"aml-insight/service/admin/internal/config"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/client/bhttpclient"
	"github.com/go-resty/resty/v2"
)

type Client struct {
	cli *bhttpclient.Client
	cfg config.HackTxConfig
}

type FromAddressResponse map[string]FromAddressData

type FromAddressData struct {
	ETH []string `json:"eth"`
}

func NewClient(c config.HackTxConfig) *Client {
	cli := bhttpclient.New(c.Config)
	cli.SetHeader("Content-Type", "application/json").
		SetHeader("Accept", "application/json").
		SetRetryCount(5).
		SetRetryWaitTime(500 * time.Millisecond).
		SetRetryMaxWaitTime(3 * time.Second).
		AddRetryCondition(func(response *resty.Response, err error) bool {
			if err != nil {
				return true
			}
			if response.StatusCode() != http.StatusOK {
				return true
			}
			return false
		})

	return &Client{
		cli: cli,
		cfg: c,
	}
}

// GetFromAddresses fetches the from addresses from the configured URI
func (c *Client) GetFromAddresses(ctx context.Context) ([]string, error) {
	resp, err := c.cli.R().SetContext(ctx).Get(c.cfg.HackAddressURI)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch from addresses: %w", err)
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode())
	}

	var response FromAddressResponse
	if err := json.Unmarshal(resp.Body(), &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	var addresses []string
	for _, data := range response {
		addresses = append(addresses, data.ETH...)
	}

	return addresses, nil
}
