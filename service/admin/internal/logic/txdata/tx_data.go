package txdata

import (
	"context"
	"fmt"
	"time"

	"code.bydev.io/frameworks/byone/core/logc"

	"aml-insight/pkg/bydata"
	"aml-insight/pkg/common"
)

const (
	tableName        = "ods_security_risk.eth_transaction_v2"
	fromAddressQuery = "select address from ads_fiat_analysis.bybit_exploiter_address where entity_name='Bybit Exploiter' and chain='ETH' and valid=1"
	toAddressQuery   = "select address from ads_fiat_analysis.bybit_exploiter_address where entity_name='THORChain' and chain='ETH' and valid=1"
	dateThreshold    = "2025-02-21"

	queryTimeout = 5 * time.Minute
	pollInterval = 5 * time.Second

	longQueryTimeout = 10 * time.Minute
	longPollInterval = 10 * time.Second
)

type TxDetail struct {
	FromAddr  string `csv:"transaction_from"`
	ToAddr    string `csv:"transaction_to"`
	TxId      string `csv:"transaction_hash"`      // transaction_hash
	Timestamp int64  `csv:"transaction_timestamp"` // transaction_timestamp, 单位: 秒
	Amount    string `csv:"transaction_value"`     // transaction_value 以wei为单位的转账金额
	Symbol    string `csv:"-"`
	State     int    `csv:"status"` // status, 0-失败，1-成功
	TxType    string `csv:"type"`   // type
}

type TxHashGetter interface {
	GetTxHash(ctx context.Context, timestampSec int64) ([]TxDetail, error)
}

type TxDataService struct {
	srv *bydata.ByDataService
	cli *Client // TODO: 移除
}

func NewTxDataService(srv *bydata.ByDataService, cli *Client) *TxDataService {
	return &TxDataService{
		srv: srv,
		cli: cli,
	}
}

func (t *TxDataService) GetTxHash(ctx context.Context, timestampSec int64) ([]*TxDetail, error) {
	return t.GetTxHashWithTimeout(ctx, timestampSec, queryTimeout, pollInterval)
}

// GetTxHashWithTimeout 带超时配置的GetTxHash
func (t *TxDataService) GetTxHashWithTimeout(ctx context.Context, timestampSec int64,
	timeout time.Duration, interval time.Duration) ([]*TxDetail, error) {
	// 查询所有地址的tx
	qSql := fmt.Sprintf(`
		select transaction_from, transaction_to, transaction_hash, 
		       transaction_timestamp, transaction_value, status, type 
		from %s 
		where transaction_from in (%s) 
		  and transaction_to in (%s) 
		  and transaction_timestamp >= %d 
		  and dt >= '%s'`,
		tableName,
		fromAddressQuery,
		toAddressQuery,
		timestampSec,
		dateThreshold)

	if !common.IsQuerySafe(qSql) {
		logc.Errorw(ctx, "it not safe to execute query", logc.Field("query", qSql))
		return nil, fmt.Errorf("it not safe to execute query: %s", qSql)
	}

	return bydata.ExecuteSyncByDataQuery[TxDetail](ctx, t.srv, qSql, timeout, interval)
}

func (t *TxDataService) GetAllTxHashWithTimeout(ctx context.Context,
	queryTimeout time.Duration, interval time.Duration) ([]*TxDetail, error) {
	qSql := fmt.Sprintf(`
		select transaction_from, transaction_to, transaction_hash, 
		       transaction_timestamp, transaction_value, status, type 
		from %s 
		where transaction_from in (%s) 
		  and transaction_to in (%s) 
		  and dt >= '%s'`,
		tableName,
		fromAddressQuery,
		toAddressQuery,
		dateThreshold)

	return bydata.ExecuteSyncByDataQuery[TxDetail](ctx, t.srv, qSql, queryTimeout, interval)
}

func (t *TxDataService) GetAllTxHash(ctx context.Context) ([]*TxDetail, error) {
	return t.GetAllTxHashWithTimeout(ctx, longQueryTimeout, longPollInterval)
}
