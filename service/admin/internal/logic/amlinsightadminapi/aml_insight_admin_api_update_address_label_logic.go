package amlinsightadminapilogic

import (
	"context"
	"errors"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIUpdateAddressLabelLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAdminAPIUpdateAddressLabelLogic(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIUpdateAddressLabelLogic {
	return &AmlInsightAdminAPIUpdateAddressLabelLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAdminAPIUpdateAddressLabelLogic) UpdateAddressLabel(ctx context.Context, in *aml_insightv1.UpdateAddressLabelRequest) (*aml_insightv1.UpdateAddressLabelResponse, error) {

	if len(in.Address) == 0 || len(in.Chain) == 0 {
		return nil, errors.New("address or chain empty")
	}

	err := NewRiskAmlGatewayAddressLabelAdminLogic(l.svcCtx).UpdateByIDs(ctx, in.OperatorName, &AddressLabelUpdateByIDsReq{
		DryRun: 0,
		Force:  0,
		Where: &QueryByIds{
			Address: []string{in.Address},
			Chain:   in.Chain,
		},
		Update: map[string]any{
			"Category":   in.Category,
			"EntityName": in.EntityName,
			"DetailName": in.DetailName,
			"Editor":     in.Editor,
			"Source":     in.Source,
			"Remark":     in.Remark,
			"Valid":      in.Valid,
		},
	})

	if err != nil {
		return nil, err
	}

	return &aml_insightv1.UpdateAddressLabelResponse{
		Code: 0,
		Msg:  "",
	}, nil
}
