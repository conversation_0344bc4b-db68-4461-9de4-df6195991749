package amlinsightadminapilogic

import (
	"aml-insight/service/admin/internal/svc/mock"
	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"context"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"testing"
)

func TestUpsertAmlHitCategory(t *testing.T) {
	ctrl := gomock.NewController(t)
	svc := mock.NewMockServiceContext(ctrl)
	logic := NewAmlInsightAdminAPIUpsertAmlHitCategoryLogic(svc.SvcCtx)

	svc.AmlHitCategoryModel.EXPECT().Update(gomock.Any(), gomock.Any())
	_, err := logic.UpsertAmlHitCategory(context.Background(), &aml_insightv1.UpsertAmlHitCategoryRequest{
		Id:             1,
		Provider:       "Provider",
		Category:       "Category",
		Entity:         "Entity",
		ActionType:     1,
		ExposureType:   "ExposureType",
		RiskLevel:      "RiskLevel",
		RiskLevelScore: 20,
		Status:         1,
		CreatedAt:      "CreatedAt",
		UpdatedAt:      "UpdatedAt",
		CategoryId:     "CategoryId",
		Editor:         "Editor",
		Remark:         "Remark",
	})

	assert.Nil(t, err)
}
