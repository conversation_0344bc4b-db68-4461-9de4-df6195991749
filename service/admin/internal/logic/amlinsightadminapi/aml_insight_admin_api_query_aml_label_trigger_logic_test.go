package amlinsightadminapilogic

import (
	"aml-insight/service/admin/internal/svc/mock"
	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"context"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"testing"
)

func TestQueryAmlLabelTrigger(t *testing.T) {
	ctrl := gomock.NewController(t)
	svc := mock.NewMockServiceContext(ctrl)
	logic := NewAmlInsightAdminAPIQueryAmlLabelTriggerLogic(svc.SvcCtx)

	svc.AmlLabelTriggerModel.EXPECT().QueryByIdOrLabel(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	svc.AmlEmailTriggerModel.EXPECT().QueryByLabels(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	_, err := logic.QueryAmlLabelTrigger(context.Background(), &aml_insightv1.QueryAmlLabelTriggerRequest{
		Id:           1,
		TriggerLabel: "123",
		Page:         1,
		Limit:        10,
	})

	assert.Nil(t, err)
}
