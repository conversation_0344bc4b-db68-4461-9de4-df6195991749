package amlinsightadminapilogic

import (
	"aml-insight/service/admin/internal/svc/mock"
	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"context"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"testing"
)

func TestQueryVendorCategoryMapping(t *testing.T) {
	ctrl := gomock.NewController(t)
	svc := mock.NewMockServiceContext(ctrl)
	logic := NewAmlInsightAdminAPIQueryVendorCategoryMappingLogic(svc.SvcCtx)

	svc.VendorCategoryMappingModel.EXPECT().QueryByVendorCategoryName(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	_, err := logic.QueryVendorCategoryMapping(context.Background(), &aml_insightv1.QueryVendorCategoryMappingRequest{
		Id:     1,
		Vendor: "ch",
		Key:    "123",
		Page:   1,
		Limit:  10,
	})

	assert.Nil(t, err)
}
