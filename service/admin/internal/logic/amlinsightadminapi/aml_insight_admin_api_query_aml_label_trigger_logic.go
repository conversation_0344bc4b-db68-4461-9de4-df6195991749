package amlinsightadminapilogic

import (
	"context"

	"code.bydev.io/frameworks/byone/core/logc"

	"aml-insight/internal/model"
	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIQueryAmlLabelTriggerLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAdminAPIQueryAmlLabelTriggerLogic(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIQueryAmlLabelTriggerLogic {
	return &AmlInsightAdminAPIQueryAmlLabelTriggerLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAdminAPIQueryAmlLabelTriggerLogic) QueryAmlLabelTrigger(ctx context.Context, in *aml_insightv1.QueryAmlLabelTriggerRequest) (*aml_insightv1.QueryAmlLabelTriggerResponse, error) {
	var (
		resp = &aml_insightv1.QueryAmlLabelTriggerResponse{
			Code: 0,
			Msg:  "",
			Result: &aml_insightv1.QueryAmlLabelTriggerResponse_Result{
				List:  make([]*aml_insightv1.UpsertAmlLabelTriggerRequest, 0),
				Page:  in.GetPage(),
				Total: 1000,
			},
		}
	)

	amlLabelTriggers, err := l.svcCtx.AmlLabelTriggerModel.QueryByIdOrLabel(ctx, in.GetId(), in.GetTriggerLabel(), in.GetLimit(), in.GetPage())
	if err != nil {
		return nil, err
	}

	labels := make([]string, 0)

	for _, amlLabelTrigger := range amlLabelTriggers {
		labels = append(labels, amlLabelTrigger.TriggerLabel)
		respAmlLabelTrigger := &aml_insightv1.UpsertAmlLabelTriggerRequest{
			Id:                                  amlLabelTrigger.Id,
			TriggerLabel:                        amlLabelTrigger.TriggerLabel,
			RiskLevel:                           amlLabelTrigger.RiskLevel,
			ExposureType:                        amlLabelTrigger.ExposureType,
			ManuallyAuto:                        amlLabelTrigger.ManuallyAuto,
			AssetIsBf:                           amlLabelTrigger.AssetIsBf,
			NeedEdd:                             amlLabelTrigger.NeedEdd,
			SuggestedAc:                         amlLabelTrigger.SuggestedAc,
			SuggestedQoa:                        amlLabelTrigger.SuggestedQoa,
			SuggestedNoa:                        amlLabelTrigger.SuggestedNoa,
			SuggestedAtb:                        amlLabelTrigger.SuggestedAtb,
			SuggestedAtb2:                       amlLabelTrigger.SuggestedAtb2,
			SuggestedAwb:                        amlLabelTrigger.SuggestedAwb,
			SuggestedStr:                        amlLabelTrigger.SuggestedStr,
			VendorSource:                        amlLabelTrigger.VendorSource,
			TriggerRiskValue:                    amlLabelTrigger.TriggerRiskValue,
			TriggerRiskUsdValue:                 amlLabelTrigger.TriggerRiskUsdValue,
			TriggerRiskRatio:                    amlLabelTrigger.TriggerRiskRatio,
			TriggerRiskEntityList:               amlLabelTrigger.TriggerRiskEntityList,
			TriggerThisCategoryUsdOneMonth:      amlLabelTrigger.TriggerThisCategoryUsdOneMonth,
			TriggerThisCategoryUsdLifetime:      amlLabelTrigger.TriggerThisCategoryUsdLifetime,
			Status:                              amlLabelTrigger.Status,
			TriggerThisCategoryWithdrawOneMonth: amlLabelTrigger.TriggerThisCategoryWithdrawOneMonth,
			Editor:                              amlLabelTrigger.Editor,
			Remark:                              amlLabelTrigger.Remark,
			CreatedAt:                           amlLabelTrigger.CreatedAt.String(),
			UpdatedAt:                           amlLabelTrigger.UpdatedAt.String(),
			//BrokerEmailTemplate:   "",
			//NoBrokerEmailTemplate: "",
		}
		resp.Result.List = append(resp.Result.List, respAmlLabelTrigger)
	}

	amlEmailTriggers, err := l.svcCtx.AmlEmailTriggerModel.QueryByLabels(ctx, labels, 10000, 1)
	if err != nil {
		logc.Errorw(ctx, "query email template failed", logc.Field("err", err))
		return resp, nil
	}

	brokerSubAccountMap := make(map[string]*model.AmlEmailTrigger)
	noBrokerSubAccountMap := make(map[string]*model.AmlEmailTrigger)
	for _, trigger := range amlEmailTriggers {

		if trigger.IsBrokerSubAccount == "yes" {
			brokerSubAccountMap[trigger.TriggerLabel] = trigger
		}
		if trigger.IsBrokerSubAccount == "no" {
			noBrokerSubAccountMap[trigger.TriggerLabel] = trigger
		}
	}

	for _, trigger := range resp.Result.List {
		if triggerEmail, ok := brokerSubAccountMap[trigger.TriggerLabel]; ok {
			trigger.BrokerEmailTemplate = triggerEmail.FirstEmailTemplate
		}
		if triggerEmail, ok := noBrokerSubAccountMap[trigger.TriggerLabel]; ok {
			trigger.NoBrokerEmailTemplate = triggerEmail.FirstEmailTemplate
		}
	}

	return resp, nil
}
