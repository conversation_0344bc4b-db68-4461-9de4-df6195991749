package amlinsightadminapilogic

import (
	"aml-insight/internal/model"
	"aml-insight/service/admin/internal/svc"
	"context"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIUpsertAmlHitCategoryLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAdminAPIUpsertAmlHitCategoryLogic(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIUpsertAmlHitCategoryLogic {
	return &AmlInsightAdminAPIUpsertAmlHitCategoryLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAdminAPIUpsertAmlHitCategoryLogic) UpsertAmlHitCategory(ctx context.Context, in *aml_insightv1.UpsertAmlHitCategoryRequest) (*aml_insightv1.UpsertAmlHitCategoryResponse, error) {

	resp := &aml_insightv1.UpsertAmlHitCategoryResponse{
		Code: 0,
		Msg:  "",
	}
	var err error
	amlHitCategory := &model.AmlHitCategory{
		Id:             in.GetId(),
		Provider:       in.GetProvider(),
		Category:       in.GetCategory(),
		Entity:         in.GetEntity(),
		ActionType:     in.GetActionType(),
		ExposureType:   in.GetExposureType(),
		RiskLevel:      in.GetRiskLevel(),
		RiskLevelScore: in.GetRiskLevelScore(),
		Status:         in.GetStatus(),
		CategoryId:     in.GetCategoryId(),
		Editor:         in.GetEditor(),
		Remark:         in.GetRemark(),
	}
	if amlHitCategory.Id == 0 {
		_, err = l.svcCtx.AmlHitCategoryModel.Insert(ctx, amlHitCategory)
	} else {
		err = l.svcCtx.AmlHitCategoryModel.Update(ctx, amlHitCategory)
	}

	if err != nil {
		resp.Code = 500
		resp.Msg = err.Error()
		return resp, nil
	}

	return resp, nil
}
