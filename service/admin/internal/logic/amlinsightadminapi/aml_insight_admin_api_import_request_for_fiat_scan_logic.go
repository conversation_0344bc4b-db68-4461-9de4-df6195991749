package amlinsightadminapilogic

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/runutil"
	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/kafka"
	"github.com/360EntSecGroup-Skylar/excelize"

	kafkatool "aml-insight/internal/lib/kafka"
	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIImportRequestForFiatScanLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAdminAPIImportRequestForFiatScanLogic(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIImportRequestForFiatScanLogic {
	return &AmlInsightAdminAPIImportRequestForFiatScanLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAdminAPIImportRequestForFiatScanLogic) ImportRequestForFiatScan(ctx context.Context, in *aml_insightv1.ImportRequestForFiatScanRequest) (*aml_insightv1.ImportRequestForFiatScanResponse, error) {
	var (
		resp = &aml_insightv1.ImportRequestForFiatScanResponse{}
	)
	logc.Warnw(ctx, "import request for fiat scan", logc.Field("operator_name", in.OperatorName))
	data, err := base64.StdEncoding.DecodeString(in.GetData())
	if err != nil {
		resp.Code = 300100000 // global.SecurityDefault.Int64()
		resp.Msg = err.Error()
		return resp, nil
	}

	f, err := excelize.OpenReader(bytes.NewReader(data))
	if err != nil {
		logc.Warnw(ctx, "open excel failed", logc.Field("err", err))
		return nil, fmt.Errorf("open file err: %s", err.Error())
	}

	runutil.RunInBackground(ctx, func(ctx context.Context) {
		stat, err := l.ImportRequests(ctx, in.OperatorName, f)
		if err != nil {
			logc.Warnw(ctx, "import request for fiat scan record error", logc.Field("err", err))
		}
		if stat == nil {
			logc.Warnw(ctx, "import request for fiat scan stat empty", logc.Field("err", err))
			return
		}
		logc.Infow(ctx, "import request for fiat scan stat", logc.Field("stat", stat))
	})

	return resp, nil
}

func (l *AmlInsightAdminAPIImportRequestForFiatScanLogic) ImportRequests(ctx context.Context, operationUser string, f *excelize.File) (*ImportStat, error) {
	var (
		stat = &ImportStat{}
	)

	for _, sheet := range f.GetSheetMap() {
		for index, row := range f.GetRows(sheet) {
			if len(row) < 2 {
				return nil, fmt.Errorf("imvalid row, row index: %d", index)
			}
			if index == 0 {
				if row[0] != "topic" || row[1] != "msg" {
					return nil, errors.New("invalid excel sheet")
				} else {
					logc.Infow(ctx, "skip row", logc.Field("row", row))
					continue
				}
			}

			topic := row[0]
			msg := row[1]
			if len(topic) == 0 || len(msg) == 0 {
				logc.Warnw(ctx, "invalid msg data", logc.Field("row", row))
				continue
			}
			stat.Total++
			err := l.svcCtx.FiatProdProducer.Send(ctx, &kafka.Message{
				Topic: topic,
				Value: []byte(msg),
			})
			if err != nil {
				logc.Errorw(ctx, "DoSendMsgToTopic failed", logc.Field("err", kafkatool.ExtractPErr(err)),
					logc.Field("msg", msg), logc.Field("topic", topic))
				continue
			}
			logc.Infow(ctx, "DoSendMsgToTopic success", logc.Field("topic", topic), logc.Field("msg", msg))
			stat.Success++
		}
	}
	return stat, nil
}
