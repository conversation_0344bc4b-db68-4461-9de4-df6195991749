package amlinsightadminapilogic

import (
	"context"
	"strings"
	"testing"

	"aml-insight/internal/model"
	"aml-insight/service/admin/internal/svc/mock"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestUpdateByIDs(t *testing.T) {
	ctrl := gomock.NewController(t)
	svc := mock.NewMockServiceContext(ctrl)
	logic := NewRiskAmlGatewayAddressLabelAdminLogic(svc.SvcCtx)

	// 测试用例：空条件
	t.Run("空条件", func(t *testing.T) {
		req := &AddressLabelUpdateByIDsReq{
			DryRun: 0,
			Force:  0,
			Where:  &QueryByIds{},
			Update: map[string]any{"Category": "newCategory"},
		}
		err := logic.UpdateByIDs(context.Background(), "testUser", req)
		if err == nil || !strings.Contains(err.Error(), "invalid query") {
			t.<PERSON>("expected error containing 'invalid query', got %v", err)
		}
	})

	// 测试用例：空更新字段
	t.Run("空更新字段", func(t *testing.T) {
		// Setup mocks to pass validation
		chain := "chain1"
		addresses := []string{"address1", "address2"}

		svc.AllAddressLabelModel.EXPECT().
			QueryByAddressAndChain(gomock.Any(), chain, addresses, gomock.Any(), gomock.Any()).
			Return([]*model.AllAddressLabel{}, nil).
			Times(1)

		svc.AddressLabelModel.EXPECT().
			QueryIdIn(gomock.Any(), gomock.Any(), chain, addresses, gomock.Any(), gomock.Any()).
			Return([]*model.AddressLabel{
				{Id: 1, Chain: chain, Address: addresses[0]},
				{Id: 2, Chain: chain, Address: addresses[1]},
			}, nil).
			Times(1)

		// Create the request
		req := &AddressLabelUpdateByIDsReq{
			DryRun: 0,
			Force:  0,
			Where: &QueryByIds{
				Ids:     []uint64{1, 2},
				Address: addresses,
				Chain:   chain,
			},
			Update: map[string]any{},
		}

		// Test the function
		err := logic.UpdateByIDs(context.Background(), "testUser", req)
		if err == nil || !strings.Contains(err.Error(), "invalid update") {
			t.Errorf("expected error containing 'invalid update', got %v", err)
		}
	})

	//// 测试用例：影响行数过多
	//t.Run("影响行数过多", func(t *testing.T) {
	//	svc.AddressLabelModel.EXPECT().
	//		QueryIdIn(gomock.Any(), []uint64{1, 2, 3}, "chain1", []string{"address1", "address2"}, getLimit(0), 0).
	//		Return([]*model.AddressLabel{
	//			{Id: 1, Chain: "chain1", Address: "address1"},
	//			{Id: 2, Chain: "chain1", Address: "address2"},
	//			{Id: 3, Chain: "chain1", Address: "address3"},
	//		}, nil).
	//		Times(1)
	//
	//	req := &AddressLabelUpdateByIDsReq{
	//		DryRun: 0,
	//		Force:  0,
	//		Where: &QueryByIds{
	//			Ids:     []uint64{1, 2, 3},
	//			Address: []string{"address1", "address2"},
	//			Chain:   "chain1",
	//		},
	//		Update: map[string]any{"Category": "newCategory"},
	//	}
	//
	//	err := logic.UpdateByIDs(context.Background(), "testUser", req)
	//	if err == nil || err.Error() != "affects too many rows" {
	//		t.Errorf("expected error 'affects too many rows', got %v", err)
	//	}
	//})
}

func TestBatchInsert(t *testing.T) {
	ctrl := gomock.NewController(t)
	svc := mock.NewMockServiceContext(ctrl)
	logic := NewRiskAmlGatewayAddressLabelAdminLogic(svc.SvcCtx)

	// 测试用例：空行
	t.Run("空行", func(t *testing.T) {
		req := &BatchInsertReq{
			Force: 0,
			Rows:  []*model.AddressLabel{},
		}
		_, err := logic.BatchInsert(context.Background(), "testUser", req)
		if err == nil || err.Error() != "empty rows" {
			t.Errorf("expected error 'empty rows', got %v", err)
		}
	})

	// 测试用例：重复地址
	t.Run("重复地址", func(t *testing.T) {
		svc.AllAddressLabelModel.EXPECT().
			Upsert(gomock.Any(), gomock.Any()).
			Return(int64(1), nil).
			AnyTimes()

		req := &BatchInsertReq{
			Force: 0,
			Rows: []*model.AddressLabel{
				{Chain: "chain1", Address: "address1"},
				{Chain: "chain1", Address: "address1"}, // 重复地址
			},
		}
		count, err := logic.BatchInsert(context.Background(), "testUser", req)
		assert.Nil(t, err)
		assert.Equal(t, 2, count)
	})
}

func TestQueryByIDs(t *testing.T) {
	ctrl := gomock.NewController(t)
	svc := mock.NewMockServiceContext(ctrl)
	logic := NewRiskAmlGatewayAddressLabelAdminLogic(svc.SvcCtx)

	// 测试用例：正常查询
	t.Run("正常查询", func(t *testing.T) {
		svc.AddressLabelModel.EXPECT().
			QueryIdIn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]*model.AddressLabel{
				{Id: 1, Chain: "chain1", Address: "address1"},
				{Id: 2, Chain: "chain1", Address: "address2"},
			}, nil).
			Times(1)

		req := &QueryByIdsReq{
			Force: 0,
			Where: &QueryByIds{
				Ids:     []uint64{1, 2},
				Address: []string{"address1", "address2"},
				Chain:   "chain1",
			},
		}

		result, err := logic.QueryByIDs(context.Background(), req)
		if err != nil {
			t.Errorf("expected no error, got %v", err)
		}
		if len(result) != 2 {
			t.Errorf("expected 2 results, got %d", len(result))
		}
	})

	// 测试用例：未找到结果
	t.Run("未找到结果", func(t *testing.T) {
		svc.AddressLabelModel.EXPECT().
			QueryIdIn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]*model.AddressLabel{}, nil).
			Times(1)

		req := &QueryByIdsReq{
			Force: 0,
			Where: &QueryByIds{
				Ids:     []uint64{1, 2},
				Address: []string{"address1", "address2"},
				Chain:   "chain1",
			},
		}

		result, err := logic.QueryByIDs(context.Background(), req)
		if err != nil {
			t.Errorf("expected no error, got %v", err)
		}
		if len(result) != 0 {
			t.Errorf("expected 0 results, got %d", len(result))
		}
	})
}

func TestAdminLogicQueryAddressLabel(t *testing.T) {
	t.Run("normal", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		svc := mock.NewMockServiceContext(ctrl)
		logic := NewRiskAmlGatewayAddressLabelAdminLogic(svc.SvcCtx)

		svc.AddressLabelModel.EXPECT().
			QueryIdIn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]*model.AddressLabel{
				{Id: 1, Chain: "chain1", Address: "address1"},
				{Id: 2, Chain: "chain1", Address: "address2"},
			}, nil).
			Times(1)

		req := &QueryByIdsReq{
			Force: 0,
			Where: &QueryByIds{
				Ids:     []uint64{1, 2},
				Address: []string{"address1", "address2"},
				Chain:   "chain1",
			},
		}
		result, err := logic.QueryByIDs(context.Background(), req)

		assert.Nil(t, err)
		assert.Len(t, result, 2)
	})

	t.Run("query_address_label", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		svc := mock.NewMockServiceContext(ctrl)
		logic := NewAmlInsightAdminAPIQueryAddressLabelLogic(svc.SvcCtx)

		svc.AddressLabelModel.EXPECT().
			QueryIdIn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]*model.AddressLabel{
				{Id: 1, Chain: "chain1", Address: "address1"},
				{Id: 2, Chain: "chain1", Address: "address2"},
			}, nil).
			Times(1)
		req := &aml_insightv1.QueryAddressLabelRequest{
			Address: "address1",
			Chain:   "chain1",
		}
		result, err := logic.QueryAddressLabel(context.Background(), req)
		assert.Nil(t, err)
		assert.Len(t, result.GetResult().List, 2)
	})

}

func TestAdminLogicUpdateAddressLabel(t *testing.T) {
	t.Run("normal", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		svc := mock.NewMockServiceContext(ctrl)
		logic := NewRiskAmlGatewayAddressLabelAdminLogic(svc.SvcCtx)

		chain := "chain1"
		addresses := []string{"address1", "address2"}

		req := &AddressLabelUpdateByIDsReq{
			DryRun: 0,
			Force:  0,
			Where: &QueryByIds{
				Ids:     []uint64{1, 2},
				Address: addresses,
				Chain:   chain,
			},
			Update: map[string]any{
				"": "",
			},
		}

		svc.AllAddressLabelModel.EXPECT().
			QueryByAddressAndChain(gomock.Any(), chain, addresses, gomock.Any(), gomock.Any()).
			Return([]*model.AllAddressLabel{}, nil).
			Times(1)

		svc.AddressLabelModel.EXPECT().QueryIdIn(gomock.Any(), gomock.Any(), chain, addresses, gomock.Any(), gomock.Any()).Return([]*model.AddressLabel{}, nil).Times(1)
		svc.AddressLabelChangeLogModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(model.NewMockedResult(1, 1), nil).Times(1)
		svc.AddressLabelHistoryModel.EXPECT().BatchInsert(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		svc.AddressLabelModel.EXPECT().BatchUpdate(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		svc.AddressLabelChangeLogModel.EXPECT().UpdateChangeLogStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)

		svc.AllAddressLabelModel.EXPECT().
			Upsert(gomock.Any(), gomock.Any()).
			Return(int64(0), nil).
			AnyTimes()

		err := logic.UpdateByIDs(context.Background(), "testUser", req)
		assert.Nil(t, err)
	})

	t.Run("update_multiple_fields", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		svc := mock.NewMockServiceContext(ctrl)
		logic := NewRiskAmlGatewayAddressLabelAdminLogic(svc.SvcCtx)

		chain := "chain1"
		addresses := []string{"address1", "address2"}

		req := &AddressLabelUpdateByIDsReq{
			DryRun: 0,
			Force:  0,
			Where: &QueryByIds{
				Ids:     []uint64{1, 2},
				Address: addresses,
				Chain:   chain,
			},
			Update: map[string]any{
				"Category":   "Category",
				"EntityName": "EntityName",
				"DetailName": "DetailName",
				"Editor":     "Editor",
				"Source":     "Source",
				"Remark":     "Remark",
				"Valid":      1,
			},
		}

		svc.AllAddressLabelModel.EXPECT().
			QueryByAddressAndChain(gomock.Any(), chain, addresses, gomock.Any(), gomock.Any()).
			Return([]*model.AllAddressLabel{}, nil).
			Times(1)

		svc.AddressLabelModel.EXPECT().QueryIdIn(gomock.Any(), gomock.Any(), chain, addresses, gomock.Any(), gomock.Any()).Return([]*model.AddressLabel{}, nil).Times(1)
		svc.AddressLabelChangeLogModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(model.NewMockedResult(1, 1), nil).Times(1)
		svc.AddressLabelHistoryModel.EXPECT().BatchInsert(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		svc.AddressLabelModel.EXPECT().BatchUpdate(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		svc.AddressLabelChangeLogModel.EXPECT().UpdateChangeLogStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)

		svc.AllAddressLabelModel.EXPECT().
			Upsert(gomock.Any(), gomock.Any()).
			Return(int64(0), nil).
			AnyTimes()

		err := logic.UpdateByIDs(context.Background(), "testUser", req)
		assert.Nil(t, err)
	})

}

func TestAdminLogicImportAddressLabel(t *testing.T) {
	t.Run("normal", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		svc := mock.NewMockServiceContext(ctrl)
		logic := NewRiskAmlGatewayAddressLabelAdminLogic(svc.SvcCtx)

		req := &BatchInsertReq{
			Force: 0,
			Rows: []*model.AddressLabel{
				{Chain: "chain1", Address: "address2"},
				{Chain: "chain1", Address: "address1"}, // 重复地址
			},
		}

		svc.AllAddressLabelModel.EXPECT().
			Upsert(gomock.Any(), gomock.Any()).
			Return(int64(1), nil).
			AnyTimes()

		svc.AddressLabelModel.EXPECT().QueryIdIn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
		svc.AddressLabelChangeLogModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(model.NewMockedResult(2, 2), nil).Times(1)
		svc.AddressLabelHistoryModel.EXPECT().BatchInsert(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		svc.AddressLabelModel.EXPECT().InsertOrUpdate(gomock.Any(), gomock.Any()).Return(nil).Times(len(req.Rows))
		svc.AddressLabelChangeLogModel.EXPECT().UpdateChangeLogStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)

		_, err := logic.BatchInsert(context.Background(), "testUser", req)
		assert.Nil(t, err)
	})

}

func TestAddressLabelUpdateByIDsReq_IsValidQuery(t *testing.T) {
	tests := []struct {
		name string
		req  AddressLabelUpdateByIDsReq
		want bool
	}{
		{
			name: "nil Where",
			req:  AddressLabelUpdateByIDsReq{Where: nil},
			want: false,
		},
		{
			name: "empty Ids, empty Chain",
			req: AddressLabelUpdateByIDsReq{
				Where: &QueryByIds{
					Ids:     []uint64{},
					Chain:   "",
					Address: []string{"addr1"},
				},
			},
			want: false,
		},
		{
			name: "empty Ids, empty Address",
			req: AddressLabelUpdateByIDsReq{
				Where: &QueryByIds{
					Ids:     []uint64{},
					Chain:   "chain1",
					Address: []string{},
				},
			},
			want: false,
		},
		{
			name: "empty Ids, non-empty Chain and Address",
			req: AddressLabelUpdateByIDsReq{
				Where: &QueryByIds{
					Ids:     []uint64{},
					Chain:   "chain1",
					Address: []string{"addr1"},
				},
			},
			want: true,
		},
		{
			name: "non-empty Ids, empty Chain and Address",
			req: AddressLabelUpdateByIDsReq{
				Where: &QueryByIds{
					Ids:     []uint64{1},
					Chain:   "",
					Address: []string{},
				},
			},
			want: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.req.IsValidQuery(); got != tt.want {
				t.Errorf("AddressLabelUpdateByIDsReq.IsValidQuery() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAddressLabelUpdateByIDsReq_IsValidUpdate(t *testing.T) {
	tests := []struct {
		name string
		req  AddressLabelUpdateByIDsReq
		want bool
	}{
		{
			name: "empty Update map",
			req:  AddressLabelUpdateByIDsReq{Update: map[string]any{}},
			want: false,
		},
		{
			name: "Update with Chain key",
			req: AddressLabelUpdateByIDsReq{
				Update: map[string]any{
					"Chain": "chain1",
				},
			},
			want: false,
		},
		{
			name: "Update with Address key",
			req: AddressLabelUpdateByIDsReq{
				Update: map[string]any{
					"Address": "addr1",
				},
			},
			want: false,
		},
		{
			name: "Update with both Chain and Address keys",
			req: AddressLabelUpdateByIDsReq{
				Update: map[string]any{
					"Chain":   "chain1",
					"Address": "addr1",
				},
			},
			want: false,
		},
		{
			name: "Valid Update with other keys",
			req: AddressLabelUpdateByIDsReq{
				Update: map[string]any{
					"Category":   "category1",
					"EntityName": "entity1",
				},
			},
			want: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.req.IsValidUpdate(); got != tt.want {
				t.Errorf("AddressLabelUpdateByIDsReq.IsValidUpdate() = %v, want %v", got, tt.want)
			}
		})
	}
}
