package amlinsightadminapilogic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"time"

	"code.bydev.io/frameworks/byone/core/logc"

	"aml-insight/internal/model"
	"aml-insight/internal/service"
	"aml-insight/service/admin/internal/svc"
)

const (
	defaultLimit = 1000
	forceLimit   = 10000
)

type RiskAmlGatewayAddressLabelAdminLogic struct {
	svcCtx                     *svc.ServiceContext
	AddressLabelModel          model.AddressLabelModel    // blacklist
	AllAddressLabelModel       model.AllAddressLabelModel // whitelist + blacklist
	AddressLabelHistoryModel   model.AddressLabelHistoryModel
	AddressLabelChangeLogModel model.AddressLabelChangeLogModel
	transformService           *service.EntityMappingTransformService
}

type AddressLabelUpdateByIDsReq struct {
	DryRun int            `json:"DryRun"`
	Force  int            `json:"Force"`
	Where  *QueryByIds    `json:"Where"`
	Update map[string]any `json:"Update"`
}

func (req *AddressLabelUpdateByIDsReq) IsValidQuery() bool {
	if req.Where == nil {
		return false
	}
	if len(req.Where.Ids) == 0 && (req.Where.Chain == "" || len(req.Where.Address) == 0) {
		return false
	}
	return true
}

func (req *AddressLabelUpdateByIDsReq) IsValidUpdate() bool {
	if len(req.Update) == 0 {
		return false
	}
	if _, ok := req.Update["Chain"]; ok {
		return false
	}
	if _, ok := req.Update["Address"]; ok {
		return false
	}

	return true
}

type BatchInsertReq struct {
	Force int                   `json:"Force"`
	Rows  []*model.AddressLabel `json:"Rows"`
}

type QueryByIdsReq struct {
	Force int         `json:"Force"`
	Where *QueryByIds `json:"Where"`
	Limit int32       `json:"Limit"`
	Page  int32       `json:"Page"`
}

type QueryByIds struct {
	Ids     []uint64 `json:"Ids"`
	Address []string `json:"Address"`
	Chain   string   `json:"Chain"`
}

func NewRiskAmlGatewayAddressLabelAdminLogic(svcCtx *svc.ServiceContext) *RiskAmlGatewayAddressLabelAdminLogic {
	return &RiskAmlGatewayAddressLabelAdminLogic{
		svcCtx:                     svcCtx,
		AddressLabelModel:          svcCtx.AddressLabelModel,
		AllAddressLabelModel:       svcCtx.AllAddressLabelModel,
		AddressLabelHistoryModel:   svcCtx.AddressLabelHistoryModel,
		AddressLabelChangeLogModel: svcCtx.AddressLabelChangeLogModel,
	}
}

func (a *RiskAmlGatewayAddressLabelAdminLogic) UpdateByIDs(ctx context.Context, operationUser string, req *AddressLabelUpdateByIDsReq) error {
	if !req.IsValidQuery() {
		return fmt.Errorf("invalid query: %+v", req)
	}

	// TODO: 观察一段时间删除
	if !a.svcCtx.Config.AddressLabelDBSwitch.DisableWriteMysql {
		a.updateByIDsInMysql(ctx, operationUser, req)
	}

	// TIDB 查询
	rows, err := a.AllAddressLabelModel.QueryByAddressAndChain(ctx, req.Where.Chain, req.Where.Address, getLimit(req.Force), 1)
	if err != nil {
		logc.Warnw(ctx, "failed to query all address label in tidb", logc.Field("error", err), logc.Field("req", req))
	}

	if req.DryRun == 1 {
		return nil
	}

	_, err = a.updateAllAddressLabel(ctx, operationUser, rows, req)
	if err != nil {
		logc.Warnw(ctx, "failed to update all address label", logc.Field("error", err), logc.Field("req", req))
	}
	return err
}

// TODO: 观察一段时间后移除，保证tidb/mysql 都有黑地址全量数据
func (a *RiskAmlGatewayAddressLabelAdminLogic) updateByIDsInMysql(ctx context.Context, operationUser string, req *AddressLabelUpdateByIDsReq) {
	rows, err := a.AddressLabelModel.QueryIdIn(ctx, req.Where.Ids, req.Where.Chain, req.Where.Address, getLimit(req.Force), 1)
	if err != nil {
		logc.Warnw(ctx, "failed to query address label in mysql", logc.Field("error", err), logc.Field("req", req))
		return
	}
	if int32(len(rows)) >= getLimit(req.Force) {
		logc.Warnw(ctx, "affects too many rows", logc.Field("req", req), logc.Field("size", len(rows)))
		return
	}

	if req.DryRun == 1 {
		return
	}

	_, err = a.update(ctx, operationUser, rows, req)
	if err != nil {
		logc.Warnw(ctx, "failed to update address label in mysql", logc.Field("error", err), logc.Field("req", req))
	}
}

func (a *RiskAmlGatewayAddressLabelAdminLogic) update(ctx context.Context, operationUser string,
	rows []*model.AddressLabel, req *AddressLabelUpdateByIDsReq) ([]*model.AddressLabel, error) {
	if !req.IsValidUpdate() {
		return nil, fmt.Errorf("invalid update: %+v", req)
	}

	conditions := req.Update
	insertResult, err := a.AddressLabelChangeLogModel.Insert(ctx, &model.AddressLabelChangeLog{
		OperationUser: operationUser,
		OperationType: model.OperationTypeUpdate,
		AffectedRows:  int32(len(rows)),
		Status:        model.StatusFailed,
	})

	if err != nil {
		return nil, err
	}
	operationLogId, err := insertResult.LastInsertId()
	if err != nil {
		return nil, err
	}

	addressLabelHistoryList := make([]*model.AddressLabelHistory, 0)
	updateFields := 0

	for _, row := range rows {
		oldData := &model.AddressLabel{
			Id:         row.Id,
			Chain:      row.Chain,
			Address:    row.Address,
			Category:   row.Category,
			EntityName: row.EntityName,
			DetailName: row.DetailName,
			Editor:     row.Editor,
			Source:     row.Source,
			Remark:     row.Remark,
			Valid:      row.Valid,
			CreatedAt:  row.CreatedAt,
			UpdatedAt:  row.UpdatedAt,
		}

		if category, ok := conditions["Category"]; ok {
			if v, ok := category.(string); ok {
				row.Category = v
				updateFields++
			} else {
				return nil, fmt.Errorf("invalid type for Category: expected string")
			}
		}
		if entityName, ok := conditions["EntityName"]; ok {
			if v, ok := entityName.(string); ok {
				row.EntityName = v
				updateFields++
			} else {
				return nil, fmt.Errorf("invalid type for EntityName: expected string")
			}
		}
		if detailName, ok := conditions["DetailName"]; ok {
			if v, ok := detailName.(string); ok {
				row.DetailName = v
				updateFields++
			} else {
				return nil, fmt.Errorf("invalid type for DetailName: expected string")
			}
		}
		if editor, ok := conditions["Editor"]; ok {
			if v, ok := editor.(string); ok {
				row.Editor = v
				updateFields++
			} else {
				return nil, fmt.Errorf("invalid type for Editor: expected string")
			}
		}
		if source, ok := conditions["Source"]; ok {
			if v, ok := source.(string); ok {
				row.Source = v
				updateFields++
			} else {
				return nil, fmt.Errorf("invalid type for Source: expected string")
			}
		}
		if source, ok := conditions["Remark"]; ok {
			if v, ok := source.(string); ok {
				row.Remark = v
				updateFields++
			} else {
				return nil, fmt.Errorf("invalid type for Remark: expected string")
			}
		}
		if valid, ok := conditions["Valid"]; ok {
			if v, ok := valid.(int32); ok {
				row.Valid = v
				updateFields++
			} else {
				if v, ok := valid.(int); ok {
					row.Valid = int32(v)
				} else {
					return nil, fmt.Errorf("invalid type for Valid: expected int, type of: %v", reflect.TypeOf(valid))
				}
			}
		}
		if updateFields == 0 {
			return nil, errors.New("empty update conditions")
		}
		row.Editor = operationUser
		row.UpdatedAt = time.Now()

		oldDataStr, err := json.Marshal(oldData)
		if err != nil {
			return nil, err
		}
		newDataStr, err := json.Marshal(row)
		if err != nil {
			return nil, err
		}

		addressLabelHistoryList = append(addressLabelHistoryList, &model.AddressLabelHistory{
			OperationLogId: uint64(operationLogId),
			AddressLabelId: row.Id,
			Chain:          row.Chain,
			Address:        row.Address,
			PreviousState:  model.StringToNullString(string(oldDataStr)),
			CurrentState:   model.StringToNullString(string(newDataStr)),
			OperationType:  model.OperationTypeUpdate,
		})
	}

	if err = a.AddressLabelHistoryModel.BatchInsert(ctx, addressLabelHistoryList); err != nil {
		return nil, err
	}
	err = a.AddressLabelModel.BatchUpdate(ctx, rows)
	if err != nil {
		return nil, err
	}
	err = a.AddressLabelChangeLogModel.UpdateChangeLogStatus(ctx, uint64(operationLogId), model.StatusSuccess)
	if err != nil {
		return nil, err
	}
	return nil, nil

}

func (l *RiskAmlGatewayAddressLabelAdminLogic) updateAllAddressLabel(ctx context.Context, operationUser string,
	rows []*model.AllAddressLabel, req *AddressLabelUpdateByIDsReq) ([]*model.AddressLabel, error) {
	if !req.IsValidUpdate() {
		return nil, fmt.Errorf("invalid update: %+v", req)
	}

	for _, row := range rows {
		row.Editor = operationUser
		row.UpdatedAt = time.Now()
		_, err := l.upsert(ctx, row)
		if err != nil {
			return nil, err
		}
	}
	return nil, nil
}

func (a *RiskAmlGatewayAddressLabelAdminLogic) BatchInsert(ctx context.Context, operationUser string, req *BatchInsertReq) (int, error) {
	if len(req.Rows) == 0 {
		return 0, errors.New("empty rows")
	}

	// TODO: 观察一段时间后移除，保证tidb/mysql 都有黑地址全量数据
	if !a.svcCtx.Config.AddressLabelDBSwitch.DisableWriteMysql {
		_, err := a.upsertAddressLabel(ctx, operationUser, req)
		if err != nil {
			logc.Warnw(ctx, "failed to upsert all address label in mysql", logc.Field("error", err), logc.Field("req", req))
		}
	}

	return a.upsertAllAddressLabel(ctx, operationUser, req)
}

func (a *RiskAmlGatewayAddressLabelAdminLogic) upsertAddressLabel(ctx context.Context, operationUser string,
	req *BatchInsertReq) (int, error) {
	// key: chain - address
	chainAddressModelMap := make(map[string]map[string]*model.AddressLabel)
	chainAddressList := make(map[string][]string)
	for _, row := range req.Rows {
		if len(row.Chain) == 0 || len(row.Address) == 0 {
			return 0, errors.New("empty chain address")
		}

		if _, ok := chainAddressModelMap[row.Chain]; !ok {
			chainAddressModelMap[row.Chain] = make(map[string]*model.AddressLabel)
		}
		if _, ok := chainAddressModelMap[row.Chain][row.Address]; !ok {
			chainAddressModelMap[row.Chain][row.Address] = row
			chainAddressList[row.Chain] = append(chainAddressList[row.Chain], row.Address)
		} else {
			return 0, fmt.Errorf("duplicate chain address: %s,%s", row.Chain, row.Address)
		}
	}

	existAddressLabelList := make([]*model.AddressLabel, 0)
	for chain, addresses := range chainAddressList {
		dbAddressLabelList, err := a.AddressLabelModel.QueryIdIn(ctx, nil, chain, addresses, getLimit(req.Force), 1)
		if err != nil {
			return 0, nil
		}
		existAddressLabelList = append(existAddressLabelList, dbAddressLabelList...)
	}
	if int32(len(existAddressLabelList)) >= getLimit(req.Force) {
		return 0, errors.New("affects too many rows")
	}

	if req.Force != 1 && len(existAddressLabelList) != 0 {
		return 0, fmt.Errorf("duplicate address labels: %+v", existAddressLabelList)
	}

	// key: chain - address
	existAddressModelMap := make(map[string]map[string]*model.AddressLabel)
	for _, row := range existAddressLabelList {
		if len(row.Chain) == 0 || len(row.Address) == 0 {
			return 0, errors.New("empty chain address")
		}

		if _, ok := existAddressModelMap[row.Chain]; !ok {
			existAddressModelMap[row.Chain] = make(map[string]*model.AddressLabel)
		}
		if _, ok := existAddressModelMap[row.Chain][row.Address]; !ok {
			existAddressModelMap[row.Chain][row.Address] = row
		} else {
			return 0, fmt.Errorf("duplicate chain address: %s,%s", row.Chain, row.Address)
		}
	}

	ot := model.OperationTypeInsert
	if req.Force == 1 {
		ot = model.OperationTypeUpsert
	}
	insertResult, err := a.AddressLabelChangeLogModel.Insert(ctx, &model.AddressLabelChangeLog{
		OperationUser: operationUser,
		OperationType: ot,
		AffectedRows:  int32(len(req.Rows)),
		Status:        model.StatusFailed,
	})
	if err != nil {
		return 0, err
	}
	operationLogId, err := insertResult.LastInsertId()
	if err != nil {
		return 0, err
	}

	insertOrUpdateModel := make([]*model.AddressLabel, 0)
	addressLabelHistoryList := make([]*model.AddressLabelHistory, 0)
	for _, row := range req.Rows {
		if _, ok := existAddressModelMap[row.Chain]; ok {
			if addressLabel, ok := existAddressModelMap[row.Chain][row.Address]; ok {
				oldDataStr, err := json.Marshal(addressLabel)
				if err != nil {
					return 0, err
				}
				// 更新数据
				addressLabel.Category = row.Category
				addressLabel.EntityName = row.EntityName
				addressLabel.DetailName = row.DetailName
				addressLabel.Editor = row.Editor
				addressLabel.Source = row.Source
				addressLabel.Remark = row.Remark
				addressLabel.Valid = row.Valid
				insertOrUpdateModel = append(insertOrUpdateModel, addressLabel)

				newDataStr, err := json.Marshal(row)
				if err != nil {
					return 0, err
				}
				addressLabelHistoryList = append(addressLabelHistoryList, &model.AddressLabelHistory{
					OperationLogId: uint64(operationLogId),
					AddressLabelId: row.Id,
					Chain:          row.Chain,
					Address:        row.Address,
					PreviousState:  model.StringToNullString(string(oldDataStr)),
					CurrentState:   model.StringToNullString(string(newDataStr)),
					OperationType:  model.OperationTypeUpdate,
				})

				continue
			}
		}
		// 没匹配上则使用新数据
		insertOrUpdateModel = append(insertOrUpdateModel, row)
		newDataStr, err := json.Marshal(row)
		if err != nil {
			return 0, err
		}
		addressLabelHistoryList = append(addressLabelHistoryList, &model.AddressLabelHistory{
			OperationLogId: uint64(operationLogId),
			AddressLabelId: row.Id,
			Chain:          row.Chain,
			Address:        row.Address,
			PreviousState:  model.StringToNullString(""),
			CurrentState:   model.StringToNullString(string(newDataStr)),
			OperationType:  model.OperationTypeInsert,
		})
	}
	if err = a.AddressLabelHistoryModel.BatchInsert(ctx, addressLabelHistoryList); err != nil {
		return 0, err
	}

	success := 0
	for _, row := range insertOrUpdateModel {
		if err := a.AddressLabelModel.InsertOrUpdate(ctx, row); err != nil {
			logc.Errorw(ctx, "insert or update error", logc.Field("error", err))
			continue
		}
		success++
	}

	return success, a.AddressLabelChangeLogModel.UpdateChangeLogStatus(ctx, uint64(operationLogId), model.StatusSuccess)
}

func (l *RiskAmlGatewayAddressLabelAdminLogic) upsertAllAddressLabel(ctx context.Context, operator string,
	req *BatchInsertReq) (int, error) {
	success := 0
	for _, row := range req.Rows {
		rowsAffected, err := l.upsert(ctx, &model.AllAddressLabel{
			Id:         row.Id,
			Chain:      row.Chain,
			Address:    row.Address,
			Category:   row.Category,
			EntityName: row.EntityName,
			DetailName: row.DetailName,
			Editor:     operator,
			Source:     row.Source,
			Remark:     row.Remark,
			Valid:      row.Valid,
		})
		if err != nil {
			logc.Warnw(ctx, "failed to upsert all address label", logc.Field("error", err), logc.Field("row", row))
			continue
		}
		success += int(rowsAffected)
	}
	return success, nil
}

func (l *RiskAmlGatewayAddressLabelAdminLogic) QueryByIDs(ctx context.Context, req *QueryByIdsReq) ([]*model.AddressLabel, error) {
	return l.AddressLabelModel.QueryIdIn(ctx, req.Where.Ids, req.Where.Chain, req.Where.Address, req.Limit, req.Page)
}

func getLimit(force int) int32 {
	if force == 1 {
		return forceLimit
	}
	return defaultLimit
}

func (l *RiskAmlGatewayAddressLabelAdminLogic) upsert(ctx context.Context, data *model.AllAddressLabel) (int64, error) {
	if transformedEntityName, transformedDetailName, err := l.transformService.TransformEntityNameAndDetailName(ctx, data.EntityName, data.DetailName); err == nil {
		data.EntityName = transformedEntityName
		data.DetailName = transformedDetailName
	} else {
		logc.Warnw(ctx, "failed to transform entity name and detail name, using original", logc.Field("original", data.EntityName), logc.Field("error", err))
	}

	transformedSource, err := l.transformService.TransformSource(ctx, data.Source)
	if err == nil {
		data.Source = transformedSource
	} else {
		logc.Warnw(ctx, "failed to transform source, using original", logc.Field("original", data.Source), logc.Field("error", err))
	}

	data.Source = transformedSource
	return l.AllAddressLabelModel.Upsert(ctx, data)
}
