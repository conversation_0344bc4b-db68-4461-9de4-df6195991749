package amlinsightadminapilogic

import (
	"aml-insight/service/admin/internal/svc/mock"
	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"context"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"testing"
)

func TestUpsertAmlLabelTrigger(t *testing.T) {
	ctrl := gomock.NewController(t)
	svc := mock.NewMockServiceContext(ctrl)
	logic := NewAmlInsightAdminAPIUpsertAmlLabelTriggerLogic(svc.SvcCtx)

	svc.AmlEmailTriggerModel.EXPECT().QueryByLabel(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	svc.AmlEmailTriggerModel.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(2)
	svc.AmlLabelTriggerModel.EXPECT().Update(gomock.Any(), gomock.Any())
	_, err := logic.UpsertAmlLabelTrigger(context.Background(), &aml_insightv1.UpsertAmlLabelTriggerRequest{
		Id:                                  1,
		TriggerLabel:                        "TriggerLabel",
		RiskLevel:                           "RiskLevel",
		ExposureType:                        "ExposureType",
		ManuallyAuto:                        "ManuallyAuto",
		AssetIsBf:                           "AssetIsBf",
		NeedEdd:                             "NeedEdd",
		SuggestedAc:                         "SuggestedAc",
		SuggestedQoa:                        "SuggestedQoa",
		SuggestedNoa:                        "SuggestedNoa",
		SuggestedAtb:                        "SuggestedAtb",
		SuggestedAtb2:                       "SuggestedAtb2",
		SuggestedAwb:                        "SuggestedAwb",
		SuggestedStr:                        "SuggestedStr",
		VendorSource:                        "VendorSource",
		TriggerRiskValue:                    "TriggerRiskValue",
		TriggerRiskUsdValue:                 "TriggerRiskUsdValue",
		TriggerRiskRatio:                    "TriggerRiskRatio",
		TriggerRiskEntityList:               "TriggerRiskEntityList",
		TriggerThisCategoryUsdOneMonth:      "TriggerThisCategoryUsdOneMonth",
		TriggerThisCategoryUsdLifetime:      "TriggerThisCategoryUsdLifetime",
		Status:                              1,
		TriggerThisCategoryWithdrawOneMonth: "TriggerThisCategoryWithdrawOneMonth",
		Editor:                              "Editor",
		Remark:                              "Remark",
		CreatedAt:                           "CreatedAt",
		UpdatedAt:                           "UpdatedAt",
		BrokerEmailTemplate:                 "BrokerEmailTemplate",
		NoBrokerEmailTemplate:               "NoBrokerEmailTemplate",
	})

	assert.Nil(t, err)
}
