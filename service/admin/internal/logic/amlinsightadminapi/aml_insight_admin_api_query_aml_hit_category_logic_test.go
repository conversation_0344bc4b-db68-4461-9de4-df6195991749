package amlinsightadminapilogic

import (
	"aml-insight/service/admin/internal/svc/mock"
	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"context"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"testing"
)

func TestQueryAmlHitCategory(t *testing.T) {
	ctrl := gomock.NewController(t)
	svc := mock.NewMockServiceContext(ctrl)
	logic := NewAmlInsightAdminAPIQueryAmlHitCategoryLogic(svc.SvcCtx)

	svc.AmlHitCategoryModel.EXPECT().QueryByIdOrCategory(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
	_, err := logic.QueryAmlHitCategory(context.Background(), &aml_insightv1.QueryAmlHitCategoryRequest{
		Id:       1,
		Category: "category",
		Page:     1,
		Limit:    10,
	})

	assert.Nil(t, err)
}
