package amlinsightadminapilogic

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"strconv"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/runutil"
	"code.bydev.io/frameworks/byone/core/collection"
	"code.bydev.io/frameworks/byone/core/logc"
	"github.com/360EntSecGroup-Skylar/excelize"

	"aml-insight/internal/model"
	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIImportAddressLabelLogic struct {
	svcCtx *svc.ServiceContext
}

type ImportStat struct {
	Total   int
	Success int
	Failed  int
	Reason  string
}

func NewAmlInsightAdminAPIImportAddressLabelLogic(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIImportAddressLabelLogic {
	return &AmlInsightAdminAPIImportAddressLabelLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAdminAPIImportAddressLabelLogic) ImportAddressLabel(ctx context.Context, in *aml_insightv1.ImportAddressLabelRequest) (*aml_insightv1.ImportAddressLabelResponse, error) {
	var resp = &aml_insightv1.ImportAddressLabelResponse{}
	data, err := base64.StdEncoding.DecodeString(in.GetData())
	if err != nil {
		resp.Code = 300100000 // global.SecurityDefault.Int64()
		resp.Msg = err.Error()
		return resp, nil
	}

	f, err := excelize.OpenReader(bytes.NewReader(data))
	if err != nil {
		logc.Warnw(ctx, "open excel failed", logc.Field("err", err))
		return nil, fmt.Errorf("open file err: %s", err.Error())
	}

	runutil.RunInBackground(ctx, func(ctx context.Context) {
		stat, err := l.importAddressLabel(ctx, in.OperatorName, f)
		if err != nil {
			logc.Warnw(ctx, "import address label record error", logc.Field("err", err))
		}
		if stat == nil {
			logc.Warnw(ctx, "import address label stat empty", logc.Field("err", err))
			return
		}
		logc.Infow(ctx, "import address label stat", logc.Field("stat", stat))
	})
	return &aml_insightv1.ImportAddressLabelResponse{}, nil
}

func (l *AmlInsightAdminAPIImportAddressLabelLogic) importAddressLabel(ctx context.Context, operationUser string, f *excelize.File) (*ImportStat, error) {

	var (
		stat          = &ImportStat{}
		set           = collection.NewSet()
		addressLabels = make([]*model.AddressLabel, 0)
	)

	for _, sheet := range f.GetSheetMap() {
		sheetRows := f.GetRows(sheet)
		for index, fields := range sheetRows {
			// 判断列数
			if len(fields) < 9 {
				logc.Error(ctx, "invalid address label data", logc.Field("fields", fields))
				stat.Failed = len(sheetRows)
				stat.Reason = fmt.Sprintf("sheet: %s, line: %d, invalid address label data: %v", sheet, index, fields)
				return nil, errors.New("invalid excel sheet len")
			}
			// 判断表头
			if index == 0 {
				if fields[0] != "chain" || fields[1] != "address" || fields[2] != "category" || fields[3] != "entity_name" || fields[4] != "detail_name" || fields[5] != "editor" || fields[6] != "source" || fields[7] != "remark" || fields[8] != "valid" {
					return nil, errors.New("invalid excel sheet head")
				}
				continue
			}
			// 判断特定的字段数据格式
			valid, err := strconv.ParseInt(fields[8], 10, 32)
			if err != nil {
				return nil, errors.New("invalid address label data format")
			}
			if valid != 0 && valid != 1 {
				return nil, errors.New("invalid address label data value")
			}
			addressLabel := &model.AddressLabel{
				Chain:      fields[0],
				Address:    fields[1],
				Category:   fields[2],
				EntityName: fields[3],
				DetailName: fields[4],
				Editor:     operationUser,
				Source:     fields[6],
				Remark:     fields[7],
				Valid:      int32(valid),
			}
			if set.Contains(fmt.Sprintf("%s_%s", addressLabel.Chain, addressLabel.Address)) {
				logc.Warnw(ctx, "address exist, skip row", logc.Field("fields", fields))
				continue
			}
			set.Add(fmt.Sprintf("%s_%s", addressLabel.Chain, addressLabel.Address))
			addressLabels = append(addressLabels, addressLabel)
			stat.Total++
		}
	}

	successes, err := NewRiskAmlGatewayAddressLabelAdminLogic(l.svcCtx).BatchInsert(ctx, operationUser, &BatchInsertReq{
		Force: 1,
		Rows:  addressLabels,
	})
	if err != nil {
		return nil, err
	}
	stat.Success = successes
	stat.Failed = len(addressLabels) - successes

	return stat, nil
}
