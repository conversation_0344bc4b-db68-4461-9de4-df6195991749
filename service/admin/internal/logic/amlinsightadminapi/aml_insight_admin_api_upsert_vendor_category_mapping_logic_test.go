package amlinsightadminapilogic

import (
	"aml-insight/service/admin/internal/svc/mock"
	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"context"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"testing"
)

func TestUpsertVendorCategoryMapping(t *testing.T) {
	ctrl := gomock.NewController(t)
	svc := mock.NewMockServiceContext(ctrl)
	logic := NewAmlInsightAdminAPIUpsertVendorCategoryMappingLogic(svc.SvcCtx)

	svc.VendorCategoryMappingModel.EXPECT().Update(gomock.Any(), gomock.Any())
	_, err := logic.UpsertVendorCategoryMapping(context.Background(), &aml_insightv1.UpsertVendorCategoryMappingRequest{
		Id:        1,
		Vendor:    "Vendor",
		Key:       "Key",
		Val:       "Val",
		Valid:     1,
		Editor:    "Editor",
		Remark:    "Remark",
		CreatedAt: "CreatedAt",
		UpdatedAt: "UpdatedAt",
	})

	assert.Nil(t, err)
}
