package amlinsightadminapilogic

import (
	"context"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIQueryAmlHitCategoryLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAdminAPIQueryAmlHitCategoryLogic(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIQueryAmlHitCategoryLogic {
	return &AmlInsightAdminAPIQueryAmlHitCategoryLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAdminAPIQueryAmlHitCategoryLogic) QueryAmlHitCategory(ctx context.Context, in *aml_insightv1.QueryAmlHitCategoryRequest) (*aml_insightv1.QueryAmlHitCategoryResponse, error) {
	var (
		resp = &aml_insightv1.QueryAmlHitCategoryResponse{
			Code: 0,
			Msg:  "",
			Result: &aml_insightv1.QueryAmlHitCategoryResponse_Result{
				List:  make([]*aml_insightv1.UpsertAmlHitCategoryRequest, 0),
				Page:  in.GetPage(),
				Total: 1000,
			},
		}
	)

	amlHitCategory, err := l.svcCtx.AmlHitCategoryModel.QueryByIdOrCategory(ctx, 0, in.GetCategory(), in.GetLimit(), in.GetPage())
	if err != nil {
		return nil, err
	}

	for _, category := range amlHitCategory {
		resp.Result.List = append(resp.Result.List, &aml_insightv1.UpsertAmlHitCategoryRequest{
			Id:             category.Id,
			Provider:       category.Provider,
			Category:       category.Category,
			Entity:         category.Entity,
			ActionType:     category.ActionType,
			ExposureType:   category.ExposureType,
			RiskLevel:      category.RiskLevel,
			RiskLevelScore: category.RiskLevelScore,
			Status:         category.Status,
			CreatedAt:      category.CreatedAt.String(),
			UpdatedAt:      category.UpdatedAt.String(),
			CategoryId:     category.CategoryId,
			Editor:         category.Editor,
			Remark:         category.Remark,
		})
	}

	return resp, nil
}
