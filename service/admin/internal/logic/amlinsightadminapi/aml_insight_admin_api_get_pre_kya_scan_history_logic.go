package amlinsightadminapilogic

import (
	"aml-insight/internal/pkg/prekya"
	"code.bydev.io/frameworks/byone/core/logc"
	"context"
	"fmt"
	"git.bybit.com/gtdmicro/stub/pkg/pb/loyalty_program/priv"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"time"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIGetPreKyaScanHistoryLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAdminAPIGetPreKyaScanHistoryLogic(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIGetPreKyaScanHistoryLogic {
	return &AmlInsightAdminAPIGetPreKyaScanHistoryLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAdminAPIGetPreKyaScanHistoryLogic) GetPreKyaScanHistory(ctx context.Context, in *aml_insightv1.GetPreKyaScanHistoryRequest) (*aml_insightv1.GetPreKyaScanHistoryResponse, error) {
	var (
		resp = &aml_insightv1.GetPreKyaScanHistoryResponse{Result: &aml_insightv1.GetPreKyaScanHistoryResponse_Result{
			List:  make([]*aml_insightv1.PreKyaScanHistory, 0),
			Page:  1,
			Total: 0,
		}}
		startTime, endTime time.Time
	)

	if in.GetMemberId() == 0 {
		// TODO 返回业务 error ?
		return nil, status.Error(codes.InvalidArgument, "member id is required")
	}
	if in.Page < 1 {
		in.Page = 1
	}
	if in.Limit < 10 || in.Limit > 100 {
		in.Limit = 10
	}

	nowTime := time.Now().UTC()
	minimumTime := time.Now().AddDate(0, 0, -180).UTC()
	// check
	if in.GetStartTime() == 0 {
		startTime = minimumTime
	} else {
		startTime = time.Unix(in.GetStartTime(), 0).UTC()
		if startTime.Before(minimumTime) {
			startTime = minimumTime
		}
	}

	if in.GetEndTime() == 0 {
		endTime = nowTime
	} else {
		endTime = time.Unix(in.GetEndTime(), 0).UTC()
		if endTime.After(nowTime) {
			endTime = nowTime
		}
	}

	if startTime.After(endTime) {
		logc.Warnf(ctx, "start time is after end time")
		return resp, nil
	}

	// limit
	if endTime.After(nowTime) {
		endTime = nowTime
	}

	kyaHistory, err := l.svcCtx.PreKyaHistoryModel.QueryKyaHistoryByTime(ctx, in.GetMemberId(), startTime, endTime, in.GetAddress(), in.GetResult(), in.GetLimit(), (in.GetPage()-1)*in.GetLimit())
	if err != nil {
		return nil, err
	}
	count, err := l.svcCtx.PreKyaHistoryModel.CountKyaHistoryResultIn(ctx, in.GetMemberId(), startTime, endTime, in.GetAddress(), []string{in.GetResult()})
	resp.Result.Total = count
	resp.Result.Page = in.GetPage()
	for _, history := range kyaHistory {
		preKyaResultInfo, ok := prekya.PreKyaResultMap[history.Result]
		if !ok {
			logc.Errorw(ctx, "preKyaResultInfo not exist", logc.Field("result", history.Result))
			continue
		}
		if history.VipType != int32(priv.LabelType_LABEL_TYPE_VIP) {
			logc.Errorw(ctx, "vipStatus not matched", logc.Field("check_history", history))
			continue
		}
		vipLevelStr := fmt.Sprintf("VIP %d", history.VipLevel)
		resp.Result.List = append(resp.Result.List, &aml_insightv1.PreKyaScanHistory{
			Id:              int64(history.Id),
			Chain:           history.Chain,
			Address:         history.Address,
			Result:          history.Result,
			Title:           preKyaResultInfo.Title,
			TitleCkey:       preKyaResultInfo.TitleCkey,
			Description:     preKyaResultInfo.Description,
			DescriptionCkey: preKyaResultInfo.DescriptionCkey,
			CheckTime:       history.CreatedAt.UTC().Unix(),
			VipType:         history.VipType,
			VipLevel:        history.VipLevel,
			MemberId:        history.MemberId,
			VipLevelStr:     vipLevelStr,
		})
	}
	return resp, nil
}
