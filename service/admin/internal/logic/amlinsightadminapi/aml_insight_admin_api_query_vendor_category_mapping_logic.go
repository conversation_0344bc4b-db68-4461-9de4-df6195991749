package amlinsightadminapilogic

import (
	"context"

	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIQueryVendorCategoryMappingLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAdminAPIQueryVendorCategoryMappingLogic(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIQueryVendorCategoryMappingLogic {
	return &AmlInsightAdminAPIQueryVendorCategoryMappingLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAdminAPIQueryVendorCategoryMappingLogic) QueryVendorCategoryMapping(ctx context.Context, in *aml_insightv1.QueryVendorCategoryMappingRequest) (*aml_insightv1.QueryVendorCategoryMappingResponse, error) {
	var (
		resp = &aml_insightv1.QueryVendorCategoryMappingResponse{
			Code: 0,
			Msg:  "",
			Result: &aml_insightv1.QueryVendorCategoryMappingResponse_Result{
				List:  make([]*aml_insightv1.UpsertVendorCategoryMappingRequest, 0),
				Page:  in.GetPage(),
				Total: 10000,
			},
		}
	)
	vendorCategoryMappings, err := l.svcCtx.VendorCategoryMappingModel.QueryByVendorCategoryName(ctx, in.GetId(), in.GetKey(), in.GetLimit(), in.GetPage())
	if err != nil {
		return nil, err
	}
	for _, vendorCategoryMapping := range vendorCategoryMappings {
		resp.Result.List = append(resp.Result.List, &aml_insightv1.UpsertVendorCategoryMappingRequest{
			Id:        vendorCategoryMapping.Id,
			Vendor:    vendorCategoryMapping.Vendor,
			Key:       vendorCategoryMapping.CategoryKey,
			Val:       vendorCategoryMapping.CategoryVal,
			Valid:     vendorCategoryMapping.Valid,
			Editor:    vendorCategoryMapping.Editor,
			Remark:    vendorCategoryMapping.Remark,
			CreatedAt: vendorCategoryMapping.CreatedAt.String(),
			UpdatedAt: vendorCategoryMapping.UpdatedAt.String(),
		})
	}
	return resp, nil
}
