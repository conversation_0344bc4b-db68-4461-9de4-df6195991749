package amlinsightadminapilogic

//func TestImportAddressLabel(t *testing.T) {
//	ctrl := gomock.NewController(t)
//	svc := mock.NewMockServiceContext(ctrl)
//	logic := NewAmlInsightAdminAPIImportAddressLabelLogic(svc.SvcCtx)
//	resp, err := logic.ImportAddressLabel(context.Background(), &aml_insightv1.ImportAddressLabelRequest{
//		Data:         "",
//		FileName:     "",
//		OperatorName: "",
//	})
//	assert.Error(t, err)
//	assert.Nil(t, resp)
//
//	resp, err = logic.ImportAddressLabel(context.Background(), &aml_insightv1.ImportAddressLabelRequest{
//		Data:         "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",
//		FileName:     "",
//		OperatorName: "mocker",
//	})
//
//	assert.NoError(t, err)
//	assert.NotNil(t, resp)
//}
