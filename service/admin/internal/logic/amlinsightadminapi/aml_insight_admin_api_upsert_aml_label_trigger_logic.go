package amlinsightadminapilogic

import (
	"aml-insight/internal/model"
	"aml-insight/service/admin/internal/svc"
	"context"
	"errors"
	"fmt"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIUpsertAmlLabelTriggerLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAdminAPIUpsertAmlLabelTriggerLogic(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIUpsertAmlLabelTriggerLogic {
	return &AmlInsightAdminAPIUpsertAmlLabelTriggerLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAdminAPIUpsertAmlLabelTriggerLogic) UpsertAmlLabelTrigger(ctx context.Context, in *aml_insightv1.UpsertAmlLabelTriggerRequest) (*aml_insightv1.UpsertAmlLabelTriggerResponse, error) {
	var (
		resp = &aml_insightv1.UpsertAmlLabelTriggerResponse{}
	)
	if len(in.GetBrokerEmailTemplate()) == 0 || len(in.GetNoBrokerEmailTemplate()) == 0 {
		resp.Code = 400
		resp.Msg = "invalid email template"
		return resp, nil
	}
	if len(in.GetTriggerLabel()) == 0 {
		resp.Code = 400
		resp.Msg = "invalid trigger label"
		return resp, nil
	}

	amlLabelTrigger := &model.AmlLabelTrigger{
		Id:                                  in.GetId(),
		TriggerLabel:                        in.GetTriggerLabel(),
		RiskLevel:                           in.GetRiskLevel(),
		ExposureType:                        in.GetExposureType(),
		ManuallyAuto:                        in.GetManuallyAuto(),
		AssetIsBf:                           in.GetAssetIsBf(),
		NeedEdd:                             in.GetNeedEdd(),
		SuggestedAc:                         in.GetSuggestedAc(),
		SuggestedQoa:                        in.GetSuggestedQoa(),
		SuggestedNoa:                        in.GetSuggestedNoa(),
		SuggestedAtb:                        in.GetSuggestedAtb(),
		SuggestedAtb2:                       in.GetSuggestedAtb2(),
		SuggestedAwb:                        in.GetSuggestedAwb(),
		SuggestedStr:                        in.GetSuggestedStr(),
		VendorSource:                        in.GetVendorSource(),
		TriggerRiskValue:                    in.GetTriggerRiskValue(),
		TriggerRiskUsdValue:                 in.GetTriggerRiskUsdValue(),
		TriggerRiskRatio:                    in.GetTriggerRiskRatio(),
		TriggerRiskEntityList:               in.GetTriggerRiskEntityList(),
		TriggerThisCategoryUsdOneMonth:      in.GetTriggerThisCategoryUsdOneMonth(),
		TriggerThisCategoryUsdLifetime:      in.GetTriggerThisCategoryUsdLifetime(),
		Status:                              in.GetStatus(),
		TriggerThisCategoryWithdrawOneMonth: in.GetTriggerThisCategoryWithdrawOneMonth(),
		Editor:                              in.GetEditor(),
		Remark:                              in.GetRemark(),
	}

	if amlLabelTrigger.Id == 0 {
		amlLabel, err := l.svcCtx.AmlLabelTriggerModel.QueryByIdOrLabel(ctx, 0, amlLabelTrigger.TriggerLabel, 10, 1)
		if err != nil {
			return nil, err
		}
		if len(amlLabel) != 0 {
			return nil, fmt.Errorf("label exist,label:%s", amlLabelTrigger.TriggerLabel)
		}
	}

	emailLabels, err := l.svcCtx.AmlEmailTriggerModel.QueryByLabel(ctx, amlLabelTrigger.TriggerLabel, 10, 1)
	if err != nil {
		return nil, err
	}
	if len(emailLabels) > 2 {
		return nil, errors.New("email template have multiple, check it")
	}
	emailLabelMap := make(map[string]*model.AmlEmailTrigger)
	for _, label := range emailLabels {
		emailLabelMap[label.IsBrokerSubAccount] = label
	}

	if label, ok := emailLabelMap[model.TriggerEmailLabelBrokerYes]; ok {
		if label.FirstEmailTemplate != in.GetBrokerEmailTemplate() {
			label.FirstEmailTemplate = in.GetBrokerEmailTemplate()
			if err = l.svcCtx.AmlEmailTriggerModel.Update(ctx, label); err != nil {
				return nil, err
			}
		}
	} else {
		if _, err = l.svcCtx.AmlEmailTriggerModel.Insert(ctx, &model.AmlEmailTrigger{
			TriggerLabel:       amlLabelTrigger.TriggerLabel,
			IsBrokerSubAccount: model.TriggerEmailLabelBrokerYes,
			FirstEmailTemplate: in.GetBrokerEmailTemplate(),
			Status:             1,
		}); err != nil {
			return nil, err
		}
	}

	if label, ok := emailLabelMap[model.TriggerEmailLabelBrokerNo]; ok {
		if label.FirstEmailTemplate != in.GetNoBrokerEmailTemplate() {
			label.FirstEmailTemplate = in.GetNoBrokerEmailTemplate()
			if err = l.svcCtx.AmlEmailTriggerModel.Update(ctx, label); err != nil {
				return nil, err
			}
		}
	} else {
		if _, err = l.svcCtx.AmlEmailTriggerModel.Insert(ctx, &model.AmlEmailTrigger{
			TriggerLabel:       amlLabelTrigger.TriggerLabel,
			IsBrokerSubAccount: model.TriggerEmailLabelBrokerNo,
			FirstEmailTemplate: in.GetNoBrokerEmailTemplate(),
			Status:             1,
		}); err != nil {
			return nil, err
		}
	}

	if amlLabelTrigger.Id == 0 {
		if _, err := l.svcCtx.AmlLabelTriggerModel.Insert(ctx, amlLabelTrigger); err != nil {
			return nil, err
		}
	} else {
		if err := l.svcCtx.AmlLabelTriggerModel.Update(ctx, amlLabelTrigger); err != nil {
			return nil, err
		}
	}

	return resp, nil
}
