package amlinsightadminapilogic

import (
	"context"
	"errors"
	"sort"
	"time"

	"aml-insight/internal/model"
	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIQueryAddressLabelLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAdminAPIQueryAddressLabelLogic(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIQueryAddressLabelLogic {
	return &AmlInsightAdminAPIQueryAddressLabelLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAdminAPIQueryAddressLabelLogic) QueryAddressLabel(ctx context.Context, in *aml_insightv1.QueryAddressLabelRequest) (*aml_insightv1.QueryAddressLabelResponse, error) {
	if l.svcCtx.Config.AddressLabelDBSwitch.EnableReadTiDB {
		return l.queryAddressLabelInTiDB(ctx, in)
	}
	return l.queryAddressLabelInMysql(ctx, in)
}

func (l *AmlInsightAdminAPIQueryAddressLabelLogic) queryAddressLabelInMysql(ctx context.Context, in *aml_insightv1.QueryAddressLabelRequest) (*aml_insightv1.QueryAddressLabelResponse, error) {
	var addressLabelList []*model.AddressLabel
	var err error
	var total int32 = 1000000
	if len(in.Address) == 0 {
		if len(in.Chain) != 0 {
			return nil, errors.New("you can only query by address, or query all")
		}
		addressLabelList, err = l.svcCtx.AddressLabelModel.QueryAll(ctx, in.GetLimit(), in.GetPage())
	} else {
		addressLabelList, err = NewRiskAmlGatewayAddressLabelAdminLogic(l.svcCtx).QueryByIDs(ctx, &QueryByIdsReq{
			Force: 0,
			Where: &QueryByIds{
				Address: []string{in.GetAddress()},
				Chain:   in.GetChain(),
			},
			Limit: in.GetLimit(),
			Page:  in.GetPage(),
		})
		sort.Slice(addressLabelList, func(i, j int) bool {
			return addressLabelList[i].Id > addressLabelList[j].Id
		})
		total = int32(len(addressLabelList))
	}
	if err != nil {
		return nil, err
	}

	resp := &aml_insightv1.QueryAddressLabelResponse{
		Code: 0,
		Msg:  "",
		Result: &aml_insightv1.QueryAddressLabelResponse_Result{
			List:  make([]*aml_insightv1.AddressLabel, 0),
			Page:  0,
			Total: total,
		},
	}
	for _, addressLabel := range addressLabelList {
		resp.Result.List = append(resp.Result.List, &aml_insightv1.AddressLabel{
			Id:         addressLabel.Id,
			Chain:      addressLabel.Chain,
			Address:    addressLabel.Address,
			Category:   addressLabel.Category,
			EntityName: addressLabel.EntityName,
			DetailName: addressLabel.DetailName,
			Editor:     addressLabel.Editor,
			Source:     addressLabel.Source,
			Remark:     addressLabel.Remark,
			Valid:      addressLabel.Valid,
			CreatedAt:  addressLabel.CreatedAt.Format(time.DateTime),
			UpdatedAt:  addressLabel.UpdatedAt.Format(time.DateTime),
		})
	}
	return resp, nil
}

func (l *AmlInsightAdminAPIQueryAddressLabelLogic) queryAddressLabelInTiDB(ctx context.Context, in *aml_insightv1.QueryAddressLabelRequest) (*aml_insightv1.QueryAddressLabelResponse, error) {
	var (
		addressLabelList []*model.AllAddressLabel
		err              error
		total            int32 = 1000000
	)

	if len(in.Address) == 0 {
		if len(in.Chain) != 0 {
			return nil, errors.New("you can only query by address, or query all")
		}
		addressLabelList, err = l.svcCtx.AllAddressLabelModel.QueryAll(ctx, in.GetLimit(), in.GetPage())
	} else {
		addressLabelList, err = l.svcCtx.AllAddressLabelModel.QueryByAddressAndChain(ctx,
			in.GetChain(), []string{in.GetAddress()}, in.GetLimit(), in.GetPage())
		total = int32(len(addressLabelList))
	}

	if err != nil {
		return nil, err
	}

	resp := &aml_insightv1.QueryAddressLabelResponse{
		Code: 0,
		Msg:  "",
		Result: &aml_insightv1.QueryAddressLabelResponse_Result{
			List:  make([]*aml_insightv1.AddressLabel, 0, len(addressLabelList)),
			Page:  0,
			Total: total,
		},
	}

	for _, addressLabel := range addressLabelList {
		resp.Result.List = append(resp.Result.List, &aml_insightv1.AddressLabel{
			Id:         addressLabel.Id,
			Chain:      addressLabel.Chain,
			Address:    addressLabel.Address,
			Category:   addressLabel.Category,
			EntityName: addressLabel.EntityName,
			DetailName: addressLabel.DetailName,
			Editor:     addressLabel.Editor,
			Source:     addressLabel.Source,
			Remark:     addressLabel.Remark,
			Valid:      addressLabel.Valid,
			CreatedAt:  addressLabel.CreatedAt.Format(time.DateTime),
			UpdatedAt:  addressLabel.UpdatedAt.Format(time.DateTime),
		})
	}
	return resp, nil
}
