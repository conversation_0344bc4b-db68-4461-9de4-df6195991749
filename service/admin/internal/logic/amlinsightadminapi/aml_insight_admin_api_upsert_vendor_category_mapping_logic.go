package amlinsightadminapilogic

import (
	"aml-insight/internal/model"
	"aml-insight/service/admin/internal/svc"
	"context"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIUpsertVendorCategoryMappingLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAdminAPIUpsertVendorCategoryMappingLogic(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIUpsertVendorCategoryMappingLogic {
	return &AmlInsightAdminAPIUpsertVendorCategoryMappingLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAdminAPIUpsertVendorCategoryMappingLogic) UpsertVendorCategoryMapping(ctx context.Context, in *aml_insightv1.UpsertVendorCategoryMappingRequest) (*aml_insightv1.UpsertVendorCategoryMappingResponse, error) {

	var (
		resp = &aml_insightv1.UpsertVendorCategoryMappingResponse{
			Code: 0,
			Msg:  "",
		}
	)

	if len(in.GetVendor()) == 0 || len(in.GetKey()) == 0 || len(in.GetVal()) == 0 || in.Valid != 0 && in.Valid != 1 {
		resp.Code = 400
		resp.Msg = "invalid vendor key or val"
		return resp, nil
	}

	vendorCategoryMapping := &model.VendorCategoryMapping{
		Id:          in.GetId(),
		Vendor:      in.GetVendor(),
		CategoryKey: in.GetKey(),
		CategoryVal: in.GetVal(),
		Valid:       in.GetValid(),
		Editor:      in.GetEditor(),
		Remark:      in.GetRemark(),
	}

	if vendorCategoryMapping.Id == 0 {
		_, err := l.svcCtx.VendorCategoryMappingModel.Insert(ctx, vendorCategoryMapping)
		if err != nil {
			return nil, err
		}
		return resp, nil
	}

	err := l.svcCtx.VendorCategoryMappingModel.Update(ctx, vendorCategoryMapping)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
