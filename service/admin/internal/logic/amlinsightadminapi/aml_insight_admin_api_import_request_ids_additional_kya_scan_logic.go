package amlinsightadminapilogic

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/runutil"
	"code.bydev.io/frameworks/byone/core/collection"
	"code.bydev.io/frameworks/byone/core/logc"
	"github.com/360EntSecGroup-Skylar/excelize"

	"aml-insight/internal/model"
	"aml-insight/internal/pkg/event"
	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
)

type AmlInsightAdminAPIImportRequestIdsAdditionalKyaScanLogic struct {
	svcCtx *svc.ServiceContext
}

func NewAmlInsightAdminAPIImportRequestIdsAdditionalKyaScanLogic(svcCtx *svc.ServiceContext) *AmlInsightAdminAPIImportRequestIdsAdditionalKyaScanLogic {
	return &AmlInsightAdminAPIImportRequestIdsAdditionalKyaScanLogic{
		svcCtx: svcCtx,
	}
}

func (l *AmlInsightAdminAPIImportRequestIdsAdditionalKyaScanLogic) ImportRequestIdsAdditionalKyaScan(ctx context.Context, in *aml_insightv1.ImportRequestIdsAdditionalKyaScanRequest) (*aml_insightv1.ImportRequestIdsAdditionalKyaScanResponse, error) {
	var (
		resp = &aml_insightv1.ImportRequestIdsAdditionalKyaScanResponse{}
	)
	logc.Warnw(ctx, "import request ids for additional kya scan", logc.Field("operator_name", in.OperatorName))
	data, err := base64.StdEncoding.DecodeString(in.GetData())
	if err != nil {
		resp.Code = 300100000 // global.SecurityDefault.Int64()
		resp.Msg = err.Error()
		return resp, nil
	}

	f, err := excelize.OpenReader(bytes.NewReader(data))
	if err != nil {
		logc.Warnw(ctx, "open excel failed", logc.Field("err", err))
		return nil, fmt.Errorf("open file err: %s", err.Error())
	}

	runutil.RunInBackground(ctx, func(ctx context.Context) {
		stat, err := l.importRequestIds(ctx, in.OperatorName, f)
		if err != nil {
			logc.Warnw(ctx, "import address label record error", logc.Field("err", err))
		}
		if stat == nil {
			logc.Warnw(ctx, "import address label stat empty", logc.Field("err", err))
			return
		}
		logc.Infow(ctx, "import address label stat", logc.Field("stat", stat))
	})

	return resp, nil
}

func (l *AmlInsightAdminAPIImportRequestIdsAdditionalKyaScanLogic) importRequestIds(ctx context.Context, operationUser string, f *excelize.File) (*ImportStat, error) {
	var (
		stat       = &ImportStat{}
		set        = collection.NewSet()
		requestIds = make([]string, 0)
	)

	for _, sheet := range f.GetSheetMap() {
		for index, row := range f.GetRows(sheet) {
			if len(row) < 1 {
				logc.Warnw(ctx, "row empty", logc.Field("row_index", index))
				continue
			}
			if index == 0 {
				if row[0] != "request_id" {
					return nil, errors.New("invalid excel sheet")
				} else {
					logc.Infow(ctx, "skip row", logc.Field("row", row))
					continue
				}
			}

			requestId := row[0]
			if len(requestId) == 0 {
				logc.Warnw(ctx, "invalid address label data", logc.Field("row", row))
				continue
			}
			if set.Contains(requestId) {
				logc.Warnw(ctx, "address exist, skip row", logc.Field("row", row))
				continue
			}
			set.Add(requestId)
			stat.Total++
			requestIds = append(requestIds, requestId)
		}
	}

	result, err := l.svcCtx.AdditionalKyaScanModel.Insert(ctx, &model.AdditionalKyaScan{
		Operator:       operationUser,
		CaseCount:      int32(stat.Total),
		ValidCaseCount: int32(len(requestIds)),
	})
	if err != nil {
		return nil, err
	}

	additionalId, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	for _, requestId := range requestIds {
		if err = l.checkAndSendAdditionalKyaMsg(ctx, &event.AdditionalKyaMsg{
			AdditionalKyaScanTaskId: uint64(additionalId),
			OriginRequestId:         requestId,
		}); err != nil {
			if err == model.ErrNotFound {
				logc.Warnw(ctx, "aml_case not found, skip row", logc.Field("additional_id", additionalId), logc.Field("requestId", requestId))
			} else {
				logc.Errorw(ctx, "send additional kya msg error", logc.Field("requestId", requestId), logc.Field("err", err))
			}
			continue
		}
		stat.Success++
	}

	return stat, nil
}

func (l *AmlInsightAdminAPIImportRequestIdsAdditionalKyaScanLogic) checkAndSendAdditionalKyaMsg(ctx context.Context, msg *event.AdditionalKyaMsg) error {
	amlCase, err := l.svcCtx.AmlCaseModel.FindOneByRequestId(ctx, msg.OriginRequestId)
	if err != nil {
		return err
	}

	msg.ActionType = amlCase.ActionType
	msg.RequestId = fmt.Sprintf(event.AdditionalKyaScanRequestFormat, msg.AdditionalKyaScanTaskId, msg.OriginRequestId)

	if amlCase.ActionType == model.TYPE_WITHDRAWAL {
		msg.WithdrawTrigger = &event.WithdrawTrigger{
			RequestId:    msg.RequestId,
			MemberId:     amlCase.MemberId,
			Chain:        amlCase.Chain,
			Coin:         amlCase.Coin,
			FromAddress:  amlCase.FromAddress,
			ToAddress:    amlCase.ToAddress,
			Tag:          "",
			ActionType:   amlCase.ActionType,
			Amount:       amlCase.Amount.String(),
			BrokerId:     "",
			SpendSubType: "",
		}
	} else if amlCase.ActionType == model.TYPE_DEPOSIT {
		msg.DepositTrigger = &event.DepositTrigger{
			RequestId:   msg.RequestId,
			MemberId:    amlCase.MemberId,
			Chain:       amlCase.Chain,
			Coin:        amlCase.Coin,
			FromAddress: amlCase.FromAddress,
			ToAddress:   amlCase.ToAddress,
			Tag:         "",
			ActionType:  amlCase.ActionType,
			Amount:      amlCase.Amount.String(),
			TxHash:      amlCase.TxHash,
			TxIndex:     "", // amlCase.TxIndex,
			BlockHash:   amlCase.BlockHash,
			State:       "",
			BrokerId:    "",
		}
	}

	msgStr, err := json.Marshal(msg)
	if err != nil {
		return err
	}
	return event.DoSendToAMLGatewayProxyMsg(ctx, l.svcCtx.FiatProdProducer, event.AdditionalKyaScanEvent, string(msgStr))
}
