package mock

import (
	mock_model "aml-insight/internal/mock/model"
	"aml-insight/service/admin/internal/config"
	"aml-insight/service/admin/internal/svc"

	"go.uber.org/mock/gomock"
)

type MockServiceContext struct {
	SvcCtx *svc.ServiceContext
	Config config.Config

	// models
	// 自研风险标签库
	AddressLabelModel          *mock_model.MockAddressLabelModel
	AllAddressLabelModel       *mock_model.MockAllAddressLabelModel
	AddressLabelHistoryModel   *mock_model.MockAddressLabelHistoryModel
	AddressLabelChangeLogModel *mock_model.MockAddressLabelChangeLogModel
	AmlEmailTriggerModel       *mock_model.MockAmlEmailTriggerModel
	AmlHitCategoryModel        *mock_model.MockAmlHitCategoryModel
	AmlLabelTriggerModel       *mock_model.MockAmlLabelTriggerModel
	EntityNameMappingModel     *mock_model.MockEntityNameMappingModel
	VendorCategoryMappingModel *mock_model.MockVendorCategoryMappingModel
}

func NewMockServiceContext(ctrl *gomock.Controller) *MockServiceContext {
	mockSvcCtx := &MockServiceContext{
		Config: config.Config{},
		// models
		AddressLabelModel:          mock_model.NewMockAddressLabelModel(ctrl),
		AllAddressLabelModel:       mock_model.NewMockAllAddressLabelModel(ctrl),
		AddressLabelHistoryModel:   mock_model.NewMockAddressLabelHistoryModel(ctrl),
		AddressLabelChangeLogModel: mock_model.NewMockAddressLabelChangeLogModel(ctrl),
		AmlEmailTriggerModel:       mock_model.NewMockAmlEmailTriggerModel(ctrl),
		AmlHitCategoryModel:        mock_model.NewMockAmlHitCategoryModel(ctrl),
		AmlLabelTriggerModel:       mock_model.NewMockAmlLabelTriggerModel(ctrl),
		EntityNameMappingModel:     mock_model.NewMockEntityNameMappingModel(ctrl),
		VendorCategoryMappingModel: mock_model.NewMockVendorCategoryMappingModel(ctrl),
	}
	mockSvcCtx.SvcCtx = &svc.ServiceContext{
		Config:                     mockSvcCtx.Config,
		AllAddressLabelModel:       mockSvcCtx.AllAddressLabelModel,
		AddressLabelModel:          mockSvcCtx.AddressLabelModel,
		AddressLabelHistoryModel:   mockSvcCtx.AddressLabelHistoryModel,
		AddressLabelChangeLogModel: mockSvcCtx.AddressLabelChangeLogModel,
		AmlEmailTriggerModel:       mockSvcCtx.AmlEmailTriggerModel,
		AmlHitCategoryModel:        mockSvcCtx.AmlHitCategoryModel,
		AmlLabelTriggerModel:       mockSvcCtx.AmlLabelTriggerModel,
		EntityNameMappingModel:     mockSvcCtx.EntityNameMappingModel,
		VendorCategoryMappingModel: mockSvcCtx.VendorCategoryMappingModel,
	}
	return mockSvcCtx
}
