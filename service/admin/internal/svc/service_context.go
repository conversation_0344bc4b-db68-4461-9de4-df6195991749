package svc

import (
	"errors"

	assetshow "code.bydev.io/cht/asset/asset-show-stub.git/pkg/bybit/assetshow/inner/v1"
	appealv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/appeal/v1"
	bizconfigv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/bizconfig/v1"
	quotev1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/quote/v1"
	"code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/card"
	"code.bydev.io/cht/fiat/backend/bufgen.git/pkg/java/user"
	"code.bydev.io/frameworks/byone/core/stores/redis"
	"code.bydev.io/frameworks/byone/kafka"
	"code.bydev.io/frameworks/byone/zrpc"
	"code.bydev.io/frameworks/sechub-go/api"
	"code.bydev.io/frameworks/sechub-go/client"
	platformkyc "git.bybit.com/svc/stub/pkg/pb/api/kyc"
	memberuser "git.bybit.com/svc/stub/pkg/pb/api/user"

	"aml-insight/internal/model"
	"aml-insight/internal/pkg/oklink"
	"aml-insight/internal/pkg/rpc"
	"aml-insight/pkg/bydata"
	"aml-insight/service/admin/internal/config"
	"aml-insight/service/admin/internal/logic/txdata"
)

type ServiceContext struct {
	Config config.Config

	BizRedis *redis.Redis

	// models
	// 自研风险标签库
	AddressLabelModel          model.AddressLabelModel
	AllAddressLabelModel       model.AllAddressLabelModel
	AddressLabelHistoryModel   model.AddressLabelHistoryModel
	AddressLabelChangeLogModel model.AddressLabelChangeLogModel

	HackTxModel model.HackTxModel

	// AML 规则相关 model
	AmlHitCategoryModel     model.AmlHitCategoryModel
	AmlLabelTriggerModel    model.AmlLabelTriggerModel
	AmlEmailTriggerModel    model.AmlEmailTriggerModel
	AmlAsyncTaskStatusModel model.AmlAsyncTaskStatusModel
	// 自建标签库映射关系
	VendorCategoryMappingModel model.VendorCategoryMappingModel
	EntityNameMappingModel     model.EntityNameMappingModel
	SfCaseModel                model.SfCaseModel
	AmlCaseModel               model.AmlCaseModel
	FiatAmlTransactionModel    model.FiatAmlTransactionModel

	// transaction 相关 model
	NormalTransactionModel model.NormalTransactionModel
	TokenTransactionModel  model.TokenTransactionModel
	SyncAddressLabelModel  model.SyncAddressLabelModel
	AdditionalKyaScanModel model.AdditionalKyaScanModel

	PreKyaHistoryModel model.PreKyaHistoryModel

	// bydata
	ByDataService *bydata.ByDataService

	// rpc
	KycInternalClient              platformkyc.KycInternalClient // bything
	FiatUserClient                 user.GrpcUserServiceClient
	QuoteClient                    quotev1.QuoteAPIClient
	AssetShowInternalClient        assetshow.AssetShowInternalClient
	CardManagerFacadeServiceClient card.CardManagerFacadeServiceClient
	MemberClient                   memberuser.MemberInternalClient
	BizConfigClient                bizconfigv1.CustomerAPIClient
	AppealAdminClient              appealv1.SecurityAppealAdminAPIClient

	// xxjob
	TxDataService *txdata.TxDataService

	//
	StoreSechubEncryptKey string

	// oklink client
	OklinkClient oklink.TxGetter

	// http client
	ThorNodeHttpClient *rpc.Client

	// Kafka Producter
	// kafka
	FiatProdProducer kafka.Producer // fiat 的 kafka producer

	PhotoSharingKey string // photo sharing key
}

func NewServiceContext(c config.Config) *ServiceContext {
	sechubKey, err := getStoreSechubEncryptKey(c.StoreSecConfig)
	if err != nil {
		panic("getStoreSechubEncryptKey failed" + err.Error())
	}

	hackTxClient := txdata.NewClient(c.HackTxConfig)
	byDataService := bydata.NewByDataService(c.ByData)
	txDataService := txdata.NewTxDataService(byDataService, hackTxClient)

	return &ServiceContext{
		Config:   c,
		BizRedis: c.BizRedis.NewRedis(),
		// models
		AddressLabelModel:          model.MustNewAddressLabelModel(c.Mysql),
		AllAddressLabelModel:       model.MustNewAllAddressLabelModel(c.TiDBCfg),
		AddressLabelHistoryModel:   model.MustNewAddressLabelHistoryModel(c.Mysql),
		AddressLabelChangeLogModel: model.MustNewAddressLabelChangeLogModel(c.Mysql),
		AmlHitCategoryModel:        model.MustNewAmlHitCategoryModel(c.Mysql),
		AmlLabelTriggerModel:       model.MustNewAmlLabelTriggerModel(c.Mysql),
		AmlEmailTriggerModel:       model.MustNewAmlEmailTriggerModel(c.Mysql),
		VendorCategoryMappingModel: model.MustNewVendorCategoryMappingModel(c.Mysql),
		EntityNameMappingModel:     model.MustNewEntityNameMappingModel(c.Mysql, model.CacheConfig{}), // FIXME: 需要配置缓存
		AmlAsyncTaskStatusModel:    model.MustNewAmlAsyncTaskStatusModel(c.Mysql),
		PreKyaHistoryModel:         model.MustNewPreKyaHistoryModel(c.Mysql),
		SfCaseModel:                model.MustNewSfCaseModel(c.Mysql),
		AmlCaseModel:               model.MustNewAmlCaseModel(c.Mysql),
		FiatAmlTransactionModel:    model.MustNewFiatAmlTransactionModel(c.AMLInsightMysql),
		NormalTransactionModel:     model.MustNewNormalTransactionModel(c.AMLInsightMysql),
		TokenTransactionModel:      model.MustNewTokenTransactionModel(c.AMLInsightMysql),
		SyncAddressLabelModel:      model.MustNewSyncAddressLabelModel(c.SyncAddrLabelMysql),
		AdditionalKyaScanModel:     model.MustNewAdditionalKyaScanModel(c.Mysql),
		// bydata
		ByDataService: byDataService,

		HackTxModel: model.MustNewHackTxModel(c.AMLInsightMysql),

		KycInternalClient:              platformkyc.NewKycInternalClient(zrpc.MustNewClient(c.KycInternalClient).Conn()),
		FiatUserClient:                 user.NewGrpcUserServiceClient(zrpc.MustNewClient(c.FiatUserClient).Conn()),
		QuoteClient:                    quotev1.NewQuoteAPIClient(zrpc.MustNewClient(c.QuoteClient).Conn()),
		AssetShowInternalClient:        assetshow.NewAssetShowInternalClient(zrpc.MustNewClient(c.AssetShowInternalClient).Conn()),
		CardManagerFacadeServiceClient: card.NewCardManagerFacadeServiceClient(zrpc.MustNewClient(c.FiatCardManagerClient).Conn()),
		MemberClient:                   memberuser.NewMemberInternalClient(zrpc.MustNewClient(c.KycInternalClient).Conn()),
		BizConfigClient:                bizconfigv1.NewCustomerAPIClient(zrpc.MustNewClient(c.PayBizConfig).Conn()),
		AppealAdminClient:              appealv1.NewSecurityAppealAdminAPIClient(zrpc.MustNewClient(c.AppealAdminClient).Conn()),
		StoreSechubEncryptKey:          sechubKey,
		TxDataService:                  txDataService,
		// oklink client
		OklinkClient:       oklink.NewClient(c.OklinkCfg),
		ThorNodeHttpClient: rpc.NewClient(c.ThorNodeCfg),

		FiatProdProducer: kafka.MustNewProducer(c.FiatProdProducer),
		PhotoSharingKey:  c.PhotoSharingKey,
	}
}

func getStoreSechubEncryptKey(cfg client.Config) (string, error) {
	s := api.NewClient(&cfg)
	if s.Client == nil {
		return "", errors.New("transfer sechub client is nil")
	}

	return s.GetEncryptKey("aes")
}
