package interceptors

import (
	"context"
	"errors"
	"reflect"

	"code.bydev.io/frameworks/byone/core/logc"
	"google.golang.org/grpc"
)

func Interceptors(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	if info == nil || info.FullMethod == "" {
		logc.Errorw(ctx, "Interceptors", logc.Field("FullMethod", info.FullMethod))
		return nil, errors.New("获取UnaryServerInfo失败")
	}

	var reqValue = reflect.ValueOf(req)
	for reqValue.Kind() == reflect.Ptr {
		reqValue = reqValue.Elem()
	}
	if operatorNameValue := reqValue.FieldByName("OperatorName"); operatorNameValue.IsValid() &&
		operatorNameValue.Kind() == reflect.String && operatorNameValue.CanSet() {
		operatorName := GetAdminOperator(ctx)
		if operatorName == "" {
			logc.Warnw(ctx, "GetAdminOperatorEmpty")
		} else {
			operatorNameValue.SetString(operatorName)
		}
	}
	if editorValue := reqValue.FieldByName("Editor"); editorValue.IsValid() &&
		editorValue.Kind() == reflect.String && editorValue.CanSet() {
		editorName := GetAdminOperator(ctx)
		if editorName == "" {
			logc.Warnw(ctx, "GetAdminOperatorEmpty")
		} else {
			editorValue.SetString(editorName)
		}
	}
	return handler(ctx, req)
}
