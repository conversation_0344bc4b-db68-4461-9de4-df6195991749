package handler

import (
	"context"
	"fmt"
	"time"

	"aml-insight/internal/common"
	"aml-insight/internal/model"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"code.bydev.io/frameworks/byone/core/logc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type EntityNameMappingHandler struct {
	entityNameMappingModel model.EntityNameMappingModel
}

func NewEntityNameMappingHandler(m model.EntityNameMappingModel) *EntityNameMappingHandler {
	return &EntityNameMappingHandler{
		entityNameMappingModel: m,
	}
}

// GRPC Handler Methods
func (h *EntityNameMappingHandler) QueryEntityNameMapping(ctx context.Context, in *aml_insightv1.QueryEntityNameMappingRequest) (*aml_insightv1.QueryEntityNameMappingResponse, error) {
	resp := &aml_insightv1.QueryEntityNameMappingResponse{
		Code: int64(0),
		Msg:  "",
	}

	mappings, err := h.queryMappings(ctx, in.GetId(), in.GetKey(), in.GetLimit(), in.GetPage())
	if err != nil {
		logc.Errorw(ctx, "failed to query entity name mapping", logc.Field("error", err), logc.Field("req", in.String()))
		return nil, status.Error(codes.Internal, fmt.Sprintf("failed to query entity name mapping: %s", err.Error()))
	}

	resp.Result = h.convertToResponse(mappings, in.GetPage())

	return resp, nil
}

func (h *EntityNameMappingHandler) UpsertEntityNameMapping(ctx context.Context, in *aml_insightv1.UpsertEntityNameMappingRequest) (*aml_insightv1.UpsertEntityNameMappingResponse, error) {
	resp := &aml_insightv1.UpsertEntityNameMappingResponse{
		Code: int64(0),
		Msg:  "",
	}

	if err := h.validateUpsertRequest(in); err != nil {
		logc.Errorw(ctx, "failed to validate upsert request", logc.Field("error", err), logc.Field("req", in.String()))
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("failed to validate upsert request: %s", err.Error()))
	}

	entityNameMapping := h.convertUpsertToModel(in)

	if err := h.upsertMapping(ctx, entityNameMapping); err != nil {
		logc.Errorw(ctx, "failed to upsert entity name mapping", logc.Field("error", err), logc.Field("req", in.String()))
		return nil, status.Error(codes.Internal, fmt.Sprintf("failed to upsert entity name mapping: %s", err.Error()))
	}

	return resp, nil
}

func (h *EntityNameMappingHandler) ListEntityMappings(ctx context.Context, in *aml_insightv1.ListEntityMappingsRequest) (*aml_insightv1.ListEntityMappingsResponse, error) {
	resp := &aml_insightv1.ListEntityMappingsResponse{
		Code: int64(0),
		Msg:  "",
	}

	mappings, total, err := h.listMappings(ctx, in)
	if err != nil {
		logc.Errorw(ctx, "failed to list entity mappings", logc.Field("error", err), logc.Field("req", in.String()))
		return nil, status.Error(codes.Internal, fmt.Sprintf("failed to list entity mappings: %s", err.Error()))
	}

	resp.Result = h.convertToListResponse(mappings, in.GetPage(), total)

	return resp, nil
}

func (h *EntityNameMappingHandler) UpdateEntityMapping(ctx context.Context, in *aml_insightv1.UpdateEntityMappingRequest) (*aml_insightv1.UpdateEntityMappingResponse, error) {
	resp := &aml_insightv1.UpdateEntityMappingResponse{
		Code: int64(0),
		Msg:  "",
	}

	if err := h.updateMapping(ctx, in); err != nil {
		logc.Errorw(ctx, "failed to update entity mapping", logc.Field("error", err), logc.Field("req", in.String()))
		return nil, status.Error(codes.Internal, fmt.Sprintf("failed to update entity mapping: %s", err.Error()))
	}

	return resp, nil
}

func (h *EntityNameMappingHandler) BatchUpdateEntityMapping(ctx context.Context, in *aml_insightv1.BatchUpdateEntityMappingRequest) (*aml_insightv1.BatchUpdateEntityMappingResponse, error) {
	resp := &aml_insightv1.BatchUpdateEntityMappingResponse{
		Code: int64(0),
		Msg:  "",
	}

	if len(in.GetUpdates()) == 0 {
		logc.Errorw(ctx, "failed to batch update entity mapping", logc.Field("req", in.String()))
		return nil, status.Error(codes.InvalidArgument, "batch update_list cannot be empty")
	}

	results := h.batchUpdateMappings(ctx, in.GetUpdates())
	resp.Result.Results = results

	return resp, nil
}

func (h *EntityNameMappingHandler) InsertEntityMapping(ctx context.Context, in *aml_insightv1.InsertEntityMappingRequest) (*aml_insightv1.InsertEntityMappingResponse, error) {
	resp := &aml_insightv1.InsertEntityMappingResponse{
		Code: int64(0),
		Msg:  "",
	}

	if err := h.insertMapping(ctx, in); err != nil {
		logc.Errorw(ctx, "failed to insert entity mapping", logc.Field("error", err), logc.Field("req", in.String()))
		return nil, status.Error(codes.Internal, fmt.Sprintf("failed to insert entity mapping: %s", err.Error()))
	}

	return resp, nil
}

func (h *EntityNameMappingHandler) BatchInsertEntityMapping(ctx context.Context, in *aml_insightv1.BatchInsertEntityMappingRequest) (*aml_insightv1.BatchInsertEntityMappingResponse, error) {
	resp := &aml_insightv1.BatchInsertEntityMappingResponse{
		Code: int64(0),
		Msg:  "",
		Result: &aml_insightv1.BatchInsertEntityMappingResponse_Result{
			Results: []*aml_insightv1.EntityMappingInsertResult{},
		},
	}

	if len(in.GetInsertList()) == 0 {
		logc.Errorw(ctx, "failed to batch insert entity mapping", logc.Field("req", in.String()))
		return nil, status.Error(codes.InvalidArgument, "batch insert_list cannot be empty")
	}

	results := h.batchInsertMappings(ctx, in.GetInsertList())
	resp.Result.Results = results

	return resp, nil
}

// Business Logic Methods (previously in service)
func (h *EntityNameMappingHandler) queryMappings(ctx context.Context, id uint64, key string, limit, page int32) ([]*model.EntityNameMapping, error) {
	return h.entityNameMappingModel.QueryByEntityName(ctx, id, key, limit, page)
}

func (h *EntityNameMappingHandler) upsertMapping(ctx context.Context, entityNameMapping *model.EntityNameMapping) error {
	if len(entityNameMapping.EntityNameKey) == 0 || len(entityNameMapping.CategoryVal) == 0 {
		return common.ErrInvalidParams
	}

	if entityNameMapping.Valid != 0 && entityNameMapping.Valid != 1 {
		return common.ErrInvalidParams
	}

	if entityNameMapping.Id == 0 {
		_, err := h.entityNameMappingModel.Insert(ctx, entityNameMapping)
		return err
	}

	return h.entityNameMappingModel.Update(ctx, entityNameMapping)
}

func (h *EntityNameMappingHandler) updateMapping(ctx context.Context, req *aml_insightv1.UpdateEntityMappingRequest) error {
	if err := h.validateUpdateRequest(req); err != nil {
		return err
	}

	// First get the existing record to preserve fields that shouldn't be updated
	existingMapping, err := h.entityNameMappingModel.FindOne(ctx, req.GetId())
	if err != nil {
		return fmt.Errorf("entity mapping not found for id: %d", req.GetId())
	}

	// Update the existing record with new values
	entityMapping := &model.EntityNameMapping{
		Id:            req.GetId(),
		EntityNameKey: req.GetEntityNameKey(),
		EntityNameVal: req.GetEntityNameVal(),
		CategoryVal:   existingMapping.CategoryVal,
		Valid:         existingMapping.Valid,
		MappingType:   req.GetMappingType(),
		HasMapping:    model.HasMappingYes, // Set to 1 when updating
		Editor:        req.GetEditor(),
		Remark:        req.GetRemark(),
		UpdatedAt:     time.Now(),
	}

	return h.entityNameMappingModel.Update(ctx, entityMapping)
}

func (h *EntityNameMappingHandler) batchUpdateMappings(ctx context.Context, updates []*aml_insightv1.UpdateEntityMappingRequest) []*aml_insightv1.EntityMappingUpdateResult {
	results := make([]*aml_insightv1.EntityMappingUpdateResult, 0, len(updates))

	for _, update := range updates {
		result := &aml_insightv1.EntityMappingUpdateResult{
			EntityNameKey: update.GetEntityNameKey(),
			Success:       false,
		}

		if err := h.updateMapping(ctx, update); err != nil {
			result.ErrorMessage = err.Error()
		} else {
			result.Success = true
		}

		results = append(results, result)
	}

	return results
}

func (h *EntityNameMappingHandler) listMappings(ctx context.Context, req *aml_insightv1.ListEntityMappingsRequest) ([]*model.EntityNameMapping, int32, error) {
	var startTime, endTime *time.Time
	if req.GetCreatedAtStart() != nil {
		t := req.GetCreatedAtStart().AsTime()
		startTime = &t
	}
	if req.GetCreatedAtEnd() != nil {
		t := req.GetCreatedAtEnd().AsTime()
		endTime = &t
	}

	page := req.GetPage()
	if page == 0 {
		page = 1 // Default page is 1
	}

	limit := req.GetLimit()
	if limit == 0 {
		limit = 50 // Default limit is 50
	}

	mappings, total, err := h.entityNameMappingModel.ListWithFilters(
		ctx,
		startTime,
		endTime,
		req.GetMappingType(),
		req.GetEntityNameKey(),
		req.HasMapping,
		limit,
		page,
	)
	if err != nil {
		return nil, 0, err
	}

	return mappings, total, nil
}

func (h *EntityNameMappingHandler) insertMapping(ctx context.Context, req *aml_insightv1.InsertEntityMappingRequest) error {
	if err := h.validateInsertRequest(req); err != nil {
		return err
	}

	entityMapping := h.convertInsertToModel(req)
	_, err := h.entityNameMappingModel.Insert(ctx, entityMapping)
	return err
}

func (h *EntityNameMappingHandler) batchInsertMappings(ctx context.Context, insertList []*aml_insightv1.InsertEntityMappingRequest) []*aml_insightv1.EntityMappingInsertResult {
	results := make([]*aml_insightv1.EntityMappingInsertResult, 0, len(insertList))

	for _, insert := range insertList {
		result := &aml_insightv1.EntityMappingInsertResult{
			EntityNameKey: insert.GetEntityNameKey(),
			Success:       false,
		}

		if err := h.insertMapping(ctx, insert); err != nil {
			result.ErrorMessage = err.Error()
		} else {
			result.Success = true
		}

		results = append(results, result)
	}

	return results
}

// Validation Methods

func (h *EntityNameMappingHandler) validateUpsertRequest(req *aml_insightv1.UpsertEntityNameMappingRequest) error {
	if len(req.GetKey()) == 0 || len(req.GetVal()) == 0 {
		return common.ErrInvalidParams
	}

	if req.Valid != 0 && req.Valid != 1 {
		return common.ErrInvalidParams
	}

	return nil
}

func (h *EntityNameMappingHandler) validateCommonEntityMappingFields(entityNameKey, entityNameVal, mappingType, editor string) error {
	if len(entityNameKey) == 0 {
		return fmt.Errorf("entity_name_key is required")
	}
	if len(entityNameVal) == 0 {
		return fmt.Errorf("entity_name_val is required")
	}
	if len(mappingType) == 0 {
		return fmt.Errorf("mapping_type is required")
	}
	if mappingType != "entity" && mappingType != "source" {
		return fmt.Errorf("mapping_type must be 'entity' or 'source'")
	}
	return nil
}

func (h *EntityNameMappingHandler) validateUpdateRequest(req *aml_insightv1.UpdateEntityMappingRequest) error {
	if req.GetId() == 0 {
		return fmt.Errorf("id is required")
	}
	return h.validateCommonEntityMappingFields(req.GetEntityNameKey(), req.GetEntityNameVal(), req.GetMappingType(), req.GetEditor())
}

func (h *EntityNameMappingHandler) validateInsertRequest(req *aml_insightv1.InsertEntityMappingRequest) error {
	return h.validateCommonEntityMappingFields(req.GetEntityNameKey(), req.GetEntityNameVal(), req.GetMappingType(), req.GetEditor())
}

// Conversion Methods

func (h *EntityNameMappingHandler) convertUpsertToModel(req *aml_insightv1.UpsertEntityNameMappingRequest) *model.EntityNameMapping {
	return &model.EntityNameMapping{
		Id:            req.GetId(),
		EntityNameKey: req.GetKey(),
		CategoryVal:   req.GetVal(),
		Valid:         req.GetValid(),
		Editor:        req.GetEditor(),
		Remark:        req.GetRemark(),
	}
}

func (h *EntityNameMappingHandler) convertToResponse(mappings []*model.EntityNameMapping, page int32) *aml_insightv1.QueryEntityNameMappingResponse_Result {
	result := &aml_insightv1.QueryEntityNameMappingResponse_Result{
		List:  make([]*aml_insightv1.UpsertEntityNameMappingRequest, 0, len(mappings)),
		Page:  page,
		Total: 10000, // TODO: 实现真实的总数统计
	}

	for _, mapping := range mappings {
		result.List = append(result.List, &aml_insightv1.UpsertEntityNameMappingRequest{
			Id:        mapping.Id,
			Key:       mapping.EntityNameKey,
			Val:       mapping.CategoryVal,
			Valid:     mapping.Valid,
			Editor:    mapping.Editor,
			Remark:    mapping.Remark,
			CreatedAt: mapping.CreatedAt.String(),
			UpdatedAt: mapping.UpdatedAt.String(),
		})
	}

	return result
}

func (h *EntityNameMappingHandler) convertToListResponse(mappings []*model.EntityNameMapping, page, total int32) *aml_insightv1.ListEntityMappingsResponse_Result {
	result := &aml_insightv1.ListEntityMappingsResponse_Result{
		List:  make([]*aml_insightv1.EntityMapping, 0, len(mappings)),
		Page:  page,
		Total: total,
	}

	for _, mapping := range mappings {
		result.List = append(result.List, &aml_insightv1.EntityMapping{
			Id:            mapping.Id,
			EntityNameKey: mapping.EntityNameKey,
			EntityNameVal: mapping.EntityNameVal,
			HasMapping:    mapping.HasMapping,
			MappingType:   mapping.MappingType,
			CreatedAt:     timestamppb.New(mapping.CreatedAt),
			UpdatedAt:     timestamppb.New(mapping.UpdatedAt),
			Editor:        mapping.Editor,
		})
	}

	return result
}

func (h *EntityNameMappingHandler) convertInsertToModel(req *aml_insightv1.InsertEntityMappingRequest) *model.EntityNameMapping {
	return &model.EntityNameMapping{
		EntityNameKey: req.GetEntityNameKey(),
		EntityNameVal: req.GetEntityNameVal(),
		MappingType:   req.GetMappingType(),
		HasMapping:    req.GetHasMapping(),
		Editor:        req.GetEditor(),
		Remark:        req.GetRemark(),
	}
}
