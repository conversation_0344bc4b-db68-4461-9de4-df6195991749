package handler

import (
	"context"
	"testing"
	"time"

	mockmodel "aml-insight/internal/mock/model"
	"aml-insight/internal/model"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/mock/gomock"
)

func TestEntityNameMappingHandler_QueryEntityNameMapping(t *testing.T) {
	mockModel := mockmodel.NewMockEntityNameMappingModel(gomock.NewController(t))
	handler := NewEntityNameMappingHandler(mockModel)

	// Mock data
	mockMappings := []*model.EntityNameMapping{
		{
			Id:            1,
			EntityNameKey: "test_key",
			CategoryVal:   "test_category",
			Valid:         1,
			Editor:        "test_editor",
			Remark:        "test_remark",
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
	}

	mockModel.EXPECT().QueryByEntityName(gomock.Any(), uint64(1), "test_key", int32(10), int32(1)).Return(mockMappings, nil)

	// Test QueryEntityNameMapping method
	req := &aml_insightv1.QueryEntityNameMappingRequest{
		Id:    1,
		Key:   "test_key",
		Limit: 10,
		Page:  1,
	}

	result, err := handler.QueryEntityNameMapping(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, result.Result)
	assert.Len(t, result.Result.List, 1)
	assert.Equal(t, "test_key", result.Result.List[0].Key)
}

func TestEntityNameMappingHandler_UpsertEntityNameMapping_Insert(t *testing.T) {
	mockModel := mockmodel.NewMockEntityNameMappingModel(gomock.NewController(t))
	handler := NewEntityNameMappingHandler(mockModel)

	// Test inserting new record
	req := &aml_insightv1.UpsertEntityNameMappingRequest{
		Id:     0, // ID 0 means insert
		Key:    "new_key",
		Val:    "new_category",
		Valid:  1,
		Editor: "test_editor",
		Remark: "test_remark",
	}

	mockModel.EXPECT().Insert(gomock.Any(), mock.MatchedBy(func(mapping *model.EntityNameMapping) bool {
		return mapping.EntityNameKey == "new_key" && mapping.CategoryVal == "new_category"
	})).Return(nil, nil)

	result, err := handler.UpsertEntityNameMapping(context.Background(), req)

	assert.NoError(t, err)
	assert.Equal(t, int64(0), result.Code)
}

func TestEntityNameMappingHandler_UpsertEntityNameMapping_Update(t *testing.T) {
	mockModel := mockmodel.NewMockEntityNameMappingModel(gomock.NewController(t))
	handler := NewEntityNameMappingHandler(mockModel)

	// Test updating existing record
	req := &aml_insightv1.UpsertEntityNameMappingRequest{
		Id:     1, // ID not 0 means update
		Key:    "update_key",
		Val:    "update_category",
		Valid:  1,
		Editor: "test_editor",
		Remark: "test_remark",
	}

	mockModel.EXPECT().Update(gomock.Any(), mock.MatchedBy(func(mapping *model.EntityNameMapping) bool {
		return mapping.Id == 1 && mapping.EntityNameKey == "update_key"
	})).Return(nil)

	result, err := handler.UpsertEntityNameMapping(context.Background(), req)

	assert.NoError(t, err)
	assert.Equal(t, int64(0), result.Code)
}

func TestEntityNameMappingHandler_ValidateUpsertRequest(t *testing.T) {
	handler := &EntityNameMappingHandler{}

	tests := []struct {
		name    string
		req     *aml_insightv1.UpsertEntityNameMappingRequest
		wantErr bool
	}{
		{
			name: "valid request",
			req: &aml_insightv1.UpsertEntityNameMappingRequest{
				Key:   "test_key",
				Val:   "test_val",
				Valid: 1,
			},
			wantErr: false,
		},
		{
			name: "empty key",
			req: &aml_insightv1.UpsertEntityNameMappingRequest{
				Key:   "",
				Val:   "test_val",
				Valid: 1,
			},
			wantErr: true,
		},
		{
			name: "empty val",
			req: &aml_insightv1.UpsertEntityNameMappingRequest{
				Key:   "test_key",
				Val:   "",
				Valid: 1,
			},
			wantErr: true,
		},
		{
			name: "invalid valid value",
			req: &aml_insightv1.UpsertEntityNameMappingRequest{
				Key:   "test_key",
				Val:   "test_val",
				Valid: 2,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := handler.validateUpsertRequest(tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestEntityNameMappingHandler_UpdateEntityMapping(t *testing.T) {
	mockModel := mockmodel.NewMockEntityNameMappingModel(gomock.NewController(t))
	handler := NewEntityNameMappingHandler(mockModel)

	// Mock existing mapping
	existingMapping := &model.EntityNameMapping{
		Id:          1,
		CategoryVal: "existing_category",
		Valid:       1,
	}

	mockModel.EXPECT().FindOne(gomock.Any(), uint64(1)).Return(existingMapping, nil)
	mockModel.EXPECT().Update(gomock.Any(), mock.MatchedBy(func(mapping *model.EntityNameMapping) bool {
		return mapping.Id == 1 && mapping.EntityNameKey == "updated_key" && mapping.HasMapping == 1
	})).Return(nil)

	req := &aml_insightv1.UpdateEntityMappingRequest{
		Id:            1,
		EntityNameKey: "updated_key",
		EntityNameVal: "updated_val",
		MappingType:   "entity",
		Editor:        "test_editor",
		Remark:        "updated_remark",
	}

	result, err := handler.UpdateEntityMapping(context.Background(), req)

	assert.NoError(t, err)
	assert.Equal(t, int64(0), result.Code)
}

func TestEntityNameMappingHandler_InsertEntityMapping(t *testing.T) {
	mockModel := mockmodel.NewMockEntityNameMappingModel(gomock.NewController(t))
	handler := NewEntityNameMappingHandler(mockModel)

	mockModel.EXPECT().Insert(gomock.Any(), mock.MatchedBy(func(mapping *model.EntityNameMapping) bool {
		return mapping.EntityNameKey == "new_entity" && mapping.MappingType == "entity"
	})).Return(nil, nil)

	req := &aml_insightv1.InsertEntityMappingRequest{
		EntityNameKey: "new_entity",
		EntityNameVal: "new_value",
		MappingType:   "entity",
		HasMapping:    1,
		Editor:        "test_editor",
		Remark:        "test_remark",
	}

	result, err := handler.InsertEntityMapping(context.Background(), req)

	assert.NoError(t, err)
	assert.Equal(t, int64(0), result.Code)
}
