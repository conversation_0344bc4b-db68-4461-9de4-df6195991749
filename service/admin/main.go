package main

import (
	"aml-insight/service/admin/internal/xxljob"
	"flag"
	"fmt"

	"code.bydev.io/frameworks/byone/core/threading"

	"aml-insight/service/admin/internal/config"
	"aml-insight/service/admin/internal/interceptors"
	api "aml-insight/service/admin/internal/server/amlinsightadminapi"
	amlinsightstradminapiServer "aml-insight/service/admin/internal/server/amlinsightstradminapi"
	amlinsighttmradminapiServer "aml-insight/service/admin/internal/server/amlinsighttmradminapi"
	"aml-insight/service/admin/internal/svc"

	aml_insightv1 "code.bydev.io/cht/fiat/backend/bufgen.git/pkg/channel/aml_insight/v1"

	"code.bydev.io/frameworks/byone/core/conf"
	"code.bydev.io/frameworks/byone/zrpc"
	"google.golang.org/grpc"
)

var configFile = flag.String("f", "etc/admin.toml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)
	c.Must<PERSON>etUp()

	ctx := svc.NewServiceContext(c)
	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		aml_insightv1.RegisterAmlInsightAdminAPIServer(grpcServer, api.NewAmlInsightAdminAPIServer(ctx))
		aml_insightv1.RegisterAmlInsightTmrAdminAPIServer(grpcServer, amlinsighttmradminapiServer.NewAmlInsightTmrAdminAPIServer(ctx))
		aml_insightv1.RegisterAmlInsightStrAdminAPIServer(grpcServer, amlinsightstradminapiServer.NewAmlInsightStrAdminAPIServer(ctx))
	})
	s.AddUnaryInterceptors(interceptors.Interceptors)
	defer s.Stop()

	// 运行 zcron job
	job := xxljob.Init(ctx)
	defer job.Stop()
	threading.GoSafe(func() {
		job.Start()
	})

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
