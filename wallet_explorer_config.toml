# Wallet Explorer 配置
[WalletExplorerCfg]
# Lark 通知配置（WalletExplorerConfig 自有字段）
WebhookURL = "https://open.larksuite.com/open-apis/bot/v2/hook/8cd38dda-45ab-493e-8c20-fcaa9e00f522"
AtUsers = ["56c5f451", "8457f8c6"]

# 嵌入的 WalletExplorerCrawlConfig 字段
BaseURL = "http://www.walletexplorer.com/api/1"
MaxRetries = 60
RetryDelayMs = 1000          # 重试基础延迟时间（毫秒）
RetryMaxWaitTimeMs = 15000   # 重试最大等待时间（毫秒）
MaxConcurrency = 3           # 最大并发数
PageSize = 100               # 分页大小
CallerEmail = "<EMAIL>"  # API 调用者邮箱

# 嵌入的 bhttpclient.Config 字段
Timeout = "30s"
MaxIdleConns = 100
MaxIdleConnsPerHost = 10
IdleConnTimeout = "90s"
TLSHandshakeTimeout = "10s"
ExpectContinueTimeout = "1s"
