code:
    api:
        port: "8081"
    format: by_bit
    model:
        types_map:
            bigint:
                null_type: sql.NullInt64
                type: int64
                unsigned_type: uint64
            dec:
                null_type: decimal.NullDecimal
                pkg: github.com/shopspring/decimal
                type: decimal.Decimal
            decimal:
                null_type: decimal.NullDecimal
                pkg: github.com/shopspring/decimal
                type: decimal.Decimal
            double:
                null_type: sql.NullFloat64
                type: float64
            float:
                null_type: sql.NullFloat64
                type: float64
            float4:
                null_type: sql.NullFloat64
                type: float64
            float8:
                null_type: sql.NullFloat64
                type: float64
            int:
                null_type: sql.NullInt32
                type: int32
                unsigned_type: uint32
            int1:
                null_type: sql.NullInt16
                type: int16
                unsigned_type: uint16
            int2:
                null_type: sql.NullInt16
                type: int16
                unsigned_type: uint16
            int3:
                null_type: sql.NullInt16
                type: int16
                unsigned_type: uint16
            int4:
                null_type: sql.NullInt16
                type: int16
                unsigned_type: uint16
            int8:
                null_type: sql.NullInt16
                type: int16
                unsigned_type: uint16
            integer:
                null_type: sql.NullInt32
                type: int32
                unsigned_type: uint32
            mediumint:
                null_type: sql.NullInt32
                type: int32
                unsigned_type: uint32
            middleint:
                null_type: sql.NullInt32
                type: int32
                unsigned_type: uint32
            smallint:
                null_type: sql.NullInt16
                type: int16
                unsigned_type: uint16
            tinyint:
                null_type: sql.NullInt16
                type: int16
                unsigned_type: uint16
    rpc:
        git:
            pb_url: https://code.bydev.io/cht/fiat/backend/bufgen.git
            proto_url: https://code.bydev.io/cht/fiat/backend/bufmodule.git
        port: "9102"
        registry_server: nacos
template:
    local: /Users/<USER>/.byctl/0.0.16
