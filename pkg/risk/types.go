package risk

// ReadME: 带有 omitempty 是可选字段，否则必填。

type (
	// Request is the request struct for the security client.
	Request struct {
		RequestId    string `json:"requestId"`
		MemberId     uint64 `json:"memberId"`
		Chain        string `json:"chain"`
		Coin         string `json:"coin"`
		FromAddress  string `json:"fromAddress,optional"`
		ToAddress    string `json:"toAddress"`
		Tag          string `json:"tag,optional"`
		ActionType   string `json:"actionType,optional"` // deposit or withdraw
		Amount       string `json:"amount"`
		CreateTime   uint64 `json:"createTime,optional"`
		SpendSubType string `json:"spendSubType,optional"` // added @2023-09-18 清退区分需求
	}

	// Response is the response struct for the security client.
	Response struct {
		Conclusion      string `json:"conclusion"`
		SuggestedAction string `json:"suggestedAction"`
		Disposal        int64  `json:"disposal,optional"`
		Status          int    `json:"status,optional"`
	}
)
