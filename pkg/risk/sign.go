package risk

import (
	"bytes"
	"crypto"
	"crypto/ed25519"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"sort"
	"strings"
)

// ed25519Sign 这块我们和集团风控约定 hash 使用 SHA-256
//
//	{
//	   "FIAT2403": {
//	     "algorithm": "ed25519",
//	     "hash": "SHA-256",
//	     "public_key": "MCowBQYDK2VwAyEApVBrBGarPJkPmcPAT+9jFvdFrc0zjr6HHJAAYx1Vo44="
//	   }
//	}
type ed25519Sign struct {
	priKey ed25519.PrivateKey
	hash   crypto.Hash
}

func newEd25519SignSvc(privateKey string) (*ed25519Sign, error) {
	private, err := parseED25519PrivateKeyPKCS8(privateKey)
	if err != nil {
		return nil, err
	}
	return &ed25519Sign{
		priKey: private,
		hash:   crypto.SHA256,
	}, nil
}

func (s *ed25519Sign) CalcSignature(body []byte) (string, error) {
	data, err := decoderBody(body)
	if err != nil {
		return "", err
	}
	signContent := getSignStr(data)
	if signContent == "" {
		return "", errors.New("sign str is empty or has not support value type")
	}

	h := s.hash.New()
	_, _ = h.Write([]byte(signContent))
	hashed := h.Sum(nil)

	signature := ed25519.Sign(s.priKey, hashed)
	return base64.StdEncoding.EncodeToString(signature), nil
}

func decoderBody(body []byte) (map[string]interface{}, error) {
	var out map[string]interface{}
	decoder := json.NewDecoder(bytes.NewReader(body))
	decoder.UseNumber()

	err := decoder.Decode(&out)
	return out, err
}

func parseED25519PrivateKeyPKCS8(privateKey string) (ed25519.PrivateKey, error) {
	block, _ := pem.Decode([]byte(privateKey))
	if block == nil {
		return nil, errors.New("decode ed25519 private key error")
	}
	priKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("%s: %w", "parse ed25519 key error", err)
	}

	edkey, ok := priKey.(ed25519.PrivateKey)
	if !ok {
		return nil, errors.New("parse ed25519 private key error")
	}

	return edkey, nil
}

func getSignStr(param map[string]interface{}) string {
	var keys []string
	var str string
	for k := range param {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	for _, key := range keys {
		val := param[key]
		switch val.(type) {
		case nil:
			val = "null"
			str += fmt.Sprintf("%s=%s&", key, val)
		case json.Number, string, bool:
			str += fmt.Sprintf("%s=%v&", key, val)
		case []interface{}:
			strList := make([]string, 0)
			for _, accessToken := range val.([]interface{}) {
				switch accessToken.(type) {
				case nil:
					strList = append(strList, "null")
				case json.Number, string, bool:
					strList = append(strList, fmt.Sprintf("%v", accessToken))
				default:
					childMap, ok := accessToken.(map[string]interface{})
					if !ok {
						return ""
					}
					childStr := getSignStr(childMap)
					strList = append(strList, fmt.Sprintf("(%s)", childStr))
				}
			}
			str += fmt.Sprintf("%s=%s&", key, strings.Join(strList, ","))
		default:
			childMap, ok := val.(map[string]interface{})
			if !ok {
				return ""
			}
			childStr := getSignStr(childMap)
			str += fmt.Sprintf("%s=(%s)&", key, childStr)
		}
	}
	return str[:len(str)-1]
}
