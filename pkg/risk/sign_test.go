package risk

import (
	"crypto/ed25519"
	"encoding/json"
	"testing"
)

const privateKey = `-----BEGIN PRIVATE KEY-----
	MC4CAQAwBQYDK2VwBCIEIFGczsQ/slWDvf+U2fq6is40a6NyfUe3aQbVO7pNXaTs
-----<PERSON><PERSON> PRIVATE KEY-----`

func TestNewEd25519SignSvc(t *testing.T) {
	type args struct {
		privateKey string
	}
	tests := []struct {
		name string
		args args
		//want    *ed25519Sign
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				privateKey: privateKey,
			},
			wantErr: false,
		},
		{
			name: "empty private key",
			args: args{
				privateKey: "",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := newEd25519SignSvc(tt.args.privateKey)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewEd25519SignSvc() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("NewEd25519SignSvc() got = %v, want %v", got, tt.want)
			//}
		})
	}
}

func Test_ed25519Sign_CalcSignature(t *testing.T) {
	type args struct {
		body []byte
	}
	tests := []struct {
		name       string
		privateKey string
		args       args
		want       string
		wantErr    bool
	}{
		{
			name:       "ok",
			privateKey: privateKey,
			args: args{
				body: []byte(`{"k1":"v1"}`),
			},
			wantErr: false,
			want:    "0jPD3aLNjuJWzpGcMO5norBWQeWib3JHEd+WJzRLk/x01J2cAI40Hk2f6XHQZb5dRgjx/qE3KXm3O7e/3d/WAw==",
		},
		{
			name:       "not json boyd",
			privateKey: privateKey,
			args: args{
				body: []byte(`{"k1":"`),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, _ := newEd25519SignSvc(tt.privateKey)
			got, err := s.CalcSignature(tt.args.body)
			if (err != nil) != tt.wantErr {
				t.Errorf("CalcSignature() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CalcSignature() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getSignStr(t *testing.T) {
	type args struct {
		param map[string]interface{}
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "ok",
			args: args{
				param: map[string]interface{}{
					"key1": "value1",
					"key2": json.Number("2"),
					"key3": []interface{}{"value3", "value4"},
					"key4": map[string]interface{}{
						"key4_key1": "key4_value1",
						"key4_key2": "key4_value2",
					},
					"key5": nil,
				},
			},
			want: "key1=value1&key2=2&key3=value3,value4&key4=(key4_key1=key4_value1&key4_key2=key4_value2)&key5=null",
		},
		{
			name: "json number",
			args: args{
				param: map[string]interface{}{
					"key2": 2,
				},
			},
			want: "",
		},
		{
			name: "json array",
			args: args{
				param: map[string]interface{}{
					"key3": []interface{}{1, 2, nil, new(Client), map[string]any{}},
				},
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getSignStr(tt.args.param); got != tt.want {
				t.Errorf("getSignStr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_parseED25519PrivateKeyPKCS8(t *testing.T) {
	type args struct {
		privateKey string
	}
	tests := []struct {
		name    string
		args    args
		want    ed25519.PrivateKey
		wantErr bool
	}{
		{
			name: "empty private key",
			args: args{
				privateKey: ``,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "invalid private key",
			args: args{
				privateKey: `-----BEGIN PRIVATE KEY-----
	invalid key
-----END PRIVATE KEY-----`,
			},
			wantErr: true,
		},
		{
			name: "ok private key",
			args: args{
				privateKey: `-----BEGIN PRIVATE KEY-----
	MC4CAQAwBQYDK2VwBCIEIFGczsQ/slWDvf+U2fq6is40a6NyfUe3aQbVO7pNXaTs
-----END PRIVATE KEY-----`,
			},
			wantErr: false,
		},
		{
			name: "rsa private key",
			args: args{
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := parseED25519PrivateKeyPKCS8(tt.args.privateKey)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseED25519PrivateKeyPKCS8() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("parseED25519PrivateKeyPKCS8() got = %v, want %v", got, tt.want)
			//}
		})
	}
}
