package risk

import (
	"crypto"
	"crypto/hmac"
	"encoding/base64"
	"time"
)

type hamcAuth struct {
	hash         crypto.Hash
	appId        string
	accessKey    string
	accessSecret string
}

type hmacAuthSignHeader struct {
	Method      string
	RawUrlPath  string
	RawUrlQuery string
	AccessKey   string
	AppId       string
	AppSign     string
	Version     int
	Now         time.Time
}

func newhamcAuth(appId, accessKey, accessSecret string) *hamcAuth {
	return &hamcAuth{
		hash:         crypto.SHA256,
		appId:        appId,
		accessKey:    accessKey,
		accessSecret: accessSecret,
	}
}

func (h *hamcAuth) CalcSignature(body []byte) string {
	hc := hmac.New(h.hash.New, []byte(h.accessSecret))
	hc.Write(body)
	signature := base64.StdEncoding.EncodeToString(hc.Sum(nil))
	return signature
}
