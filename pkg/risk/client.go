package risk

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	"time"

	"code.bydev.io/cht/fiat/backend/lib.git/pkg/client/bhttpclient"
	"code.bydev.io/frameworks/byone/core/breaker"
	"github.com/go-resty/resty/v2"
)

type (
	// Client is the interface for the security client. https://uponly.larksuite.com/wiki/GG33wczl2iT3Cek6E46ufQvVsff
	Client interface {
		SendReq(ctx context.Context, req *Request) (*Response, *resty.Response, error)
	}

	Config struct {
		bhttpclient.Config

		AppID     string
		AccessKey string
		SecretKey string

		PrivateKey string
		Breaker    bool `json:",default=false"`

		Path string `json:",default=/rag/aml/v1/kya/scan"`
	}

	apiClient struct {
		restyClient *bhttpclient.Client
		breaker     breaker.Breaker

		path string
	}
)

func MustNewClient(cfg Config) Client {
	c, err := NewClient(cfg)
	if err != nil {
		panic(err)
	}
	return c
}

func NewClient(cfg Config) (Client, error) {
	signer, err := newEd25519SignSvc(cfg.PrivateKey)
	if err != nil {
		return nil, err
	}
	hamcAuth := newhamcAuth(cfg.AppID, cfg.AccessKey, cfg.SecretKey)

	restyC := bhttpclient.New(cfg.Config)
	restyC.SetHeader("Content-Type", "application/json")
	restyC.SetHeader("Accept", "application/json")

	restyC.OnBeforeRequest(func(client *resty.Client, request *resty.Request) error {
		body, err := json.Marshal(request.Body)
		if err != nil {
			return err
		}

		// sign body
		signStr, err := signer.CalcSignature(body)
		if err != nil {
			return err
		}

		//
		now := time.Now()
		dateNow := now.UTC().Format(http.TimeFormat)
		timestamp := strconv.FormatInt(now.Unix(), 10)

		var headerElems []string
		headerElems = append(headerElems, request.Method, request.URL, "", /*RawUrlQuery  我们不需要 RawUrlQuery*/
			hamcAuth.accessKey, dateNow,
			"Date:"+dateNow, "app_id:"+hamcAuth.appId, "sign:"+signStr,
			"timestamp:"+timestamp, "version:"+strconv.Itoa(0))
		headerMessage := strings.Join(headerElems, "\n") + "\n"
		signHeader := hamcAuth.CalcSignature([]byte(headerMessage))
		_ = signHeader
		// signBody
		signBody := hamcAuth.CalcSignature(body)
		_ = signBody

		request.SetHeader("X-HMAC-SIGNATURE", signHeader)
		request.SetHeader("X-HMAC-DIGEST", signBody)
		request.SetHeader("X-HMAC-ALGORITHM", "hmac-sha256")
		request.SetHeader("X-HMAC-ACCESS-KEY", hamcAuth.accessKey)
		request.SetHeader("X-HMAC-SIGNED-HEADERS", "Date;app_id;sign;timestamp;version")
		request.SetHeader("Date", dateNow)
		if len(hamcAuth.appId) > 0 {
			request.SetHeader("app_id", hamcAuth.appId)
		}
		if len(signStr) > 0 {
			request.SetHeader("sign", signStr)
		}

		request.SetHeader("timestamp", timestamp)
		request.SetHeader("version", "0")
		return nil
	})

	brk := breaker.NewNoopBreaker()
	if cfg.Breaker {
		brk = breaker.GetBreaker("security://bybitsecurity-v2")
	}

	return &apiClient{
		restyClient: restyC,
		breaker:     brk,

		path: cfg.Path,
	}, nil
}

func (c *apiClient) SendReq(ctx context.Context, req *Request) (*Response, *resty.Response, error) {
	resp := &Response{}
	var r *resty.Response
	var err error
	err = c.breaker.Do(func() error {
		r, err = c.restyClient.R().
			SetContext(ctx).
			SetBody(req).
			SetResult(resp).
			Post(c.path)
		return err
	})
	if err != nil {
		return nil, nil, err
	}
	return resp, r, nil
}
