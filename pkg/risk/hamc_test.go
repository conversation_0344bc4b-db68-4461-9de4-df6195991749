package risk

import (
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNewHmacAuth(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		appId := "testAppId"
		accessKey := "testAccessKey"
		accessSecret := "testAccessSecret"
		h := newhamcAuth(appId, accessKey, accessSecret)

		// Test initialization
		assert.NotNil(t, h)
		assert.Equal(t, appId, h.appId)
		assert.Equal(t, accessKey, h.accessKey)
		assert.Equal(t, accessSecret, h.accessSecret)
	})
}

func TestHmacAuth_CalcSignature(t *testing.T) {
	// Example data for testing the signature calculation
	appId := "testAppId"
	accessKey := "testAccessKey"
	accessSecret := "testAccessSecret"
	h := newhamcAuth(appId, accessKey, accessSecret)

	t.Run("empty_body", func(t *testing.T) {
		// An empty body
		body := []byte("")

		// Calculate the signature
		signature := h.CalcSignature(body)

		// Check that signature is calculated even for empty input
		assert.NotEmpty(t, signature)
	})

	t.Run("different_body", func(t *testing.T) {
		// Different body for generating a different signature
		body := []byte("differentTestBody")

		// Calculate the signature
		signature := h.CalcSignature(body)

		// Check that signature is different for a different body
		assert.NotEmpty(t, signature)
		assert.NotEqual(t, signature, "V3rv5O3UwHRRWeM4XYwyOH36oPMvblv1E8H9Bgu5gyA=") // Example, it should be different
	})
}

func TestHmacAuth_SignatureWithCurrentTime(t *testing.T) {
	appId := "testAppId"
	accessKey := "testAccessKey"
	accessSecret := "testAccessSecret"
	h := newhamcAuth(appId, accessKey, accessSecret)

	t.Run("success", func(t *testing.T) {
		// A sample request header data
		header := hmacAuthSignHeader{
			Method:      "GET",
			RawUrlPath:  "/api/v1/resource",
			RawUrlQuery: "param=value",
			AccessKey:   accessKey,
			AppId:       appId,
			AppSign:     "", // Empty because we will calculate it
			Version:     1,
			Now:         time.Now(),
		}

		// Signature based on the header
		body := []byte(header.RawUrlPath + header.RawUrlQuery + header.Method + header.AccessKey + header.AppId + strconv.Itoa(header.Version))

		// Calculate the signature
		signature := h.CalcSignature(body)

		// Verify that the generated signature is correct
		assert.NotEmpty(t, signature)
	})
}
