package cache

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"code.bydev.io/frameworks/byone/core/collection"
	"code.bydev.io/frameworks/byone/core/mathx"
)

var ErrNotFound = errors.New("cache: key not found")

type CacheOption = collection.CacheOption

const expiryDeviation = 0.05

type LocalCache struct {
	*collection.Cache
	unstableExpiry mathx.Unstable
	expire         time.Duration
}

func NewLocalCache(expire time.Duration, opts ...CacheOption) (*LocalCache, error) {
	cache, err := collection.NewCache(expire, opts...)
	if err != nil {
		return nil, err
	}
	return &LocalCache{
		Cache:          cache,
		expire:         expire,
		unstableExpiry: mathx.NewUnstable(expiryDeviation),
	}, nil
}

func (lc *LocalCache) DelCtx(ctx context.Context, keys ...string) error {
	for _, key := range keys {
		lc.Del(ctx, key)
	}
	return nil
}

func (lc *LocalCache) GetCtx(ctx context.Context, key string, val any) error {
	data, ok := lc.Get(ctx, key)
	if !ok {
		return ErrNotFound
	}

	jsonData, ok := data.([]byte)
	if !ok {
		return ErrNotFound
	}

	return json.Unmarshal(jsonData, val)
}

func (lc *LocalCache) IsNotFound(err error) bool {
	return errors.Is(err, ErrNotFound)
}

func (lc *LocalCache) SetCtx(ctx context.Context, key string, val any) error {
	data, err := json.Marshal(val)
	if err != nil {
		return err
	}
	lc.Set(ctx, key, data)
	return nil
}

func (lc *LocalCache) SetWithExpireCtx(ctx context.Context, key string, val any, expire time.Duration) error {
	data, err := json.Marshal(val)
	if err != nil {
		return err
	}
	lc.SetWithExpire(ctx, key, data, expire)
	return nil
}

func (lc *LocalCache) TakeCtx(ctx context.Context, val any, key string, query func(val any) error) error {
	if err := lc.GetCtx(ctx, key, val); err == nil {
		return nil
	} else if !lc.IsNotFound(err) {
		return err
	}

	if err := query(val); err != nil {
		return err
	}

	_ = lc.SetCtx(ctx, key, val)
	return nil
}

func (lc *LocalCache) TakeWithExpireCtx(ctx context.Context, val any, key string,
	query func(val any, expire time.Duration) error) error {
	if err := lc.GetCtx(ctx, key, val); err == nil {
		return nil
	} else if !lc.IsNotFound(err) {
		return err
	}

	expiry := lc.aroundDuration(lc.expire)
	if err := query(val, expiry); err != nil {
		return err
	}

	_ = lc.SetWithExpireCtx(ctx, key, val, expiry)
	return nil
}

func (lc LocalCache) aroundDuration(duration time.Duration) time.Duration {
	return lc.unstableExpiry.AroundDuration(duration)
}
