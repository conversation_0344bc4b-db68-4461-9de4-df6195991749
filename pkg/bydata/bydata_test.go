package bydata

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestByData(t *testing.T) {
	mockHttpServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.<PERSON><PERSON><PERSON>ead<PERSON>(http.StatusOK)
		w.Write([]byte(`{"status": "ok"}`))
	}))
	defer mockHttpServer.Close()
	ast := assert.New(t)
	s := NewByDataService(ByDataConf{
		Host: mockHttpServer.URL,
	})
	ast.NotNil(s, "ByDataService should not be nil")

	data, err, ti := s.byDataPost(&BydataReqParams{
		Url: mockHttpServer.URL,
	})
	ast.Nil(err, "Error should be nil")
	ast.NotNil(data, "Data should not be nil")
	ast.NotNil(ti, "TimeInfo should not be nil")
}
