package bydata

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/csv"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"aml-insight/internal/model"

	byoneconf "code.bydev.io/frameworks/byone/core/conf"
	"code.bydev.io/frameworks/byone/core/contextutils"
	"code.bydev.io/frameworks/byone/core/logc"
	"code.bydev.io/frameworks/byone/core/logx"
	"code.bydev.io/frameworks/byone/rest/httpc"
	"github.com/gocarina/gocsv"
	"github.com/pkg/errors"
)

type ByDataConf struct {
	Host      string
	AppId     string
	AppSecret string
}

type ByDataService struct {
	Conf   ByDataConf
	client httpc.Service
}

type CreatedQueryHqlReq struct {
	Sql    string `json:"sql"`
	Engine string `json:"engine"`
	AppId  string `json:"appId"`
}

type CreateQueryHqlResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		QueryId string `json:"queryId"`
	}
}

type GetQueryHqlStateResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		QueryId  string `json:"queryId"`
		Status   string `json:"status"`
		Started  int64  `json:"started"`
		Ended    int64  `json:"ended"`
		ErrorMsg string `json:"errorMsg"`
	}
}

type BydataReqParams struct {
	Url       string
	Data      any
	Headers   map[string]string
	Timeout   int64
	Remark    string
	RequestId string
}

var letter = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")

func NewByDataService(conf ByDataConf) *ByDataService {
	var c httpc.Config
	_ = byoneconf.FillDefault(&c)
	client := httpc.NewServiceWithConfig(c)
	return &ByDataService{
		Conf:   conf,
		client: client,
	}
}

// 创建hql查询任务
func (s *ByDataService) CreateQueryHql(sql string) (string, error) {
	// 主动添加https前缀
	if !strings.HasPrefix(s.Conf.Host, "http") {
		s.Conf.Host = "http://" + s.Conf.Host
	}
	createQueryHql := s.Conf.Host + "/bydata-interface/api/query/dataQuery"
	req := &BydataReqParams{
		Url: createQueryHql,
		Data: CreatedQueryHqlReq{
			Sql:    sql,
			Engine: "spark",
			AppId:  s.Conf.AppId,
		},
	}
	respByte, err, _ := s.byDataPost(req)
	logx.Infow("CreateQueryHql 返回", logx.Field("data", string(respByte)))
	if err != nil {
		return "", err
	}
	resp := CreateQueryHqlResponse{}
	err = json.Unmarshal(respByte, &resp)
	if err != nil {
		return "", errors.Wrap(err, "CreateQueryHql error")
	}
	if resp.Code != http.StatusOK {
		return "", errors.New(resp.Message)
	}
	return resp.Data.QueryId, err
}

func (s *ByDataService) GetQueryHqlState(qid string) (string, error) {
	logx.Infow(fmt.Sprintf("开始获取任务%s状态", qid))
	// 主动添加https前缀
	if !strings.HasPrefix(s.Conf.Host, "http") {
		s.Conf.Host = "http://" + s.Conf.Host
	}
	createQueryHql := s.Conf.Host + "/bydata-interface/api/query/" + qid
	req := BydataReqParams{
		Url:     createQueryHql,
		Timeout: 5000,
	}
	respByte, err, _ := s.byDataGet(req)
	logx.Infow("GetQueryHqlState 返回", logx.Field("resp body", string(respByte)), logx.Field("request_id", qid))
	if err != nil {
		return "", err
	}
	resp := GetQueryHqlStateResponse{}
	err = json.Unmarshal(respByte, &resp)
	if err != nil {
		return "", errors.Wrap(err, "GetQueryHqlState error")
	}
	if resp.Code != http.StatusOK {
		return "", errors.New(resp.Message + "|" + resp.Data.ErrorMsg)
	}
	return resp.Data.Status, err
}

func (s *ByDataService) GetLabelResultStreamV2(qid string, result any) error {
	resB, err := s.getOriginRes(qid)
	if err != nil {
		logx.Infow("byDataGet Stream 错误", logx.Field("err", err), logx.Field("request_id", qid))
		return err
	}
	defer os.Remove(string(resB))

	clientsFile, err := os.OpenFile(string(resB), os.O_RDWR|os.O_CREATE, os.ModePerm)
	if err != nil {
		return fmt.Errorf("failed to open file %s", string(resB))
	}
	defer clientsFile.Close()

	if err := gocsv.UnmarshalFile(clientsFile, result); err != nil {
		return fmt.Errorf("failed to unmarshal file %s", string(resB))
	}
	return nil
}

// 获取label查询任务的结果
func (s *ByDataService) GetLabelResultStream(qid string) []*model.AddressLabel {
	resB, err := s.getOriginRes(qid)
	if err != nil {
		logx.Infow("byDataGet Stream 错误", logx.Field("err", err), logx.Field("request_id", qid))
		return []*model.AddressLabel{}
	}
	return csvResultToLabelStruct(string(resB), qid)
}

func csvResultToLabelStruct(filename, queryId string) []*model.AddressLabel {
	logx.Infow("开始转换为文件保存", logx.Field("request_id", queryId))
	queryResults := make([]*model.AddressLabel, 0)
	file, err := os.Open(filename)
	if err != nil {
		logx.Errorw(fmt.Sprintf("Open csv file %s error, queryId:%s", filename, queryId), logx.Field("err", err))
		return queryResults
	}
	defer file.Close()
	// 将结果字节流解析为对象
	cr := csv.NewReader(file)

	for {
		rec, err := cr.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			logx.Warnw(fmt.Sprintf("read csv stream error, queryId:%s", queryId), logx.Field("err", err))
			continue
		}
		if len(rec) == 0 {
			return queryResults
		}
		// 首行跳过 注意文件头部BOM字符
		if rec[0] == "chain" && rec[1] == "address" {
			continue
		}

		//chain,address,category,entity_name,detail_name,editor,source,remark,valid
		valid, err := strconv.ParseInt(rec[8], 10, 32)
		if err != nil {
			logx.Warnw("invalid address label data", logc.Field("valid", valid))
			continue
		}
		temp := &model.AddressLabel{
			Chain:      rec[0],
			Address:    rec[1],
			Category:   rec[2],
			EntityName: rec[3],
			DetailName: rec[4],
			Editor:     rec[5],
			Source:     rec[6],
			Remark:     rec[7],
			Valid:      int32(valid),
		}
		queryResults = append(queryResults, temp)
	}
	logx.Infow("转换完毕", logx.Field("request_id", queryId))
	// delete other csv files and only keep the latest one
	err = os.Remove(filename)
	if err != nil {
		logx.Warnw(fmt.Sprintf("remove csv file %s error", filename), logx.Field("err", err))
	}
	logx.Infow(fmt.Sprintf("删除临时文件%s完毕", filename), logx.Field("request_id", queryId))
	return queryResults
}

func (s *ByDataService) getOriginRes(qid string) ([]byte, error) {
	// 主动添加https前缀
	if !strings.HasPrefix(s.Conf.Host, "http") {
		s.Conf.Host = "http://" + s.Conf.Host
	}
	getResHql := s.Conf.Host + fmt.Sprintf("/bydata-interface/api/query/%s/result", qid)
	req := BydataReqParams{
		Url:       getResHql,
		Timeout:   5000,
		RequestId: qid,
	}
	respByte, err, _ := s.byDataGet(req)
	logx.Infow("GetQueryHqlResultStream 返回", logx.Field("data", string(respByte)), logx.Field("request_id", qid))
	return respByte, err
}

func (s *ByDataService) byDataPost(reqParams *BydataReqParams) ([]byte, error, *int64) {
	var timeCost int64 = 0
	startTime := time.Now().UnixMilli()
	// 记录结束时的耗时
	defer func(tt int64, tc *int64) {
		*tc = time.Now().UnixMilli() - tt
	}(startTime, &timeCost)
	dataByte, err := json.Marshal(reqParams.Data)
	if err != nil {
		return nil, err, &timeCost
	}
	// 准备http request
	// 会自动设置header.Host为url
	ctx := context.Background()
	ctx = contextutils.SetReferSiteId(ctx, contextutils.GetEnvSiteId())
	request, err := http.NewRequestWithContext(ctx, http.MethodPost, reqParams.Url, strings.NewReader(string(dataByte)))
	request.Header.Set("content-type", "application/json; charset=utf-8")
	signTime := fmt.Sprintf("%d", time.Now().Unix())
	request.Header.Set("x-de-timestamp", signTime)
	for k, v := range reqParams.Headers {
		// 跳过自定义 content-type
		if strings.ToLower(k) == "content-type" {
			continue
		}
		request.Header.Set(k, v)
	}
	// 获得signed headers
	sh := SignedHeaders(request)
	//sh := getSignedHeaders(request)
	// 获得 signature-canonicalRequest
	cr, err := CanonicalRequest(request)
	if err != nil {
		return nil, errors.Wrap(err, "CanonicalRequest error"), &timeCost
	}
	sts := GetStringToSign(cr, signTime)
	sig, err := hmacsha256([]byte(s.Conf.AppSecret), sts)
	if err != nil {
		return nil, errors.Wrap(err, "hmacsha256 error"), &timeCost
	}
	// 组成Authorization 放到 header中
	authorization := fmt.Sprintf("%s Access=%s, SignedHeaders=%s, Signature=%s", "DE1-HMAC-SHA256", s.Conf.AppId, sh, sig)
	request.Header.Set("Authorization", authorization)
	timeout := reqParams.Timeout
	if timeout == 0 {
		timeout = 3 * 1000
	}
	resp, err := s.client.DoRequest(request)
	if err != nil {
		return nil, err, &timeCost
	}
	defer resp.Body.Close()
	respBody, err := io.ReadAll(resp.Body)
	if resp.StatusCode >= 300 {
		err = errors.Wrap(err, fmt.Sprintf("return http code %d", resp.StatusCode))
	}
	return respBody, err, &timeCost
}

// TODO 放在本地文件会不会不太好
func (s *ByDataService) byDataGet(reqParams BydataReqParams) ([]byte, error, *int64) {
	var timeCost int64 = 0
	startTime := time.Now().UnixMilli()
	// 记录结束时的耗时
	defer func(tt int64, tc *int64) {
		*tc = time.Now().UnixMilli() - tt
	}(startTime, &timeCost)
	// 准备http request
	// 会自动设置header.Host为url
	signTime := fmt.Sprintf("%d", time.Now().Unix())
	ctx := context.Background()
	ctx = contextutils.SetReferSiteId(ctx, contextutils.GetEnvSiteId())
	r, err := http.NewRequestWithContext(ctx, "GET", reqParams.Url, nil)
	if err != nil {
		return nil, errors.Wrap(err, "new request error"), &timeCost
	}
	r.Header.Set("content-type", "application/json; charset=utf-8")
	r.Header.Set("x-de-timestamp", signTime)
	for k, v := range reqParams.Headers {
		// 跳过自定义 content-type
		if strings.ToLower(k) == "content-type" {
			continue
		}
		r.Header.Set(k, v)
	}
	// 获得signed headers
	sh := SignedHeaders(r)
	// 获得 signature-canonicalRequest
	cr, err := CanonicalRequest(r)
	if err != nil {
		return nil, errors.Wrap(err, "CanonicalRequest error"), &timeCost
	}
	sts := GetStringToSign(cr, signTime)
	sig, err := hmacsha256([]byte(s.Conf.AppSecret), sts)
	if err != nil {
		return nil, errors.Wrap(err, "hmacsha256 error"), &timeCost
	}
	// 组成Authorization 放到 header中
	authorization := fmt.Sprintf("%s Access=%s, SignedHeaders=%s, Signature=%s", "DE1-HMAC-SHA256", s.Conf.AppId, sh, sig)
	r.Header.Set("Authorization", authorization)
	// 请求发送
	timeout := reqParams.Timeout
	if timeout == 0 {
		timeout = 3 * 1000
	}
	resp, err := s.client.DoRequest(r)
	if err != nil {
		return nil, err, &timeCost
	}
	defer resp.Body.Close()
	if resp.StatusCode >= 300 {
		err = errors.Wrap(err, fmt.Sprintf("return http code %d", resp.StatusCode))
		return []byte{}, err, &timeCost
	}
	// 说明是附件形式
	if strings.HasPrefix(resp.Header.Get("Content-Disposition"), "attachment") {
		logx.Infow("byDataGet数据", logx.Field("data", resp.Body), logx.Field("request_id", reqParams.RequestId))
		// 拼凑文件名
		filename := strconv.FormatInt(time.Now().UnixMilli(), 10) + "_" + RandomString(5) + ".csv"
		file, err := os.Create(filename)
		if err != nil {
			return []byte{}, err, &timeCost
		}
		defer file.Close()
		_, err = io.Copy(file, resp.Body)
		if err != nil {
			return []byte{}, err, &timeCost
		}
		return []byte(filename), nil, &timeCost
	} else {
		// 普通结果
		respBody, err := io.ReadAll(resp.Body)
		return respBody, err, &timeCost
	}
}

func ExecuteSyncByDataQuery[T any](ctx context.Context, srv *ByDataService, qSql string,
	queryTimeout time.Duration, pollInterval time.Duration) ([]*T, error) {
	qid, err := srv.CreateQueryHql(qSql)
	if err != nil {
		return nil, err
	}

	if queryTimeout == 0 || pollInterval == 0 {
		return nil, errors.New("queryTimeout or pollInterval must be greater than 0")
	}

	// Poll for query completion with timeout
	timeout := time.After(queryTimeout)
	ticker := time.NewTicker(pollInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-timeout:
			logc.Errorw(ctx, "query timed out", logc.Field("qid", qid))
			return nil, errors.New("query timed out")
		case <-ticker.C:
			state, err := srv.GetQueryHqlState(qid)
			if err != nil {
				logc.Errorw(ctx, "failed to get query state", logc.Field("qid", qid), logc.Field("error", err))
				continue
			}

			if state == "succeeded" {
				// Query completed successfully, get results
				results := make([]*T, 0)
				err = srv.GetLabelResultStreamV2(qid, &results)
				if err != nil {
					logc.Errorw(ctx, "failed to get query result", logc.Field("qid", qid), logc.Field("error", err))
					return nil, err
				}
				logc.Infow(ctx, "success to query result", logc.Field("results_size", len(results)))
				return results, nil
			} else {
				logc.Infow(ctx, "query state", logc.Field("state", state))
			}
		}
	}
}

func SignedHeaders(r *http.Request) string {
	var a []string
	for key := range r.Header {
		a = append(a, strings.ToLower(key))
	}
	//a = append(a, HeaderHost)
	sort.Strings(a)
	return fmt.Sprintf("%s", strings.Join(a, ";"))
}

func CanonicalRequest(r *http.Request) (string, error) {
	data, err := RequestPayload(r)
	if err != nil {
		return "", err
	}
	var hexencode string
	if r.Method == "POST" {
		hexencode, err = HexEncodeSHA256Hash(data)
		hexencode = hexencode + "\n"
	} else {
		hexencode = ""
	}
	return fmt.Sprintf("%s\n%s\n%s\n%s\n%s\n%s",
		r.Method, CanonicalURI(r),
		CanonicalQueryString(r),
		CanonicalHeaders(r),
		SignedHeaders(r),
		hexencode,
	), err
}

func RequestPayload(r *http.Request) ([]byte, error) {
	if r.Body == nil {
		return []byte(""), nil
	}
	b, err := ioutil.ReadAll(r.Body)
	r.Body = ioutil.NopCloser(bytes.NewBuffer(b))
	return b, err
}

func HexEncodeSHA256Hash(body []byte) (string, error) {
	hash := sha256.New()
	if body == nil {
		body = []byte("")
	}
	_, err := hash.Write(body)
	return fmt.Sprintf("%x", hash.Sum(nil)), err
}

// CanonicalURI returns request uri
func CanonicalURI(r *http.Request) string {
	pattens := strings.Split(r.URL.Path, "/")
	var uri []string
	for _, v := range pattens {
		switch v {
		case "":
			continue
		case ".":
			continue
		case "..":
			if len(uri) > 0 {
				uri = uri[:len(uri)-1]
			}
		default:
			uri = append(uri, url.QueryEscape(v))
		}
	}
	urlpath := "/" + strings.Join(uri, "/")
	r.URL.Path = strings.Replace(urlpath, "+", "%20", -1)
	if ok := strings.HasSuffix(r.URL.Path, "/"); ok {
		return fmt.Sprintf("%s", r.URL.Path)
	}
	return fmt.Sprintf("%s", r.URL.Path)
}

func CanonicalQueryString(r *http.Request) string {
	var a []string
	for key, value := range r.URL.Query() {
		k := url.QueryEscape(key)
		for _, v := range value {
			var kv string
			if v == "" {
				kv = k
			} else {
				kv = fmt.Sprintf("%s=%s", k, url.QueryEscape(v))
			}
			a = append(a, strings.Replace(kv, "+", "%20", -1))
		}
	}
	sort.Strings(a)
	return fmt.Sprintf("%s", strings.Join(a, "&"))
}

func CanonicalHeaders(r *http.Request) string {
	var a []string
	for key, value := range r.Header {
		sort.Strings(value)
		var q []string
		for _, v := range value {
			q = append(q, trimString(v))
		}
		a = append(a, strings.ToLower(key)+":"+strings.Join(q, ","))
	}
	//a = append(a, HeaderHost+":"+r.Host)
	sort.Strings(a)
	return fmt.Sprintf("%s\n", strings.Join(a, "\n"))
}

func trimString(s string) string {
	var trimedString []byte
	inQuote := false
	var lastChar byte
	s = strings.TrimSpace(s)
	for _, v := range []byte(s) {
		if byte(v) == byte('"') {
			inQuote = !inQuote
		}
		if lastChar == byte(' ') && byte(v) == byte(' ') && !inQuote {
			continue
		}
		trimedString = append(trimedString, v)
		lastChar = v
	}
	return string(trimedString)
}

func GetStringToSign(cr, ts string) string {
	// HashedCanonicalRequest
	h := calculateHash(cr)
	return fmt.Sprintf("%s\n%s\n%s", "DE1-HMAC-SHA256", ts, h)
}

func hmacsha256(secret []byte, data string) (string, error) {
	h := hmac.New(sha256.New, secret)
	if _, err := h.Write([]byte(data)); err != nil {
		return "", err
	}
	return hex.EncodeToString(h.Sum(nil)), nil
}

func calculateHash(canonicalRequest string) string {
	hash := sha256.New()
	hash.Write([]byte(canonicalRequest))
	hashBytes := hash.Sum(nil)
	hashString := hex.EncodeToString(hashBytes)
	return hashString
}

func RandomString(n int) string {
	b := make([]rune, n)
	source := rand.NewSource(time.Now().UnixNano())
	rSource := rand.New(source)
	for i := range b {
		b[i] = letter[rSource.Intn(len(letter))]
	}
	return string(b)
}
