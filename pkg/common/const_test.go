package common

import (
	"testing"

	"aml-insight/internal/model"
)

func TestConvertAssetCaseStatus(t *testing.T) {
	type args struct {
		sfCase       *model.SfCase
		appealStatus string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "initial",
			args: args{
				sfCase: &model.SfCase{
					SfStatus: "initial",
				},
			},
			want: AMLCasePending,
		},
		{
			name: "failed",
			args: args{
				sfCase: &model.SfCase{
					SfStatus: "failed",
				},
			},
			want: AMLCaseStatusUnknown,
		},
		{
			name: "sent",
			args: args{
				sfCase: &model.SfCase{
					SfStatus: "sent",
				},
			},
			want: AMLNeedEdd,
		},
		{
			name: "Pending Trader Reply EDD",
			args: args{
				sfCase: &model.SfCase{
					SfStatus: "Pending Trader Reply EDD",
				},
			},
			want: AMLNeedEdd,
		},
		{
			name: "Pending Trader Reply EDD",
			args: args{
				sfCase: &model.SfCase{
					SfStatus: "Pending Trader Reply EDD",
				},
				appealStatus: "REJECTED",
			},
			want: AMLEddRFI,
		},
		{
			name: "Pending Trader Reply additional EDD",
			args: args{
				sfCase: &model.SfCase{
					SfStatus: "Pending Trader Reply additional EDD",
				},
			},
			want: AMLEddRFI,
		},
		{
			name: "Solved_6",
			args: args{
				sfCase: &model.SfCase{
					SfStatus: "Solved",
					AmlFlow:  6,
				},
			},
			want: AMLEddRefund,
		},
		{
			name: "Solved_6",
			args: args{
				sfCase: &model.SfCase{
					SfStatus: "Solved",
					AmlFlow:  0,
				},
			},
			want: AMLEddRefund,
		},
		{
			name: "Solved_7",
			args: args{
				sfCase: &model.SfCase{
					SfStatus: "Solved",
					AmlFlow:  7,
				},
			},
			want: AMLEddGuilty,
		},
		{
			name: "Solved_8",
			args: args{
				sfCase: &model.SfCase{
					SfStatus: "Solved",
					AmlFlow:  8,
				},
			},
			want: AMLEddRecharge,
		},
		{
			name: "default",
			args: args{
				sfCase: &model.SfCase{
					SfStatus: "default",
				},
			},
			want: AMLEddCommitted,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ConvertAssetCaseStatus(tt.args.sfCase, tt.args.appealStatus); got != tt.want {
				t.Errorf("ConvertAssetCaseStatus() = %v, want %v", got, tt.want)
			}
		})
	}
}
