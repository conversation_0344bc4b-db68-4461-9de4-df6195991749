package common

import (
	"math/rand"
	"strings"
	"time"
)

// GenerateHexString generates a random hexadecimal string of specified length
func GenerateHexString(length int) string {
	const hexChars = "0123456789ABCDEF"
	source := rand.NewSource(time.Now().UnixNano())
	rnd := rand.New(source)

	builder := strings.Builder{}
	builder.Grow(length)

	for i := 0; i < length; i++ {
		builder.WriteByte(hexChars[rnd.Intn(len(hexChars))])
	}

	return builder.String()
}

// GenerateRequestID generates a request ID with date prefix (YYYYMMDD) followed by random hex string
func GenerateRequestID(randomLength int) string {
	dateStr := time.Now().Format("20060102")
	randomHex := GenerateHexString(randomLength)
	return dateStr + randomHex
}

// GenerateDefaultRequestID generates a default format request ID (8-digit date + 22-digit random hex)
func GenerateDefaultRequestID() string {
	return GenerateRequestID(22)
}

// GenerateTimestampRequestID generates a request ID with timestamp prefix (YYYYMMDDHHMMSS) followed by random hex string
func GenerateTimestampRequestID(randomLength int) string {
	timestampStr := time.Now().Format("20060102150405")
	randomHex := GenerateHexString(randomLength)
	return timestampStr + randomHex
}
