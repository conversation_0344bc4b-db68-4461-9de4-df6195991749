package common

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUnmarshalMessage(t *testing.T) {
	t.Run("success_struct", func(t *testing.T) {
		// Setup
		type TestStruct struct {
			Name string `json:"name"`
			Age  int    `json:"age"`
		}

		jsonData := []byte(`{"name":"<PERSON>","age":30}`)

		// Execute
		result, err := UnmarshalMessage[TestStruct](jsonData)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "Alice", result.Name)
		assert.Equal(t, 30, result.Age)
	})

	t.Run("success_map", func(t *testing.T) {
		// Setup
		jsonData := []byte(`{"key1":"value1","key2":"value2"}`)

		// Execute
		result, err := UnmarshalMessage[map[string]string](jsonData)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		expected := map[string]string{"key1": "value1", "key2": "value2"}
		assert.Equal(t, expected, *result)
	})

	t.Run("error_empty_message", func(t *testing.T) {
		// Setup
		var jsonData []byte

		// Execute
		result, err := UnmarshalMessage[string](jsonData)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, ErrMessageEmpty, err)
		assert.Nil(t, result)
	})

	t.Run("error_nil_message", func(t *testing.T) {
		// Setup
		var jsonData []byte = nil

		// Execute
		result, err := UnmarshalMessage[string](jsonData)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, ErrMessageEmpty, err)
		assert.Nil(t, result)
	})

	t.Run("error_invalid_json", func(t *testing.T) {
		// Setup
		jsonData := []byte(`{"invalid":json}`)

		// Execute
		result, err := UnmarshalMessage[map[string]string](jsonData)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
	})

	t.Run("partial_json_struct", func(t *testing.T) {
		// Setup
		type TestStruct struct {
			Name string `json:"name"`
			Age  int    `json:"age"`
			City string `json:"city"`
		}

		// Missing 'city' field
		jsonData := []byte(`{"name":"Bob","age":25}`)

		// Execute
		result, err := UnmarshalMessage[TestStruct](jsonData)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "Bob", result.Name)
		assert.Equal(t, 25, result.Age)
		assert.Equal(t, "", result.City) // default value
	})

	t.Run("extra_fields_json", func(t *testing.T) {
		// Setup
		type TestStruct struct {
			Name string `json:"name"`
		}

		// Extra field 'age' not in struct
		jsonData := []byte(`{"name":"Charlie","age":35}`)

		// Execute
		result, err := UnmarshalMessage[TestStruct](jsonData)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "Charlie", result.Name)
	})
}
