package common

import (
	"strings"

	"aml-insight/internal/model"
)

const (
	AmlByDataSyncJobKey = "aml_bydata_job:address_label_sync" // 每小时同步自建标签库的任务

)

const (
	LogRequestId = "request_id" // requestId用于全链路追踪
	LogErr       = "err"        // 报错

)

const (
	DECISION_ATYPE_PASS       = "pass"
	DECISION_ATYPE_MANUAL     = "manual"
	DECISION_ATYPE_EDD        = "edd"
	DECISION_ATYPE_REJECT     = "reject"
	DECISION_ATYPE_REJECT_AML = "reject_aml"
	DECISION_ATYPE_PENDING    = "pending"
)

// AMLCaseStatus 和资产侧的映射状态 https://uponly.larksuite.com/wiki/LQgzwRCIZiqFeKkpN6quLpdpsbd
const (
	AMLCaseStatusUnknown = "AMLCaseStatusUnknown"
	AMLCasePending       = "AMLCasePending"
	AMLNeedEdd           = "AMLNeedEdd"
	AMLEddCommitted      = "AMLEddCommitted"
	AMLEddRFI            = "AMLEddRFI"
	AMLEddRefund         = "AMLEddRefund"
	AMLEddGuilty         = "AMLEddGuilty"
	AMLEddRecharge       = "AMLEddRecharge"
	AMLNeedSTR           = "AMLNeedSTR"
)

func ConvertAssetCaseStatus(sfCase *model.SfCase, appealStatus string) string {
	switch strings.TrimSpace(sfCase.SfStatus) {
	case "initial": // 刚刚初始化的状态
		return AMLCasePending
	case "failed":
		return AMLCaseStatusUnknown
	case "failed_outbound", "sent", "open":
		return AMLNeedEdd
	case "Pending Trader Reply EDD":
		if appealStatus == "REJECTED" { // OCR 驳回的时候，需要重新提交
			return AMLEddRFI
		} else {
			return AMLNeedEdd
		}
	case "Pending Trader Reply additional EDD":
		return AMLEddRFI
	case "Solved", // 新版本的统一为 solved
		"Approved", "Rejected": // 这块兼容旧版本的
		switch sfCase.AmlFlow {
		case 6:
			return AMLEddRefund
		case 7:
			return AMLEddGuilty
		case 8:
			return AMLEddRecharge
		default:
			return AMLEddRefund
		}

	default:
		// Pending OCR
		// Pending Compliance
		// Pending Legal Approval
		// No Response
		// Pending CS Reply
		return AMLEddCommitted
	}
}
