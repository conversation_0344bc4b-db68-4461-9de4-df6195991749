package common

import (
	"fmt"
	"regexp"
	"strings"
)

var (
	inputPattern = regexp.MustCompile(`^[a-zA-Z0-9_\.]+$`)
)

// SanitizeInput 校验并清理用户输入
func SanitizeInput(input string) error {
	if !inputPattern.MatchString(input) {
		return fmt.Errorf("invalid input: %s", input)
	}
	return nil
}

// SanitizeHiveSQL 清理可能导致SQL注入的Hive查询输入
// 此函数处理各种SQL注入技术，包括:
// - 引号转义
// - 分号移除/转义
// - 注释移除
// - 危险关键字过滤
// - 多行语句阻止
func SanitizeHiveSQL(input string) string {
	if input == "" {
		return ""
	}

	// 第1步: 转义单引号 (Hive中最常见的注入点)
	sanitized := strings.ReplaceAll(input, "'", "\\'")

	// 第2步: 转义双引号
	sanitized = strings.ReplaceAll(sanitized, "\"", "\\\"")

	// 第3步: 移除或转义分号以防止多语句执行
	sanitized = strings.ReplaceAll(sanitized, ";", "")

	// 第4步: 移除注释标记
	sanitized = removeComments(sanitized)

	// 第5步: 过滤危险的Hive关键字和函数
	sanitized = filterDangerousKeywords(sanitized)

	// 第6步: 移除多余的空格、换行符等
	sanitized = cleanWhitespace(sanitized)

	return sanitized
}

// 移除SQL注释
func removeComments(input string) string {
	// 移除单行注释 (-- 和 #)
	reInlineComment := regexp.MustCompile(`(--).*?(\n|$)`)
	noInlineComments := reInlineComment.ReplaceAllString(input, "")

	reHashComment := regexp.MustCompile(`(#).*?(\n|$)`)
	noHashComments := reHashComment.ReplaceAllString(noInlineComments, "")

	// 移除多行注释 /* ... */
	reMultilineComment := regexp.MustCompile(`/\*[\s\S]*?\*/`)
	return reMultilineComment.ReplaceAllString(noHashComments, "")
}

// 过滤危险关键字
func filterDangerousKeywords(input string) string {
	// 转为小写以便不区分大小写匹配
	lowerInput := strings.ToLower(input)

	// 危险关键字列表
	dangerousKeywords := []string{
		"insert", "update", "delete", "drop", "alter", "truncate",
		"create", "grant", "revoke", "union", "select.*from", "into outfile",
		"load_file", "exec", "execute", "set", "shutdown", "connect", "use",
		"add jar", "create temporary function", "set role", "set hivevar",
		"lateral view", "transform", "load data", "analyze",
	}

	// 检查输入是否包含任何危险关键字
	for _, keyword := range dangerousKeywords {
		re := regexp.MustCompile(`\b` + keyword + `\b`)
		if re.MatchString(lowerInput) {
			// 如果找到危险关键字，返回空字符串或替换为安全值
			// 这里我们选择返回一个明显无效的值，这样应用程序可以捕获和记录这些尝试
			return "INJECTION_ATTEMPT_BLOCKED"
		}
	}

	return input
}

// 清理多余空白字符
func cleanWhitespace(input string) string {
	// 将多个空白字符替换为单个空格
	re := regexp.MustCompile(`\s+`)
	return strings.TrimSpace(re.ReplaceAllString(input, " "))
}

// IsQuerySafe 检查完整查询是否安全
func IsQuerySafe(query string) bool {
	// 检查是否有多个语句（通过分号分隔）
	if strings.Contains(query, ";") {
		return false
	}

	// 检查常见的SQL注入模式
	injectionPatterns := []string{
		`'\s*OR\s*'.*?'\s*=\s*'.*?'`,         // 'OR '1'='1
		`'\s*OR\s*.*?=.*?`,                   // 'OR 1=1
		`'\s*OR\s*.*?<>.*?`,                  // 'OR 1<>2
		`--`,                                 // SQL注释
		`/\*.*?\*/`,                          // 多行注释
		`UNION\s+SELECT`,                     // UNION注入
		`;\s*SELECT`,                         // 多语句
		`CONCATENATE\(\s*.*?\s*,\s*.*?\s*\)`, // 字符串连接攻击
	}

	lowerQuery := strings.ToLower(query)
	for _, pattern := range injectionPatterns {
		re := regexp.MustCompile(`(?i)` + pattern)
		if re.MatchString(lowerQuery) {
			return false
		}
	}

	return true
}
