package common

import (
	"testing"
)

func TestSanitizeInput(t *testing.T) {
	// Test cases structure
	tests := []struct {
		name    string
		input   string
		wantErr bool
	}{
		// Valid inputs
		{
			name:    "Valid alphanumeric",
			input:   "abc123",
			wantErr: false,
		},
		{
			name:    "Valid with underscore",
			input:   "abc_123",
			wantErr: false,
		},
		{
			name:    "Valid with dot",
			input:   "abc.123",
			wantErr: false,
		},
		{
			name:    "Valid uppercase",
			input:   "ABC",
			wantErr: false,
		},
		{
			name:    "Valid numbers only",
			input:   "123",
			wantErr: false,
		},
		{
			name:    "Valid mix of allowed characters",
			input:   "a.b_c.1_2_3",
			wantErr: false,
		},

		// Invalid inputs
		{
			name:    "Invalid with hyphen",
			input:   "abc-123",
			wantErr: true,
		},
		{
			name:    "Invalid with special character",
			input:   "abc@123",
			wantErr: true,
		},
		{
			name:    "Invalid with space",
			input:   "abc 123",
			wantErr: true,
		},
		{
			name:    "Invalid with slash",
			input:   "abc/123",
			wantErr: true,
		},
		{
			name:    "Invalid with non-ASCII characters",
			input:   "你好",
			wantErr: true,
		},
		{
			name:    "Empty string",
			input:   "",
			wantErr: true,
		},
	}

	// Run all test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := SanitizeInput(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("SanitizeInput() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil && tt.wantErr {
				// Optionally verify the error message format
				expectedErrMsg := "invalid input: " + tt.input
				if err.Error() != expectedErrMsg {
					t.Errorf("SanitizeInput() error message = %v, want %v", err.Error(), expectedErrMsg)
				}
			}
		})
	}
}

func TestSanitizeHiveSQL(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "Simple string no sanitization needed",
			input:    "simple_column",
			expected: "simple_column",
		},
		{
			name:     "String with single quotes",
			input:    "column_name = 'value'",
			expected: "column_name = \\'value\\'",
		},
		{
			name:     "String with double quotes",
			input:    "column_name = \"value\"",
			expected: "column_name = \\\"value\\\"",
		},
		{
			name:     "String with semicolon",
			input:    "column_name = 'value'; DROP TABLE users;",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
		{
			name:     "String with inline comment",
			input:    "column_name = 'value' -- comment",
			expected: "column_name = \\'value\\'",
		},
		{
			name:     "String with hash comment",
			input:    "column_name = 'value' # comment",
			expected: "column_name = \\'value\\'",
		},
		{
			name:     "String with multiline comment",
			input:    "column_name = 'value' /* multiline comment */",
			expected: "column_name = \\'value\\'",
		},
		{
			name:     "String with dangerous keyword",
			input:    "column_name = 'value'; DROP TABLE users",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
		{
			name:     "String with excess whitespace",
			input:    "column_name   =   'value'",
			expected: "column_name = \\'value\\'",
		},
		{
			name:     "Complex injection attempt",
			input:    "column_name = 'value'; DROP TABLE users; -- comment",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SanitizeHiveSQL(tt.input)
			if result != tt.expected {
				t.Errorf("SanitizeHiveSQL() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestRemoveComments(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "No comments",
			input:    "SELECT * FROM table",
			expected: "SELECT * FROM table",
		},
		{
			name:     "Inline comment with --",
			input:    "SELECT * FROM table -- This is a comment",
			expected: "SELECT * FROM table ",
		},
		{
			name:     "Inline comment with # symbol",
			input:    "SELECT * FROM table # This is a comment",
			expected: "SELECT * FROM table ",
		},
		{
			name:     "Multiline comment",
			input:    "SELECT * FROM table /* This is a multiline comment */",
			expected: "SELECT * FROM table ",
		},
		{
			name:     "Multiline comment spanning multiple lines",
			input:    "SELECT * FROM table /* This is a \n multiline comment */",
			expected: "SELECT * FROM table ",
		},
		{
			name:     "Multiple comments of different types",
			input:    "SELECT * FROM table1 /* multiline */ JOIN table2 -- inline comment\n WHERE id = 1 # another comment",
			expected: "SELECT * FROM table1  JOIN table2  WHERE id = 1 ",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := removeComments(tt.input)
			if result != tt.expected {
				t.Errorf("removeComments() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestFilterDangerousKeywords(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "No dangerous keywords",
			input:    "SELECT * FROM table WHERE column = 'value'",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
		{
			name:     "With INSERT keyword",
			input:    "INSERT INTO table VALUES (1, 'value')",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
		{
			name:     "With UPDATE keyword",
			input:    "UPDATE table SET column = 'value'",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
		{
			name:     "With DELETE keyword",
			input:    "DELETE FROM table WHERE id = 1",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
		{
			name:     "With DROP keyword",
			input:    "DROP TABLE users",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
		{
			name:     "With ALTER keyword",
			input:    "ALTER TABLE users ADD COLUMN new_column",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
		{
			name:     "With TRUNCATE keyword",
			input:    "TRUNCATE TABLE users",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
		{
			name:     "With CREATE keyword",
			input:    "CREATE TABLE new_table (id INT)",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
		{
			name:     "With UNION keyword",
			input:    "SELECT * FROM table1 UNION SELECT * FROM table2",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
		{
			name:     "With mixed-case dangerous keyword",
			input:    "seLeCt * from users DrOp table admins",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
		{
			name:     "With keyword as part of a larger word should not trigger",
			input:    "SELECT dropdown_value FROM table",
			expected: "INJECTION_ATTEMPT_BLOCKED",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := filterDangerousKeywords(tt.input)
			if result != tt.expected {
				t.Errorf("filterDangerousKeywords() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCleanWhitespace(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "No excess whitespace",
			input:    "SELECT * FROM table",
			expected: "SELECT * FROM table",
		},
		{
			name:     "Multiple spaces",
			input:    "SELECT   *   FROM   table",
			expected: "SELECT * FROM table",
		},
		{
			name:     "Tabs",
			input:    "SELECT\t*\tFROM\ttable",
			expected: "SELECT * FROM table",
		},
		{
			name:     "Newlines",
			input:    "SELECT\n*\nFROM\ntable",
			expected: "SELECT * FROM table",
		},
		{
			name:     "Mixed whitespace",
			input:    "SELECT \t \n * \t \n FROM \t \n table",
			expected: "SELECT * FROM table",
		},
		{
			name:     "Leading and trailing whitespace",
			input:    "  \t\n  SELECT * FROM table  \t\n  ",
			expected: "SELECT * FROM table",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := cleanWhitespace(tt.input)
			if result != tt.expected {
				t.Errorf("cleanWhitespace() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestIsQuerySafe(t *testing.T) {
	tests := []struct {
		name     string
		query    string
		expected bool
	}{
		{
			name:     "Safe query",
			query:    "SELECT * FROM table WHERE column = 'value'",
			expected: true,
		},
		{
			name:     "Query with semicolon",
			query:    "SELECT * FROM table; SELECT * FROM another_table",
			expected: false,
		},
		{
			name:     "OR injection",
			query:    "SELECT * FROM table WHERE username = '' OR '1'='1'",
			expected: false,
		},
		{
			name:     "OR injection variant",
			query:    "SELECT * FROM table WHERE username = '' OR 1=1",
			expected: false,
		},
		{
			name:     "Comment injection",
			query:    "SELECT * FROM table WHERE username = 'admin'-- AND password = 'pass'",
			expected: false,
		},
		{
			name:     "Multiline comment injection",
			query:    "SELECT * FROM table WHERE username = 'admin'/* AND password = 'pass'*/",
			expected: false,
		},
		{
			name:     "UNION injection",
			query:    "SELECT * FROM table UNION SELECT password FROM users",
			expected: false,
		},
		{
			name:     "Multiple statement injection",
			query:    "SELECT * FROM table; SELECT password FROM users",
			expected: false,
		},
		{
			name:     "Complex safe query",
			query:    "SELECT t1.column1, t2.column2 FROM table1 t1 JOIN table2 t2 ON t1.id = t2.id WHERE t1.value BETWEEN 10 AND 20 ORDER BY t1.column1 DESC LIMIT 10",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsQuerySafe(tt.query)
			if result != tt.expected {
				t.Errorf("IsQuerySafe() = %v, want %v", result, tt.expected)
			}
		})
	}
}
