package common

import (
	"regexp"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestGenerateHexString(t *testing.T) {
	t.Run("length_validation", func(t *testing.T) {
		// Test various lengths
		lengths := []int{1, 5, 10, 16, 22, 32, 64}

		for _, length := range lengths {
			result := GenerateHexString(length)
			assert.Equal(t, length, len(result), "Expected length %d, got %d", length, len(result))
		}
	})

	t.Run("zero_length", func(t *testing.T) {
		result := GenerateHexString(0)
		assert.Equal(t, "", result)
	})

	t.Run("character_validation", func(t *testing.T) {
		result := GenerateHexString(100)

		// Check that all characters are valid hex characters (0-9, A-F)
		hexPattern := regexp.MustCompile("^[0-9A-F]+$")
		assert.True(t, hexPattern.MatchString(result), "Result should contain only hex characters: %s", result)
	})

	t.Run("uniqueness", func(t *testing.T) {
		// Generate multiple strings and check they're different
		results := make(map[string]bool)
		for i := 0; i < 100; i++ {
			result := GenerateHexString(16)
			assert.False(t, results[result], "Generated duplicate hex string: %s", result)
			results[result] = true
		}
	})
}

func TestGenerateRequestID(t *testing.T) {
	t.Run("date_format_validation", func(t *testing.T) {
		result := GenerateRequestID(10)

		// Should be date + random hex
		assert.True(t, len(result) >= 18, "RequestID should be at least 18 characters (8 date + 10 random)")

		// First 8 characters should be date in YYYYMMDD format
		dateStr := result[:8]
		datePattern := regexp.MustCompile("^[0-9]{8}$")
		assert.True(t, datePattern.MatchString(dateStr), "Date part should be 8 digits: %s", dateStr)

		// Parse the date to ensure it's valid
		_, err := time.Parse("20060102", dateStr)
		assert.NoError(t, err, "Date part should be valid date: %s", dateStr)

		// Remaining part should be hex
		hexPart := result[8:]
		hexPattern := regexp.MustCompile("^[0-9A-F]+$")
		assert.True(t, hexPattern.MatchString(hexPart), "Hex part should contain only hex characters: %s", hexPart)
		assert.Equal(t, 10, len(hexPart), "Hex part should be 10 characters")
	})

	t.Run("different_random_lengths", func(t *testing.T) {
		lengths := []int{1, 5, 15, 30}

		for _, length := range lengths {
			result := GenerateRequestID(length)
			expectedLength := 8 + length // 8 for date + random length
			assert.Equal(t, expectedLength, len(result), "Expected total length %d, got %d", expectedLength, len(result))
		}
	})

	t.Run("uniqueness", func(t *testing.T) {
		// Generate multiple IDs and check they're different
		results := make(map[string]bool)
		for i := 0; i < 50; i++ {
			result := GenerateRequestID(16)
			assert.False(t, results[result], "Generated duplicate request ID: %s", result)
			results[result] = true
		}
	})
}

func TestGenerateDefaultRequestID(t *testing.T) {
	t.Run("format_validation", func(t *testing.T) {
		result := GenerateDefaultRequestID()

		// Should be 30 characters total (8 date + 22 random)
		assert.Equal(t, 30, len(result), "Default request ID should be 30 characters")

		// First 8 characters should be date
		dateStr := result[:8]
		datePattern := regexp.MustCompile("^[0-9]{8}$")
		assert.True(t, datePattern.MatchString(dateStr), "Date part should be 8 digits: %s", dateStr)

		// Remaining 22 characters should be hex
		hexPart := result[8:]
		hexPattern := regexp.MustCompile("^[0-9A-F]+$")
		assert.True(t, hexPattern.MatchString(hexPart), "Hex part should contain only hex characters: %s", hexPart)
		assert.Equal(t, 22, len(hexPart), "Hex part should be 22 characters")
	})

	t.Run("uniqueness", func(t *testing.T) {
		// Generate multiple default IDs and check they're different
		results := make(map[string]bool)
		for i := 0; i < 50; i++ {
			result := GenerateDefaultRequestID()
			assert.False(t, results[result], "Generated duplicate default request ID: %s", result)
			results[result] = true
		}
	})

	t.Run("consistent_format", func(t *testing.T) {
		// Test multiple times to ensure consistent format
		for i := 0; i < 10; i++ {
			result := GenerateDefaultRequestID()

			// All should have same length
			assert.Equal(t, 30, len(result))

			// All should start with today's date
			today := time.Now().Format("20060102")
			assert.True(t, strings.HasPrefix(result, today), "Should start with today's date: %s", result)
		}
	})
}

func TestGenerateTimestampRequestID(t *testing.T) {
	t.Run("timestamp_format_validation", func(t *testing.T) {
		result := GenerateTimestampRequestID(8)

		// Should be timestamp + random hex
		assert.True(t, len(result) >= 22, "Timestamp RequestID should be at least 22 characters (14 timestamp + 8 random)")

		// First 14 characters should be timestamp in YYYYMMDDHHMMSS format
		timestampStr := result[:14]
		timestampPattern := regexp.MustCompile("^[0-9]{14}$")
		assert.True(t, timestampPattern.MatchString(timestampStr), "Timestamp part should be 14 digits: %s", timestampStr)

		// Parse the timestamp to ensure it's valid
		_, err := time.Parse("20060102150405", timestampStr)
		assert.NoError(t, err, "Timestamp part should be valid timestamp: %s", timestampStr)

		// Remaining part should be hex
		hexPart := result[14:]
		hexPattern := regexp.MustCompile("^[0-9A-F]+$")
		assert.True(t, hexPattern.MatchString(hexPart), "Hex part should contain only hex characters: %s", hexPart)
		assert.Equal(t, 8, len(hexPart), "Hex part should be 8 characters")
	})

	t.Run("different_random_lengths", func(t *testing.T) {
		lengths := []int{1, 5, 10, 20}

		for _, length := range lengths {
			result := GenerateTimestampRequestID(length)
			expectedLength := 14 + length // 14 for timestamp + random length
			assert.Equal(t, expectedLength, len(result), "Expected total length %d, got %d", expectedLength, len(result))
		}
	})

	t.Run("uniqueness", func(t *testing.T) {
		// Generate multiple timestamp IDs and check they're different
		results := make(map[string]bool)
		for i := 0; i < 50; i++ {
			result := GenerateTimestampRequestID(12)
			assert.False(t, results[result], "Generated duplicate timestamp request ID: %s", result)
			results[result] = true
		}
	})

	t.Run("timestamp_progression", func(t *testing.T) {
		// Generate IDs with small delay to check timestamp progression
		first := GenerateTimestampRequestID(6)
		time.Sleep(time.Millisecond * 10) // Small delay
		second := GenerateTimestampRequestID(6)

		firstTimestamp := first[:14]
		secondTimestamp := second[:14]

		// Second timestamp should be greater than or equal to first
		assert.True(t, secondTimestamp >= firstTimestamp,
			"Second timestamp (%s) should be >= first (%s)", secondTimestamp, firstTimestamp)
	})
}
