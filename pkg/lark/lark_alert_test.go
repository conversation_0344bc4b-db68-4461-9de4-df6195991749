package lark

import (
	"context"
	"net/http"
	"net/http/httptest"
	"net/http/httputil"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestByData(t *testing.T) {
	mockHttpServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		zz, _ := httputil.DumpRequest(r, true)
		t.Logf("Request: %s", zz)
		w.<PERSON><PERSON><PERSON><PERSON><PERSON>(http.StatusOK)
		w.Write([]byte(`{"status": "ok"}`))
	}))
	defer mockHttpServer.Close()
	ast := assert.New(t)
	s := NewLarkAlerter()

	ast.NotNil(s, "ByDataService should not be nil")

	err := s.SendLarkRichTextMsg(context.Background(), mockHttpServer.URL, LarkRichMsgBody{
		Title: "Test Alert",
		Text:  "This is a test alert message."})
	ast.Nil(err, "Error should be nil")
}

func TestIntegration(t *testing.T) {
	alert := NewLarkAlerter()
	webhookUrl := "https://open.larksuite.com/open-apis/bot/v2/hook/8cd38dda-45ab-493e-8c20-fcaa9e00f522"
	err := alert.SendLarkRichTextMsg(context.Background(), webhookUrl, LarkRichMsgBody{
		Title: "Test Alert",
		Text:  "This is a test alert message.",
		At:    []string{"rex.ni"}, // 需要向IT申请用户ID
	})
	assert.Nil(t, err, "Error should be nil")
}
