package lark

import (
	"context"
	"fmt"
	"net/http"

	byoneconf "code.bydev.io/frameworks/byone/core/conf"
	"code.bydev.io/frameworks/byone/rest/httpc"
)

type LarkMsgReq struct {
	MsgType string `json:"msg_type"`
	Content struct {
		Post struct {
			ZhCn struct {
				Title   string      `json:"title"`
				Content [][]Content `json:"content"`
			} `json:"zh_cn"`
		} `json:"post"`
	} `json:"content"`
}

type Content struct {
	Tag    string `json:"tag"`
	Text   string `json:"text,omitempty"`
	UserId string `json:"user_id,omitempty"`
}

type LarkRichMsgBody struct {
	Title string
	Text  string
	At    []string
}

type T struct {
	Code int      `json:"code"`
	Data struct{} `json:"data"`
	Msg  string   `json:"msg"`
}

type LarkAlerter struct {
	client httpc.Service
}

func NewLarkAlerter() *LarkAlerter {
	var c httpc.Config
	_ = byoneconf.FillDefault(&c)
	client := httpc.NewServiceWithConfig(c)
	return &LarkAlerter{
		client: client,
	}
}

func (l *LarkAlerter) SendLarkRichTextMsg(ctx context.Context, webhookUrl string, reqBody LarkRichMsgBody) error {
	req := LarkMsgReq{
		MsgType: "post",
	}
	req.Content.Post.ZhCn.Title = reqBody.Title
	req.Content.Post.ZhCn.Content = [][]Content{{}}

	req.Content.Post.ZhCn.Content[0] = append(req.Content.Post.ZhCn.Content[0], Content{
		Tag:  "text",
		Text: reqBody.Text,
	})
	for _, at := range reqBody.At {
		req.Content.Post.ZhCn.Content[0] = append(req.Content.Post.ZhCn.Content[0], Content{
			Tag:    "at",
			UserId: at,
		})
	}

	resp, err := l.client.Do(ctx, http.MethodPost, webhookUrl, req)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("send lark alert not ok, resp.Status:%s", resp.Status)
	}

	// TODO check resp body
	return nil
}
